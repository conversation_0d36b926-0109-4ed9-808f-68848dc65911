package utils.email;

import static utils.email.RecipientProps.RECIPIENTS_DELIMITER;

import java.io.IOException;
import java.util.List;
import models.Attachment;
import org.apache.commons.mail.*;
import utils.ApplicationConfiguration;
import utils.StringHelper;

public class EmailService {

    public enum AttachExtension {
        pdf,
        xls,
        xlsx,
        noOne,
    }

    public static void send(String to, String subject, String body)
        throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternal(recipientProps, subject, body, null, "", AttachExtension.noOne, null);
    }

    public static void sendAsync(String to, String subject, String body)
        throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternalAsync(recipientProps, subject, body, null, "", AttachExtension.noOne, null);
    }

    public static void sendAsync(String to, String cc, String subject, String body)
        throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, cc, null);
        sendInternalAsync(recipientProps, subject, body, null, "", AttachExtension.noOne, null);
    }

    public static void send(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension
    ) throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternal(recipientProps, subject, body, attach, attachName, extension, null);
    }

    public static void sendAsync(
        String to,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension
    ) throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternalAsync(recipientProps, subject, body, attach, attachName, extension, null);
    }

    public static void sendAsync(
        String to,
        String cc,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension
    ) throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, cc, null);
        sendInternalAsync(recipientProps, subject, body, attach, attachName, extension, null);
    }

    public static void send(String to, String subject, String body, List<Attachment> attachments)
        throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternal(recipientProps, subject, body, null, "", AttachExtension.noOne, attachments);
    }

    public static void send(
        String to,
        FromProps from,
        String subject,
        String body,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternal(
            recipientProps,
            from,
            subject,
            body,
            null,
            "",
            AttachExtension.noOne,
            attachments
        );
    }

    public static void sendAsync(
        String to,
        String subject,
        String body,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        RecipientProps recipientProps = new RecipientProps(to, null, null);
        sendInternalAsync(
            recipientProps,
            subject,
            body,
            null,
            "",
            AttachExtension.noOne,
            attachments
        );
    }

    public static String doReplaces(String body) {
        return body
            .replace("plusIcon ", " .plusIcon")
            .replace("lessIcon ", " .lessIcon")
            .replace("isexpanded ", "#isexpanded")
            .replace(" checked ", ":checked~* ")
            .replace(" expandable ", ".expandable")
            .replace(" }}", " }")
            .replace(" ${{", " {");
    }

    /**
     * Get lis of recipients delimited by internal delimiter
     */
    public static String generateRecipientsList(List<String> recipients) {
        return String.join(RECIPIENTS_DELIMITER, recipients);
    }

    public static String generateRecipientsList(String... recipients) {
        return String.join(RECIPIENTS_DELIMITER, recipients);
    }

    private static void sendInternalAsync(
        RecipientProps recipientProps,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) {
        new Thread(
            (
                new Runnable() {
                    RecipientProps _recipientProps;
                    String _subject;
                    String _body;
                    byte[] _attach;
                    String _attachName;
                    AttachExtension _extension;
                    List<Attachment> _attachments;

                    public Runnable init(
                        RecipientProps recipientProps,
                        String subject,
                        String body,
                        byte[] attach,
                        String attachName,
                        AttachExtension extension,
                        List<Attachment> attachments
                    ) {
                        this._recipientProps = recipientProps;
                        this._subject = subject;
                        this._body = body;
                        this._attach = attach;
                        this._attachName = attachName;
                        this._extension = extension;
                        this._attachments = attachments;

                        return this;
                    }

                    public void run() {
                        try {
                            sendInternal(
                                this._recipientProps,
                                this._subject,
                                this._body,
                                this._attach,
                                this._attachName,
                                this._extension,
                                this._attachments
                            );
                        } catch (EmailException | IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            ).init(recipientProps, subject, body, attach, attachName, extension, attachments)
        )
            .start();
    }

    private static void sendInternal(
        RecipientProps recipientProps,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        sendInternal(
            recipientProps,
            new LavomatFromProps(),
            subject,
            body,
            attach,
            attachName,
            extension,
            attachments
        );
    }

    private static void sendInternal(
        RecipientProps recipientProps,
        FromProps fromProps,
        String subject,
        String body,
        byte[] attach,
        String attachName,
        AttachExtension extension,
        List<Attachment> attachments
    ) throws EmailException, IOException {
        body = doReplaces(body);

        Boolean isProduction = ApplicationConfiguration.isProd();
        HtmlEmail email = new HtmlEmail();

        if (isProduction) {
            String[] tos = recipientProps.getTo();
            for (String recipient : tos) {
                email.addTo(recipient);
            }
            if (recipientProps.hasCc()) {
                String[] ccs = recipientProps.getCc();
                for (String recipient : ccs) {
                    email.addCc(recipient);
                }
            }
            if (recipientProps.hasBcc()) {
                String[] bccs = recipientProps.getBcc();
                for (String recipient : bccs) {
                    email.addBcc(recipient);
                }
            }
        } else {
            String debugEmail = ApplicationConfiguration.getDebugEmail();
            if (!StringHelper.isBlank(debugEmail)) {
                email.addTo(debugEmail);
            } else {
                return;
            }
        }

        email.setSmtpPort(ApplicationConfiguration.getSMTPPort());
        email.setAuthenticator(
            new DefaultAuthenticator(
                ApplicationConfiguration.getSMTPUser(),
                ApplicationConfiguration.getSMTPPassword()
            )
        );
        email.setDebug(ApplicationConfiguration.isDebugEmailEnabled());
        email.setHostName(ApplicationConfiguration.getEmailHostname());
        email.setTLS(true);
        email.setSSL(true);
        email.setFrom(fromProps.EMAIL, fromProps.NAME);
        email.setHtmlMsg(body);

        String tag = isProduction ? "" : "[PRUEBA] ";
        email.setSubject(tag + subject);

        if (attach != null && attach.length > 0) {
            email.attach(
                new ByteArrayDataSource(attach, "application/" + extension),
                attachName + "." + extension,
                attachName,
                EmailAttachment.ATTACHMENT
            );
        }

        if (attachments != null) {
            for (Attachment attachment : attachments) {
                if (attachment.getPayload() != null) {
                    email.attach(
                        new ByteArrayDataSource(attachment.getPayload(), attachment.getMIMEType()),
                        attachment.getName() + "." + attachment.getExtension(),
                        attachment.getName(),
                        EmailAttachment.ATTACHMENT
                    );
                }
            }
        }

        try {
            email.send();
        } catch (EmailException e) {
            play.Logger.error("Error sending email: " + e.getMessage(), e);
            throw e;
        }
    }
}
