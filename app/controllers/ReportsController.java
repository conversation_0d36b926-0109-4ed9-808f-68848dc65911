package controllers;

import static java.util.stream.Collectors.groupingBy;

import com.play4jpa.jpa.db.Tx;
import domains.sale_notifier.services.SaleNotifierService;
import domains.sale_notifier.services.wrappers.SaleWrapper;
import dto.report.UsesReportParameters;
import global.APIException;
import global.APIException.APIErrors;
import global.BackOfficeCop;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import models.*;
import models.Audit.ResultCode;
import models.Card.ContractType;
import models.Currency;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.json.JSONException;
import org.json.JSONObject;
import play.db.jpa.JPA;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import policies.ApiClient;
import policies.actions.Policy;
import policies.machine_uses.MachineUsePolicy;
import queries.cards.CardEventQuery;
import queries.machine_uses.MachineUseCustomQuery;
import queries.machine_uses.MachineUseQuery;
import queries.machines.MachineWithOldestKeepAliveQuery;
import scala.Int;
import security.v1.Secured;
import serializers.MachineUseSerializer;
import serializers.ReportItemSerializer;
import services.ReportService;
import utils.DateHelper;

@ErrorMessage
@global.LoggingMessage(auth = true, headers = true)
@Security.Authenticated(Secured.class)
@Policy(MachineUsePolicy.class)
public class ReportsController extends AbstractController {

    private HashMap<String, List<? extends BillableItem>> getReportData() throws APIException {
        return getReportData(false);
    }

    private HashMap<String, List<? extends BillableItem>> getReportData(
        boolean includeDisabledUses
    ) throws APIException {
        HashMap<String, List<? extends BillableItem>> result = new HashMap<>();

        Date from = null;
        Date to = null;
        int machineId = -1;
        int buildingId = -1;
        int unitId = -1;
        int accountId = -1;
        String keyword = null;
        String cardUId = null;
        boolean unitFound = false;

        String accountIdString = request().getQueryString("account");
        if (accountIdString != null) {
            accountId = Integer.parseInt(accountIdString);
            Account account = Account.findById(accountId);

            for (Building building : Building.findAll()) {
                for (Unit unit : building.getUnits()) {
                    if (unit.getOwner() != null && unit.getOwner().equals(account.getOwner())) {
                        buildingId = building.getId();
                        unitId = unit.getId();
                        unitFound = true;
                    }
                }
            }
        } else {
            unitFound = true;

            String machineIdString = request().getQueryString("machine");
            if (machineIdString != null) {
                machineId = Integer.parseInt(machineIdString);
            }

            String buildingIdString = request().getQueryString("building");
            if (buildingIdString != null) {
                buildingId = Integer.parseInt(buildingIdString);
            }

            String unitIdString = request().getQueryString("unit");
            if (unitIdString != null) {
                unitId = Integer.parseInt(unitIdString);
            }

            keyword = request().getQueryString("keyword");
            cardUId = request().getQueryString("card");
        }

        if (!unitFound) {
            return new HashMap<>();
        }

        boolean queryOnlyCardEvents = safeBoolean("onlyCardEvents", request().queryString());

        List<MachineUse> uses;
        if (queryOnlyCardEvents) {
            uses = new ArrayList<MachineUse>();
        } else {
            uses = MachineUse.find(from, to, buildingId, machineId, unitId, cardUId, keyword);
        }

        List<CardEvent> cards = CardEvent.find(
            from,
            to,
            buildingId,
            machineId,
            unitId,
            cardUId,
            keyword,
            CardEventType.CARD_ASSIGNED_BILLABLE
        );

        result.put("uses", sortBillableItem(uses));
        result.put("cards", sortBillableItem(cards));

        return result;
    }

    @Tx(readOnly = true)
    public Promise<Result> findUses() throws APIException {
        MachineUsePolicy allowedPolicy = this.getAllowedPolicy();
        MachineUseQuery machineUseQuery = allowedPolicy.list();
        UsesReportParameters dto = new UsesReportParameters(queryString()).validate();

        CardEventQuery cardEventQuery = new CardEventQuery();
        cardEventQuery
            .filterByPeriod(dto.getFrom(), dto.getTo())
            .filterByEventTypes(CardEventType.CARD_ASSIGNED_BILLABLE)
            .filterByBuildingId(dto.getBuildingId())
            .filterByCardUUID(dto.getCardUUID());

        List<CardEvent> cardEvents = cardEventQuery.find();

        machineUseQuery
            .filterByPeriod(dto.getFrom(), dto.getTo())
            .filterByBuildingId(dto.getBuildingId())
            .filterByUnitIds(dto.getUnitIds())
            .filterByCardUUID(dto.getCardUUID())
            .filterByDayOfWeek(dto.getDaysOfWeek())
            .filterByMachineIds(dto.getMachineIds());

        // temp
        List<MachineUse> uses = machineUseQuery.find();

        // Only mark machine uses alerts when a building is selected
        if (dto.getBuildingId() > 0) {
            Map<Unit, List<MachineUse>> usesPerUnit = uses
                .stream()
                .filter(use -> use.getUnit() != null)
                .collect(groupingBy(use -> use.getCard().getUnit()));

            MachineUse washerMachineUse = null;
            MachineUse dryerMachineUse = null;
            for (Map.Entry<Unit, List<MachineUse>> machineUsesPerUnit : usesPerUnit.entrySet()) {
                for (MachineUse machineUse : machineUsesPerUnit.getValue()) {
                    if (shouldAlertUse(machineUse, washerMachineUse)) {
                        machineUse.setAlert(true);
                        washerMachineUse.setAlert(true);
                        washerMachineUse = machineUse;
                    } else if (shouldAlertUse(machineUse, dryerMachineUse)) {
                        machineUse.setAlert(true);
                        dryerMachineUse.setAlert(true);
                        dryerMachineUse = machineUse;
                    } else if (isSameMachineType(Machine.MachineType.WASHER, machineUse)) {
                        washerMachineUse = machineUse;
                    } else if (isSameMachineType(Machine.MachineType.DRYER, machineUse)) {
                        dryerMachineUse = machineUse;
                    }
                }
            }
        }

        List<BillableItem> result = new ArrayList<>();
        result.addAll(uses);
        result.addAll(cardEvents);

        result = sortBillableItem(result);

        return json(
            ReportItemSerializer
                .itemListToJson(
                    result,
                    2,
                    new MachineWithOldestKeepAliveQuery(dto.getBuildingId()).get()
                )
                .toString()
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> findUsesByBillNumber(int billNumber) throws APIException {
        MachineUsePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        List<MachineUse> uses = new MachineUseCustomQuery().getUsesByBillNumber(billNumber);

        return json(
            ReportItemSerializer
                .itemListToJson(uses, queryLevel(3, allowedPolicy.level(ApiClient.BACKOFFICE)))
                .toString()
        );
    }

    private boolean isSameMachineType(Machine.MachineType type, MachineUse machineUse) {
        return type == Machine.MachineType.valueOf(machineUse.getMachine().getMachineType().name());
    }

    private boolean shouldAlertUse(MachineUse machineUse, MachineUse washerMachineUse) {
        return (
            washerMachineUse != null &&
            washerMachineUse.getId() != machineUse.getId() &&
            washerMachineUse.getMachine().getId() == machineUse.getMachine().getId() &&
            washerMachineUse.getMachine().getMachineType() ==
            machineUse.getMachine().getMachineType() &&
            (
                getDiffMinutes(machineUse.getTimestamp(), washerMachineUse.getTimestamp()) <
                machineUse.getMachine().getAverageUseTime()
            )
        );
    }

    @Tx
    private <T extends BillableItem> List<T> sortBillableItem(List<T> items) {
        if (items.isEmpty()) return new ArrayList<>();

        List<T> resultWithOutUnit = items
            .stream()
            .filter(x -> x.getUnit() == null)
            .sorted(Comparator.comparing(BillableItem::getTimestamp))
            .collect(Collectors.toList());
        items =
            items
                .stream()
                .filter(x -> x.getUnit() != null)
                .sorted(this::billableItemSorter)
                .collect(Collectors.toList());

        items.addAll(resultWithOutUnit);

        return items;
    }

    @Tx
    private int billableItemSorter(BillableItem item1, BillableItem item2) {
        Unit unit1 = item1.getUnit();
        Unit unit2 = item2.getUnit();
        if (unit1 == null) return -1;
        if (unit2 == null) return 1;

        int unitComparison = unit1.compareTo(unit2);
        if (unitComparison != 0) return unitComparison;

        return item1.getTimestamp().compareTo(item2.getTimestamp());
    }

    @Tx
    private long getDiffMinutes(Date date1, Date date2) {
        if (date1 != null && date2 != null) {
            long diff = date1.getTime() - date2.getTime();
            long diffDays = diff / (24 * 60 * 60 * 1000);
            long diffHours = diff / (60 * 60 * 1000) % 24;
            if (diffDays == 0 && diffHours == 0) return diff / (60 * 1000) % 60;
        }

        return Int.MaxValue();
    }

    @Tx
    public Promise<Result> exportToExcel(int buildingId) throws APIException {
        Building building = Building.findById(buildingId);
        if (building == null) {
            throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
        }

        Date from = null;
        Date to = null;
        int machineId = -1;
        int unitId = -1;
        int accountId = -1;
        String keyword = null;
        String cardUId = null;

        String fromTimestamp = request().getQueryString("from");
        if (fromTimestamp != null) {
            try {
                from = DateHelper.parseUTCDate(fromTimestamp);
            } catch (ParseException ex) {
                play.Logger.error(
                    "Couldn't parse 'from': {} - ex: {}",
                    fromTimestamp,
                    ex.getMessage()
                );
                throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
            }
        }

        String toTimestamp = request().getQueryString("to");
        if (toTimestamp != null) {
            try {
                to = DateHelper.parseUTCDate(toTimestamp);
            } catch (ParseException ex) {
                play.Logger.error("Couldn't parse 'to': {} - ex: {}", toTimestamp, ex.getMessage());
                throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
            }
        }

        String accountIdString = request().getQueryString("account");
        if (accountIdString != null) {
            accountId = Integer.parseInt(accountIdString);
            Account account = Account.findById(accountId);

            for (Building bl : Building.findAll()) {
                for (Unit unit : bl.getUnits()) {
                    if (unit.getOwner() != null && unit.getOwner().equals(account.getOwner())) {
                        unitId = unit.getId();
                    }
                }
            }
        } else {
            String machineIdString = request().getQueryString("machine");
            if (machineIdString != null) {
                machineId = Integer.parseInt(machineIdString);
            }

            String unitIdString = request().getQueryString("unit");
            if (unitIdString != null) {
                unitId = Integer.parseInt(unitIdString);
            }

            keyword = request().getQueryString("keyword");
            cardUId = request().getQueryString("card");
        }

        byte[] data = ReportService.generateExcel(
            building,
            from,
            to,
            accountIdString,
            machineId,
            unitId,
            cardUId,
            keyword
        );

        return Promise.<Result>pure(ok(data).as("application/vnd.ms-excel"));
    }

    @Tx
    public Promise<Result> accreditUse(int useId) throws APIException {
        MachineUsePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.update();

        return modifyUse(useId, true, "");
    }

    @Tx
    @BackOfficeCop(name = "Desacreditar un uso")
    public Promise<Result> discreditUse(int useId) throws APIException {
        MachineUsePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.update();

        String reason = request().getQueryString("reason");

        return modifyUse(useId, false, reason);
    }

    @Tx
    private Promise<Result> modifyUse(int useId, boolean accredit, String reason)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    MachineUse use = MachineUse.findById(useId);
                    if (use == null) {
                        throw APIException.raise(APIErrors.MACHINE_USE_NOT_FOUND);
                    }

                    if (use.isMaintenance()) {
                        return handleMaintenanceUse(use, reason);
                    }

                    use.setAccredited(accredit);
                    use.setReason(reason);

                    SaleNotifierService notifierService = null;
                    if (SaleNotifierService.isNotifiable(use.getMachine().getBuilding().getId())) {
                        List<SaleWrapper> useToNotify = Stream
                            .of(use)
                            .map(SaleWrapper::new)
                            .collect(Collectors.toList());
                        notifierService =
                            SaleNotifierService.getNotificationService(
                                use.getMachine().getBuilding().getId(),
                                useToNotify
                            );
                    }

                    // Se reintegra el saldo en caso de desacreditar el uso
                    if (!accredit) {
                        Card card = Card.findByUID(use.getUid());
                        if (card.getContractType() == ContractType.PREPAID) {
                            Audit audit = new Audit(
                                Audit.TransferType.CREDIT,
                                use.getMachine().getMachineRate().getPriceCustomer(new Date()),
                                Currency.UYU,
                                card.getUuid(),
                                card.getUuid(),
                                "REINTEGRO DE USO",
                                0,
                                ResultCode.OK,
                                card.getBalance(),
                                card.getBalance() +
                                use.getMachine().getMachineRate().getPriceCustomer(new Date())
                            );
                            audit.save();
                            card.setBalance(
                                card.getBalance() +
                                use.getMachine().getMachineRate().getPriceCustomer(new Date())
                            );
                            card.update();

                            if (notifierService != null) {
                                notifierService.notifyRefund();
                            }
                        }
                    } else {
                        if (notifierService != null) {
                            notifierService.notifySale();
                        }
                    }

                    use.update();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN
        );
    }

    private Promise<Result> handleMaintenanceUse(MachineUse use, String reason) {
        MachineUse maintenanceFinishedUse = new MachineUse();
        maintenanceFinishedUse.setResult(MachineUseResult.MAINTENANCE_INTERRUPTED.getCodeString());
        maintenanceFinishedUse.setTimestamp(new Date());
        maintenanceFinishedUse.setMachine(use.getMachine());
        maintenanceFinishedUse.setBuilding(use.getBuilding());
        maintenanceFinishedUse.setReason(reason);
        maintenanceFinishedUse.save();

        return Promise.<Result>pure(ok());
    }

    @Tx
    public Result getDiscreditedUses(int billId) throws APIException {
        List<MachineUse> uses = MachineUse.usesOfBill(billId);
        List<MachineUse> usesNotAccredited = new ArrayList<MachineUse>();
        if (uses != null && !uses.isEmpty()) {
            usesNotAccredited =
                uses.stream().filter(x -> !x.isAccredited()).collect(Collectors.toList());
        }

        return ok(MachineUseSerializer.usesListToJson(usesNotAccredited, 2).toString())
            .as("application/json");
    }

    // reporte de transacciones para administradores
    @Tx
    public Promise<Result> findTransactions() throws APIException {
        Date from = null;
        Date to = null;
        int buildingId = -1;

        String fromTimestamp = request().getQueryString("from");
        if (fromTimestamp != null) {
            try {
                from = DateHelper.parseUTCDate(fromTimestamp);
            } catch (ParseException ex) {
                play.Logger.error(
                    "Couldn't parse 'from': {} - ex: {}",
                    fromTimestamp,
                    ex.getMessage()
                );
                throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
            }
        }

        String toTimestamp = request().getQueryString("to");
        if (toTimestamp != null) {
            try {
                to = DateHelper.parseUTCDate(toTimestamp);
            } catch (ParseException ex) {
                play.Logger.error("Couldn't parse 'to': {} - ex: {}", toTimestamp, ex.getMessage());
                throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
            }
        }

        String buildingIdQueryParam = request().getQueryString("building");
        if (buildingIdQueryParam != null) {
            buildingId = Integer.parseInt(buildingIdQueryParam);
        }

        SimpleDateFormat sqlDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String sqlString =
            "select b.name, p.uuid, t.amount, t.creation_date, t.currency, t.email, t.name as user_name, t.origin, t.authorization_result_message, (t.amount - t.comission) as ingreso_adm " +
            "from lavomat.part p " +
            "inner join lavomat.transaction t on t.uid = p.uuid " +
            "inner join lavomat.building_unit u on p.unit_id = u.unit_id " +
            "inner join lavomat.building b on u.building_id = b.id " +
            "inner join lavomat.rate_event r on b.rate_id = r.rate_id " +
            "where b.id in ('" +
            buildingId +
            "') " +
            "and t.creation_date between '" +
            sqlDateFormat.format(from) +
            "' and '" +
            sqlDateFormat.format(to) +
            "' " +
            "and t.authorization_result = '0' " +
            "and '" +
            sqlDateFormat.format(from) +
            "' > r.valid_from " +
            "and '" +
            sqlDateFormat.format(to) +
            "' < r.valid_until " +
            "and p.contract_type = 'PREPAID';";

        Session session = JPA.em().unwrap(Session.class);
        SQLQuery query = session.createSQLQuery(sqlString.toString());

        List<Object[]> resultSet = query.list();

        JSONObject jsonObject = new JSONObject();
        ArrayList<JSONObject> list = new ArrayList<JSONObject>();

        SimpleDateFormat region_parser = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        region_parser.setTimeZone(TimeZone.getTimeZone("Etc/GMT+3"));

        if (resultSet.size() > 0) {
            for (Object[] record : resultSet) {
                try {
                    JSONObject trxJson = new JSONObject();
                    trxJson.put("building_name", record[0]);
                    trxJson.put("uuid", record[1]);
                    trxJson.put("amount", record[2]);
                    trxJson.put("creation_date", region_parser.format(record[3]));
                    trxJson.put("currency", record[4]);
                    trxJson.put("user_email", record[5]);
                    trxJson.put("user_name", record[6]);
                    trxJson.put("origin", record[7]);
                    trxJson.put("message", record[8]);
                    trxJson.put(
                        "adm_income",
                        Math.round(Double.parseDouble(record[9].toString()) * 100.0) / 100.0
                    );
                    list.add(trxJson);
                    jsonObject.put("Transactions", list);
                } catch (JSONException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }

        if (list.size() == 0 || resultSet.size() == 0) {
            try {
                jsonObject.put("Transactions", "");
            } catch (JSONException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }

        return json(jsonObject.toString());
    }

    // gestion de facturas - export to excel
    @Tx
    public Promise<Result> exportBillsToExcel() throws APIException {
        String buildingStr = request().getQueryString("building");
        String administrationStr = request().getQueryString("administration");
        String fromStr = request().getQueryString("from");
        String toStr = request().getQueryString("to");
        String page = request().getQueryString("page");
        String pageSize = request().getQueryString("perPage");

        String type = request().getQueryString("type");
        String mode = request().getQueryString("mode");
        String dgiNumber = request().getQueryString("dgiNumber");

        String collector = request().getQueryString("collector");
        String collectionStatus = request().getQueryString("collectionStatus");
        String state = request().getQueryString("state");

        String serie = "";
        int number = 0;
        if (dgiNumber != null && dgiNumber.length() >= 5) {
            String[] serie_number = dgiNumber.split("- ");
            if (serie_number.length > 1) {
                serie = serie_number[0];
                try {
                    number = Integer.parseInt(serie_number[1]);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        String[] states = null;
        if (state != null && !state.isEmpty()) {
            try {
                states = state.split(",");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            List<Bill> bills = Bill.find(
                buildingStr != null ? Integer.parseInt(buildingStr) : -1,
                administrationStr != null ? Integer.parseInt(administrationStr) : -1,
                fromStr != null ? new Date(Long.parseLong(fromStr) * 1000) : null,
                toStr != null ? new Date(Long.parseLong(toStr) * 1000) : null,
                page != null ? Integer.parseInt(page) : 1,
                number,
                serie,
                type,
                mode,
                pageSize != null ? Integer.parseInt(pageSize) : 500,
                collectionStatus,
                collector != null ? Integer.parseInt(collector) : -1,
                states,
                null,
                null,
                null
            );

            byte[] data = ReportService.generateBillExcel(bills);
            return Promise.<Result>pure(ok(data).as("application/vnd.ms-excel"));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
        }
    }
}
