package policies;

import models.Machine;
import models.Role;
import play.mvc.Http;

public class MachinePolicy extends BaseNonModelPolicy<Machine> {

    public MachinePolicy(Http.Context ctx) {
        super(ctx);
    }

    @Override
    public Role[] readPermission() {
        return new Role[] { Role.MASTER, Role.BOT, Role.RPI, Role.USER };
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER, Role.RPI };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (Role.BOT == this.role) {
            return Level.MINIMUM_LEVEL;
        } else if (ApiClient.ASSISTANT_WEB.match(client) || ApiClient.BACKOFFICE.match(client)) {
            return Level.MINIMAL_MEDIUM_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
