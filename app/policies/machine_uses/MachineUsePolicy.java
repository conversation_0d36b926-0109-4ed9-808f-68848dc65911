package policies.machine_uses;

import models.MachineUse;
import models.Role;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.machine_uses.MachineUseQuery;

public class MachineUsePolicy extends BaseModelPolicy<MachineUse> {

    protected final MachineUseQuery query;

    public MachineUsePolicy(Http.Context ctx) {
        super(ctx);
        this.query = new MachineUseQuery();
    }

    protected MachineUseQuery query() {
        return this.query;
    }

    @Override
    public Role[] readPermission() {
        return new Role[] {
            Role.BOT,
            Role.MASTER,
            Role.ASSISTANT,
            Role.BUILDING_ADM,
            Role.SUPERVISOR,
            Role.DEBT_COLLECTOR,
        };
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (client.match(ApiClient.BACKOFFICE)) {
            return Level.MEDIUM_HIGH_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
