package policies.bills;

import models.Bill;
import models.Role;
import play.mvc.Http;
import policies.BaseNonModelPolicy;

public class BillPolicy extends BaseNonModelPolicy<Bill> {

    public BillPolicy(Http.Context ctx) {
        super(ctx);
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] readPermission() {
        return new Role[] { Role.MASTER };
    }
}
