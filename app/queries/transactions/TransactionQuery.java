package queries.transactions;

import java.text.ParseException;
import java.util.Date;
import models.Bill;
import models.Transaction;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import queries.BaseModelQuery;

public class TransactionQuery extends BaseModelQuery<Transaction> {

    protected static class Column extends BaseColumn {

        public static final String CREATION_DATE = "creationDate";
        public static final String CARD_UID = "uid";
        public static final String ERROR_CODE = "errorCode";
        public static final String PUBLIC_ID = "publicId";
        public static final String PROVIDER_TRANSACTION_ID = "providerTransactionId";
        public static final String ORIGIN = "origin";
        public static final String REASON_TYPE = "reasonType";
        public static final String EMAIL = "email";
    }

    public TransactionQuery() {
        super(Transaction.class);
    }

    public TransactionQuery filterByErrorCode(Transaction.ErrorCode code) {
        if (code != null) {
            query().eq(Column.ERROR_CODE, code);
        }

        return this;
    }

    public TransactionQuery filterByCardUid(String uid) {
        if (StringUtils.isNotBlank(uid)) {
            query().ilike(Column.CARD_UID, "%" + uid + "%");
        }

        return this;
    }

    public TransactionQuery filterByCreationDate(Date from, Date to) {
        try {
            filterByDates(Column.CREATION_DATE, from, to);
        } catch (ParseException ignored) {}

        return this;
    }

    public TransactionQuery filterByPublicId(String id) {
        if (StringUtils.isNotBlank(id)) {
            query().eq(Column.PUBLIC_ID, id);
        }

        return this;
    }

    public TransactionQuery filterByProviderTransactionId(String id) {
        if (StringUtils.isNotBlank(id)) {
            query().eq(Column.PROVIDER_TRANSACTION_ID, id);
        }

        return this;
    }

    public TransactionQuery orderByCreationDateDesc() {
        query().orderByDesc(Column.CREATION_DATE);

        return this;
    }

    public TransactionQuery filterByOrigin(String origin) {
        if (StringUtils.isNotBlank(origin)) {
            query().eq(Column.ORIGIN, origin);
        }

        return this;
    }

    public TransactionQuery filterByReason(Transaction.ReasonType reason) {
        if (StringUtils.isNotBlank(reason.toString())) {
            query().eq(Column.REASON_TYPE, reason);
        }

        return this;
    }

    public TransactionQuery filterByEmail(String email) {
        if (StringUtils.isNotBlank(email)) {
            query().eq(Column.EMAIL, email);
        }

        return this;
    }

    public TransactionQuery filterByBuildingId(int buildingId) {
        if (buildingId > 0) {
            query()
                .getCriteria()
                .createAlias("unit.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        return this;
    }

    public TransactionQuery filterBillSent() {
        query()
            .getCriteria()
            .createAlias("bills", "bills")
            .add(Restrictions.eq("bills.state", Bill.BillState.SENT));

        return this;
    }
}
