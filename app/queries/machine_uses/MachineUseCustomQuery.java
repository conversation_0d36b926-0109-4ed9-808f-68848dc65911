package queries.machine_uses;

import java.util.List;
import models.Machine;
import models.MachineUse;
import models.MachineUseResult;
import play.db.jpa.JPA;
import utils.SQLHelper;

public class MachineUseCustomQuery {

    public MachineUse getLastMachineUseByMachine(Machine machine, MachineUseResult... results) {
        try {
            return JPA
                .em()
                .createQuery(
                    "SELECT mu FROM models.MachineUse mu " +
                    "WHERE mu.result IN " +
                    SQLHelper.getMachineUseResultCodesForSql(results) +
                    " " +
                    "AND mu.machine.id = :mid " +
                    "ORDER BY mu.timestamp DESC ",
                    MachineUse.class
                )
                .setParameter("mid", machine.getId())
                .setMaxResults(1)
                .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    public List<MachineUse> getUsesByBillNumber(int billNumber) {
        try {
            return (List<MachineUse>) JPA
                .em()
                .createNativeQuery(
                    "SELECT mu.* FROM bill b " +
                    "INNER JOIN audit_workflow aw on aw.payment_tx = b.transaction_id " +
                    "INNER JOIN machine_use mu on aw.transaction_id = mu.transaction_id " +
                    "WHERE b.number = :billNumber ",
                    MachineUse.class
                )
                .setParameter("billNumber", billNumber)
                .getResultList();
        } catch (Exception e) {
            return null;
        }
    }
}
