package queries.machine_model;

import models.MachineModel;
import queries.BaseModelQuery;

public class MachineModelQuery extends BaseModelQuery<MachineModel> {

    protected static class Column extends BaseColumn {

        public static final String NAME = "name";
    }

    public MachineModelQuery() {
        super(MachineModel.class);
    }

    public MachineModelQuery filterByName(String name) {
        this.query().eq(MachineModelQuery.Column.NAME, name);

        return this;
    }
}
