package queries.machines;

import models.Machine;
import org.apache.commons.lang3.StringUtils;

public class MachineQuery extends queries.PartModelQuery<Machine> {

    public MachineQuery() {
        super(Machine.class);
    }

    protected static class Column extends BaseColumn {

        public static final String ODOO_ID = "odooId";
        public static final String SERIAL_NUMBER = "serialNumber";
        public static final String BUILDING_ID = "building.id";
        public static final String ID = "id";
        public static final String SORT_INDEX = "sortIndex";
    }

    public MachineQuery filterByOdooId(final int odooId) {
        this.query().eq(MachineQuery.Column.ODOO_ID, odooId);

        return this;
    }

    // TODO: disaggregate method
    public Long getBuildingMachineCount(int buildingId) {
        query().join("building").eq(Column.BUILDING_ID, buildingId);
        query().gt(Column.SORT_INDEX, Machine.DISABLED_SORT_INDEX);

        return count();
    }

    public MachineQuery searchBySerialNumber(String serialNumber) {
        if (StringUtils.isNotBlank(serialNumber)) {
            query().ilike(Column.SERIAL_NUMBER, "%" + serialNumber + "%");
        }

        return this;
    }

    public MachineQuery filterBySerialNumber(String serialNumber) {
        if (StringUtils.isNotBlank(serialNumber)) {
            query().eq(Column.SERIAL_NUMBER, serialNumber);
        }

        return this;
    }

    public MachineQuery orderByIdDesc() {
        query().orderByDesc(Column.ID);
        return this;
    }

    public MachineQuery filterWithoutBuilding() {
        query().isNull("building");
        return this;
    }
}
