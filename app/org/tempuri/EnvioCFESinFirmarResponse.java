
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaEnvioCFE;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="EnvioCFESinFirmarResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuestaEnvioCFE" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "envioCFESinFirmarResult" })
@XmlRootElement(name = "EnvioCFESinFirmarResponse")
public class EnvioCFESinFirmarResponse {

    @XmlElementRef(name = "EnvioCFESinFirmarResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuestaEnvioCFE> envioCFESinFirmarResult;

    /**
     * Gets the value of the envioCFESinFirmarResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}
     * 
     */
    public JAXBElement<SICFERespuestaEnvioCFE> getEnvioCFESinFirmarResult() {
        return envioCFESinFirmarResult;
    }

    /**
     * Sets the value of the envioCFESinFirmarResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuestaEnvioCFE }{@code >}
     * 
     */
    public void setEnvioCFESinFirmarResult(JAXBElement<SICFERespuestaEnvioCFE> value) {
        this.envioCFESinFirmarResult = value;
    }

}
