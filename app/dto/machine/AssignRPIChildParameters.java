package dto.machine;

import global.APIException;
import global.APIException.*;
import models.Machine;

public class AssignRPIChildParameters extends dto.ActionParameters {

    protected Machine rpiChildMachine;
    protected Machine rpiParentMachine;

    public AssignRPIChildParameters(int rpiChildId, int rpiParentId) {
        this.rpiChildMachine = Machine.findById(rpiChildId);
        this.rpiParentMachine = Machine.findById(rpiParentId);
    }

    public AssignRPIChildParameters validate() throws APIException {
        if (this.rpiChildMachine == null || this.rpiParentMachine == null) {
            throw APIException.raise(APIErrors.MACHINE_NOT_FOUND);
        }

        return this;
    }

    public Machine getRpiChildMachine() {
        return this.rpiChildMachine;
    }

    public Machine getRpiParentMachine() {
        return this.rpiParentMachine;
    }
}
