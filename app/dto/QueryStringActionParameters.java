package dto;

import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import utils.DateHelper;

public abstract class QueryStringActionParameters extends dto.ActionParameters {

    // STRING
    public String safeString(String key, Map<String, String[]> queryString) {
        return safeString(key, queryString, null);
    }

    public String safeString(String key, Map<String, String[]> queryString, String defaultValue) {
        String[] value = queryString.get(key);
        return (value == null || value.length == 0) ? defaultValue : value[0];
    }

    protected List<String> safeStringArray(String key, Map<String, String[]> queryString) {
        return Arrays.asList(queryString.get(key));
    }

    // INTEGER
    protected int safeInt(String key, Map<String, String[]> queryString, Integer defaultValue) {
        String intString = safeString(key, queryString);

        if (intString != null && !intString.isEmpty()) {
            return Integer.parseInt(intString);
        }

        return defaultValue;
    }

    protected List<Integer> safeIntegerArray(String key, Map<String, String[]> queryString) {
        String[] list = queryString.get(key);
        return (list == null ? Stream.<String>empty() : Arrays.stream(list)).map(Integer::parseInt)
            .collect(Collectors.toList());
    }

    // BOOLEAN
    protected Boolean safeBoolean(String key, Map<String, String[]> queryString)
        throws APIException {
        Boolean value = safeBooleanNullable(key, queryString);

        if (value != null) return value;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected Boolean safeBoolean(
        String key,
        Map<String, String[]> queryString,
        Boolean defaultValue
    ) {
        Boolean value = safeBooleanNullable(key, queryString);
        if (value != null) return value;

        return defaultValue;
    }

    protected Boolean safeBooleanNullable(String key, Map<String, String[]> queryString) {
        if (queryString != null && queryString.containsKey(key)) {
            String[] values = queryString.get(key);
            if (values != null && values.length > 0) {
                return "true".equalsIgnoreCase(values[0]);
            }
        }

        return null;
    }

    // DATE
    protected Date safeDate(String key, Map<String, String[]> queryString) {
        return safeDate(key, queryString, null);
    }

    protected Date safeDate(String key, Map<String, String[]> queryString, Date defaultValue) {
        String dateString = safeString(key, queryString);

        if (dateString != null && !dateString.isEmpty()) {
            return new Date(Long.parseLong(dateString) * 1000);
        }

        return defaultValue;
    }

    protected Date safeDateNullable(String key, Map<String, String[]> queryString) {
        return safeDateNullable(key, queryString, DateHelper.utcDateFormat);
    }

    protected Date safeDateNullable(
        String key,
        Map<String, String[]> queryString,
        SimpleDateFormat pattern
    ) {
        return safeDateNullable(safeString(key, queryString), (Date) null, pattern);
    }

    protected Date unsafeDateNullable(String key, Map<String, String[]> queryString)
        throws APIException {
        return unsafeDateNullable(safeString(key, queryString), null, DateHelper.utcDateFormat);
    }

    protected Date unsafeDateNullable(String dateText, Date defaultValue, SimpleDateFormat pattern)
        throws APIException {
        if (dateText != null) {
            try {
                return pattern.parse(dateText);
            } catch (Exception e) {
                play.Logger.error("Couldn't parse: {} - ex: {}", dateText, e.getMessage());
                throw APIException.raise(APIErrors.BAD_DATE_FORMAT);
            }
        }

        return defaultValue;
    }

    // ENUM
    protected <E extends Enum<E>> E safeEnum(
        Class<E> enumClass,
        String key,
        Map<String, String[]> queryString,
        E defaultValue
    ) throws APIException {
        E value = safeEnumNullable(enumClass, key, queryString, defaultValue);
        if (value != null) return value;

        if (defaultValue != null) return defaultValue;

        throw APIException
            .raise(APIErrors.MISSING_PARAMETERS)
            .setDetailMessage(getMissingParametersMessage(key));
    }

    protected <E extends Enum<E>> E safeEnumNullable(
        Class<E> enumClass,
        String key,
        Map<String, String[]> queryString,
        E defaultValue
    ) throws APIException {
        String value = safeString(key, queryString);
        if (StringUtils.isNotBlank(value)) {
            try {
                return Enum.valueOf(enumClass, value.toUpperCase());
            } catch (IllegalArgumentException e) {
                throw APIException
                    .raise(APIErrors.UNSUPPORTED_VALUE)
                    .setDetailMessage(
                        "The received value for \"" +
                        key +
                        "\" (\"" +
                        value +
                        "\") is unexpected according to the declared values."
                    );
            }
        }

        return defaultValue;
    }
    // POC: OBJECT
}
