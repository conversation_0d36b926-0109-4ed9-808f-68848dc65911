package dto.report;

import global.APIException;
import global.exceptions.MissingParametersException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import utils.StringHelper;

public class UsesReportParameters extends dto.QueryStringActionParameters {

    protected static final String FROM = "from";
    protected static final String TO = "to";
    protected static final String BUILDING_ID = "building";
    protected static final String MACHINE_IDS = "machines";
    protected static final String UNIT_ID = "unit";
    protected static final String CARD_UUID = "card";
    protected static final String DAYS_OF_WEEK = "daysOfWeek";
    protected static final String ONLY_CARD_EVENTS = "onlyCardEvents";
    protected static final String KEYWORD = "keyword";

    protected Date from;
    protected Date to;
    protected int buildingId;

    protected List<Integer> machineIds;
    protected List<Integer> unitIds;
    protected String cardUUID;
    protected String keyword;

    protected List<Integer> daysOfWeek;
    protected boolean onlyCardEvents;

    public UsesReportParameters(Map<String, String[]> queryString) throws APIException {
        this.from = unsafeDateNullable(FROM, queryString);
        this.to = unsafeDateNullable(TO, queryString);
        this.daysOfWeek = safeIntegerArray(DAYS_OF_WEEK, queryString);

        this.buildingId = safeInt(BUILDING_ID, queryString, -1);
        this.unitIds = safeIntegerArray(UNIT_ID, queryString);
        this.machineIds = safeIntegerArray(MACHINE_IDS, queryString);
        this.cardUUID = safeString(CARD_UUID, queryString, null);

        this.onlyCardEvents = safeBoolean(ONLY_CARD_EVENTS, queryString, false);

        this.keyword = safeString(KEYWORD, queryString, null);
    }

    @Override
    public UsesReportParameters validate() throws APIException {
        if (this.from == null || this.to == null) {
            throw new MissingParametersException(FROM, TO);
        }

        if (this.buildingId == -1 && StringHelper.isBlank(this.cardUUID)) {
            throw new MissingParametersException(BUILDING_ID, CARD_UUID);
        }

        return this;
    }

    public Date getFrom() {
        return this.from;
    }

    public Date getTo() {
        return this.to;
    }

    public int getBuildingId() {
        return this.buildingId;
    }

    public List<Integer> getMachineIds() {
        return this.machineIds;
    }

    public List<Integer> getUnitIds() {
        return this.unitIds;
    }

    public String getCardUUID() {
        return this.cardUUID;
    }

    public String getKeyword() {
        return this.keyword;
    }

    public List<Integer> getDaysOfWeek() {
        return this.daysOfWeek;
    }

    public boolean isOnlyCardEvents() {
        return this.onlyCardEvents;
    }
}
