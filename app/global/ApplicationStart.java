package global;

import java.util.logging.LogManager;
import javax.inject.Inject;
import javax.inject.Singleton;
import org.slf4j.bridge.SLF4JBridgeHandler;
import play.Logger;
import play.inject.ApplicationLifecycle;

@Singleton
public class ApplicationStart {

    @Inject
    public ApplicationStart(ApplicationLifecycle lifecycle) {
        Logger.info("Routing JUL to SLF4J...");
        LogManager.getLogManager().reset();
        SLF4JBridgeHandler.removeHandlersForRootLogger();
        SLF4JBridgeHandler.install();
        Logger.info("Routing completed.");
    }
}
