package models;

import com.play4jpa.jpa.models.Finder;
import java.util.List;
import javax.persistence.*;
import play.mvc.Http.Request;

@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "FROM_CLASS", discriminatorType = DiscriminatorType.STRING, length = 50)
@Table(
    indexes = {
        @Index(name = "PART_CARD_UUID", columnList = "uuid", unique = true),
        @Index(name = "MACHINE_KEEP_ALIVE", columnList = "lastAlive"),
        @Index(name = "MACHINE_TYPE", columnList = "machineType"),
        @Index(name = "ID_MACHINE_TYPE", columnList = "id, machineType"),
        @Index(name = "FROM_CLASS", columnList = "from_class"),
    }
)
public class Part extends BaseModel<Part> {

    /**
     *
     */
    private static final long serialVersionUID = 5115167222558162182L;

    public enum PartState {
        NEW,
        ON_SERVICE,
        ON_MAINTENANCE,
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        LOST,
        DISCON<PERSON>CTED,
        DAMAGED,
        PRE_BLOCKED,
        DEPRECIATED,
        MAINTENANCE_IN_SITU,
        MAINTENANCE_IN_WORKSHOP,
        SOLD,
        REMOVED_FROM_SALE,
        REFURBISHED_FOR_INSTALLATION,
    }

    @Id
    @GeneratedValue
    private int id;

    private String name;

    private String model;

    private String description;

    @Enumerated(EnumType.STRING)
    private PartState state;

    @Enumerated(EnumType.STRING)
    private PartState subState;

    private String serialNumber;

    public Part() {}

    public Part(
        String name,
        String model,
        String description,
        PartState state,
        String serialNumber
    ) {
        super();
        this.name = name;
        this.model = model;
        this.description = description;
        this.state = state;
        this.serialNumber = serialNumber;
    }

    protected static Finder<Integer, ? extends Part> finder = new Finder<>(
        Integer.class,
        Part.class
    );

    public static Part findById(int partId) {
        return finder.byId(partId);
    }

    public static List<? extends Part> findAll() {
        return finder.all();
    }

    public static List<? extends Part> findWithFilters(Request request) {
        return finder.all();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public PartState getState() {
        return state;
    }

    public void setState(PartState state) {
        this.state = state;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public PartState getSubState() {
        return subState;
    }

    public void setSubState(PartState subState) {
        this.subState = subState;
    }
}
