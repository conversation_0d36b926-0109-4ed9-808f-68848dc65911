package models;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Transient;

@Entity
public class MaintenanceParameter extends BaseModel<MaintenanceParameter> {

    @Id
    @GeneratedValue
    private int id;

    private int mp100;
    private int mp500;
    private int mp1200;

    @Transient
    private Building building;

    public MaintenanceParameter() {
        // default values
        this.mp100 = 50;
        this.mp500 = 500;
        this.mp1200 = 1000;
    }

    public MaintenanceParameter(int mp100, int mp500, int mp1200) {
        this.mp100 = mp100;
        this.mp500 = mp500;
        this.mp1200 = mp1200;
    }

    public static MaintenanceParameter getMaintenanceParameterForBuildingAndMachineModel(
        Building building,
        MachineModel machineModel
    ) {
        MaintenanceParameter buildingParameter = building.getMaintenanceParameter();

        MaintenanceParameter modelParameter = null;
        if (machineModel != null) {
            modelParameter = machineModel.getParameter();
        }

        MaintenanceParameter defaultParameter = new MaintenanceParameter();

        if (buildingParameter != null) {
            buildingParameter.setBuilding(building);
            return buildingParameter;
        }

        if (modelParameter != null) {
            modelParameter.setBuilding(building);
            return modelParameter;
        }

        defaultParameter.setBuilding(building);
        return defaultParameter;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getMp100() {
        return this.mp100;
    }

    public void setMp100(int mp100) {
        this.mp100 = mp100;
    }

    public int getMp500() {
        return this.mp500;
    }

    public void setMp500(int mp500) {
        this.mp500 = mp500;
    }

    public int getMp1200() {
        return this.mp1200;
    }

    public void setMp1200(int mp1200) {
        this.mp1200 = mp1200;
    }

    public Building getBuilding() {
        return this.building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!MaintenanceParameter.class.isAssignableFrom(obj.getClass())) return false;
        return (
            ((MaintenanceParameter) obj).getMp100() == this.getMp100() &&
            ((MaintenanceParameter) obj).getMp500() == this.getMp500() &&
            ((MaintenanceParameter) obj).getMp1200() == this.getMp1200()
        );
    }
}
