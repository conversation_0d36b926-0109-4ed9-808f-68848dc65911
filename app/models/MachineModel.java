package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.List;
import javax.persistence.*;

@Entity
public class MachineModel extends Model<MachineModel> {

    @Id
    @GeneratedValue
    private int id;

    private String name;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH })
    private MaintenanceParameter parameter;

    public MachineModel() {}

    public MachineModel(String name, MaintenanceParameter parameter) {
        this.name = name;
        this.parameter = parameter;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public MaintenanceParameter getParameter() {
        return this.parameter;
    }

    public void setParameter(MaintenanceParameter parameter) {
        if (this.parameter == null) {
            this.parameter = parameter;
        } else {
            this.parameter.setMp100(parameter.getMp100());
            this.parameter.setMp500(parameter.getMp500());
            this.parameter.setMp1200(parameter.getMp1200());
        }
    }

    public static Finder<Integer, MachineModel> find = new Finder<Integer, MachineModel>(
        Integer.class,
        MachineModel.class
    );

    public static MachineModel findById(int machineModelId) {
        return find.byId(machineModelId);
    }

    public static List<MachineModel> findAll() {
        return find.all();
    }
}
