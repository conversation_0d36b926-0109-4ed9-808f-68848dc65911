package models;

import java.util.Arrays;

public enum Role {
    USER,
    AD<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    FINANCE,
    INVESTOR,
    TECHNI<PERSON>AN,
    SUPERVISOR,
    COMPANY,
    BUILDING_ADM,
    DEBT_COLLECTOR,
    TOTEM,
    DEVELOPER,
    ASSISTANT,
    RPI,
    BOT,
    TASK_RUNNER,
    ERP;

    public boolean match(Role... roles) {
        return Arrays.stream(roles).anyMatch(x -> x == this);
    }
}
