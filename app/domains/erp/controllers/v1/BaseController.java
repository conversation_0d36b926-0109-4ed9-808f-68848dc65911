package domains.erp.controllers.v1;

import global.APIException;
import global.ErrorMessage;
import play.mvc.Security;
import policies.ApiClient;
import security.v1.Secured;

@ErrorMessage
@global.LoggingMessage(auth = true)
@Security.Authenticated(Secured.class)
public class BaseController extends controllers.AbstractController {

    @Override
    protected int queryLevel(int defaultLevel) throws APIException {
        int maxLevel = this.getAllowedPolicy().level(ApiClient.ODOO);
        int level = super.queryLevel(defaultLevel);

        return Math.min(level, maxLevel);
    }
}
