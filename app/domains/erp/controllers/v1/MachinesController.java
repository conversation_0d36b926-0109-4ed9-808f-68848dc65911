package domains.erp.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.erp.dto.machines.MachineCreateParameters;
import domains.erp.services.MachineFactory;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import policies.MachinePolicy;
import policies.actions.Policy;

@Policy(MachinePolicy.class)
public class MachinesController extends BaseController {

    @Tx
    public F.Promise<Result> create() throws APIException {
        MachinePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        MachineCreateParameters dto = new MachineCreateParameters(body()).validate();
        new MachineFactory().create(dto);

        return F.Promise.<Result>pure(created());
    }
}
