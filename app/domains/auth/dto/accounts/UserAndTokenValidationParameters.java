package domains.auth.dto.accounts;

import global.APIException;
import models.Account;
import models.User;
import org.apache.commons.lang3.StringUtils;

public class UserAndTokenValidationParameters extends dto.ActionParameters {

    protected String userId;
    protected String token;
    protected Account account;
    protected User user;

    public UserAndTokenValidationParameters(String userId, String token) {
        this.userId = userId;
        this.token = token;
    }

    @Override
    public UserAndTokenValidationParameters validate() throws APIException {
        if (StringUtils.isBlank(this.userId) || StringUtils.isBlank(this.token)) {
            throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);
        }

        int id = Integer.parseInt(this.userId);
        this.user = User.findById(id);
        if (this.user == null) {
            throw APIException
                .raise(APIException.APIErrors.USER_NOT_FOUND)
                .setDetailMessage("USER_NOT_FOUND_" + this.userId);
        }

        this.account = this.user.getMasterAccount();
        if (this.account == null) {
            throw APIException
                .raise(APIException.APIErrors.ACCOUNT_NOT_FOUND)
                .setDetailMessage("ACCOUNT_NOT_FOUND");
        }

        return this;
    }

    public Account getAccount() {
        return this.account;
    }

    public User getUser() {
        return this.user;
    }
}
