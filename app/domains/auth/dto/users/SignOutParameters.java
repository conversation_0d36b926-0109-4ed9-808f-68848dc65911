package domains.auth.dto.users;

import global.APIException;
import java.util.Map;

public class SignOutParameters extends dto.QueryStringActionParameters {

    private final boolean hasToSignOutAllDevices;

    public SignOutParameters(Map<String, String[]> queryString) {
        this.hasToSignOutAllDevices = safeBoolean("all-devices", queryString, false);
    }

    @Override
    public SignOutParameters validate() throws APIException {
        return this;
    }

    public boolean hasToSignOutAllDevices() {
        return hasToSignOutAllDevices;
    }
}
