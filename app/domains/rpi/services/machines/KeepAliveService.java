package domains.rpi.services.machines;

import domains.rpi.dto.machines.AliveParameters;
import java.util.Date;
import models.Machine;
import services.BaseService;

public class KeepAliveService extends BaseService {

    private final AliveParameters dto;
    private final Date keepAlive;

    public KeepAliveService(AliveParameters dto) {
        this.dto = dto;
        this.keepAlive = new Date();
    }

    public void record() {
        Machine mainMachine = this.dto.getMainMachine();
        updateMainMachine(mainMachine);
        updateAssociatedMachine(this.dto.getSecondMachine());
        updateAssociatedMachine(mainMachine.getRPIChild());
    }

    private void updateMainMachine(Machine machine) {
        machine.setLastAlive(this.keepAlive);
        if (this.dto.getPublicIp() != null) {
            machine.setPublicIp(this.dto.getPublicIp());
        }
        if (this.dto.getPrivateIp() != null) {
            machine.setPrivateIp(this.dto.getPrivateIp());
        }
        if (this.dto.getPort() != null) {
            machine.setPort(this.dto.getPort());
        }
        if (this.dto.getPendingUses() != null) {
            machine.setPendingUses(this.dto.getPendingUses());
        }
        if (this.dto.getMainMachineFirmwareVersion() != null) {
            machine.setFirmwareVersion(this.dto.getMainMachineFirmwareVersion());
        }
        if (this.dto.wasMainMachineFirmwareUpdateMade()) {
            machine.setFirmware(this.dto.getMainMachineFirmware());
            machine.setUpgradeTo(null);
        }

        machine.update();
    }

    private void updateAssociatedMachine(Machine machine) {
        if (machine != null) {
            machine.setLastAlive(this.keepAlive);
            machine.update();
        }
    }
}
