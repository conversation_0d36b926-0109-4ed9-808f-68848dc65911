package domains.back_office.dto.transactions;

import global.APIException;
import java.util.Map;

public class GetTransactionParameters extends dto.BaseFilterParameters {

    private final int buildingId;
    private final Boolean onlySent;
    private final Boolean isCSV;

    public GetTransactionParameters(Map<String, String[]> queryString) throws APIException {
        super(queryString);
        this.buildingId = safeInt("buildingId", queryString, null);
        this.onlySent = safeBoolean("onlySent", queryString, true);
        this.isCSV = safeBoolean("isCSV", queryString, false);
    }

    @Override
    public GetTransactionParameters validate() throws APIException {
        super.validate();

        return this;
    }

    public int getBuildingId() {
        return this.buildingId;
    }

    public Boolean onlySent() {
        return this.onlySent;
    }

    public Boolean isCSV() {
        return this.isCSV;
    }
}
