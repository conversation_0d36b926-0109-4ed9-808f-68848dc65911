package domains.back_office.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.dto.transactions.GetTransactionParameters;
import domains.back_office.serializers.TransactionSerializer;
import global.APIException;
import java.util.List;
import models.Transaction;
import play.libs.F;
import play.mvc.Result;
import policies.actions.Policy;
import policies.transactions.TransactionPolicy;
import queries.transactions.TransactionQuery;

@Policy(TransactionPolicy.class)
public class TransactionsController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list() throws APIException {
        TransactionPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        GetTransactionParameters params = new GetTransactionParameters(queryString()).validate();

        TransactionQuery query = new TransactionQuery()
            .filterByCardUid(params.getUid())
            .filterByCreationDate(params.getFrom(), params.getTo())
            .filterByBuildingId(params.getBuildingId())
            .orderBy(params.getOrder(), params.isOrderDescendant());

        if (params.onlySent()) {
            query.filterBillSent();
        }

        List<Transaction> transactions;
        if (params.isCSV()) {
            transactions = query.find();
        } else {
            transactions = query.page(params.getPage(), params.getPerPage());
        }

        return json(TransactionSerializer.listToJson(transactions, queryLevel(2)));
    }
}
