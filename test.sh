#!/bin/bash

# test.sh - Simplified testing script for Java Play Framework
# Usage: ./test.sh [options] [test-pattern]
#
# Examples:
#   ./test.sh                                          # Run all tests
#   ./test.sh controllers.WebControllerTest            # Run specific test class
#   ./test.sh controllers.WebControllerTest.testPing   # Run specific test method
#   ./test.sh controllers.*                            # Run all controller tests
#   ./test.sh -w controllers.WebControllerTest         # Watch mode for specific class
#   ./test.sh -q                                       # Run only quick/changed tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
WATCH_MODE=false
QUICK_MODE=false
VERBOSE=true
QUIET=false
TEST_PATTERN=""

# Function to show usage
show_usage() {
    echo -e "${BLUE}test.sh - Simplified Testing Script${NC}"
    echo ""
    echo "Usage: ./test.sh [options] [test-pattern]"
    echo ""
    echo "Options:"
    echo "  -w, --watch     Enable watch mode (continuous testing)"
    echo "  -q, --quick     Run only quick/changed tests"
    echo "  -s, --silent    Disable verbose output (quiet mode)"
    echo "  -h, --help      Show this help message"
    echo ""
    echo "Test Patterns:"
    echo "  (no pattern)                           Run all tests"
    echo "  controllers.WebControllerTest          Run specific test class"
    echo "  controllers.WebControllerTest.testPing Run specific test method"
    echo "  controllers.*                          Run all tests in package"
    echo "  *.WebControllerTest                    Run all classes matching pattern"
    echo ""
    echo "Examples:"
    echo "  ./test.sh                                          # All tests"
    echo "  ./test.sh controllers.WebControllerTest            # Specific class"
    echo "  ./test.sh controllers.WebControllerTest.testPing   # Specific method"
    echo "  ./test.sh controllers.*                            # All controller tests"
    echo "  ./test.sh -w controllers.WebControllerTest         # Watch specific class"
    echo "  ./test.sh -q                                       # Quick tests only"
    echo "  ./test.sh -s controllers.WebControllerTest         # Silent mode (no verbose output)"
    echo ""
}

# Function to log messages
log() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

# Function to log warnings
warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Function to log errors
error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to log success
success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -w|--watch)
            WATCH_MODE=true
            shift
            ;;
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -s|--silent)
            VERBOSE=false
            QUIET=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            error "Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            if [ -z "$TEST_PATTERN" ]; then
                TEST_PATTERN="$1"
            else
                error "Multiple test patterns provided. Use only one pattern."
                exit 1
            fi
            shift
            ;;
    esac
done

# Function to build the activator command
build_command() {
    local cmd="./activator"
    local test_cmd=""

    # Add watch prefix if enabled
    if [ "$WATCH_MODE" = true ]; then
        test_cmd="~"
    fi

    # Determine test command based on pattern and mode
    if [ "$QUICK_MODE" = true ] && [ -z "$TEST_PATTERN" ]; then
        # Quick mode with no specific pattern
        test_cmd="${test_cmd}testQuick"
        echo "$cmd \"$test_cmd\""
        return
    elif [ -z "$TEST_PATTERN" ]; then
        # No pattern - run all tests
        test_cmd="${test_cmd}test"
        echo "$cmd \"$test_cmd\""
        return
    fi

    # Parse the test pattern
    if [[ "$TEST_PATTERN" == *.* ]] && [[ "$TEST_PATTERN" != *\** ]]; then
        # Pattern contains a dot and no wildcards - likely class.method format
        local class_part="${TEST_PATTERN%.*}"
        local method_part="${TEST_PATTERN##*.}"

        # Check if method part looks like a method name (starts with 'test' or is a simple method name)
        # Class names typically start with uppercase, method names with lowercase or 'test'
        if [[ "$method_part" =~ ^test[A-Z] ]] || [[ "$method_part" =~ ^[a-z][a-zA-Z0-9]*$ ]]; then
            # This is a class.method pattern
            test_cmd="${test_cmd}testOnly $class_part -- --tests=$method_part.*"
        else
            # This is just a class pattern with dots (like package.Class)
            test_cmd="${test_cmd}testOnly $TEST_PATTERN"
        fi
    else
        # This is a class or wildcard pattern
        test_cmd="${test_cmd}testOnly $TEST_PATTERN"
    fi

    echo "$cmd \"$test_cmd\""
}

# Function to validate activator exists
check_activator() {
    if [ ! -f "./activator" ]; then
        error "activator script not found in current directory"
        error "Make sure you're running this script from the project root"
        exit 1
    fi

    if [ ! -x "./activator" ]; then
        warn "activator script is not executable, attempting to fix..."
        chmod +x ./activator
        if [ $? -eq 0 ]; then
            success "Made activator executable"
        else
            error "Failed to make activator executable"
            exit 1
        fi
    fi
}

# Function to show what command will be executed
show_command_info() {
    local cmd="$1"
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}  Test Runner Configuration${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "Pattern:     ${YELLOW}${TEST_PATTERN:-'(all tests)'}${NC}"
    echo -e "Watch Mode:  ${YELLOW}${WATCH_MODE}${NC}"
    echo -e "Quick Mode:  ${YELLOW}${QUICK_MODE}${NC}"
    echo -e "Command:     ${GREEN}${cmd}${NC}"
    echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
    echo ""

    if [ "$WATCH_MODE" = true ]; then
        echo -e "${YELLOW}Watch mode enabled - tests will rerun when files change${NC}"
        echo -e "${YELLOW}Press Enter or Ctrl+D to stop watch mode${NC}"
        echo ""
    fi
}

# Main execution
main() {
    log "Starting test script..."

    # Validate environment
    check_activator

    # Build the command
    local command=$(build_command)

    # Log pattern detection details
    if [ -n "$TEST_PATTERN" ]; then
        if [[ "$TEST_PATTERN" == *.* ]] && [[ "$TEST_PATTERN" != *\** ]]; then
            local class_part="${TEST_PATTERN%.*}"
            local method_part="${TEST_PATTERN##*.}"
            if [[ "$method_part" =~ ^test[A-Z] ]] || [[ "$method_part" =~ ^[a-z][a-zA-Z0-9]*$ ]]; then
                log "Detected class.method pattern: $class_part -> $method_part"
            else
                log "Detected class pattern: $TEST_PATTERN"
            fi
        else
            log "Detected class/wildcard pattern: $TEST_PATTERN"
        fi
    fi

    log "Built command: $command"

    # Show what we're about to do
    if [ "$QUIET" = false ] || [ "$WATCH_MODE" = true ]; then
        show_command_info "$command"
    fi

    # Execute the command
    log "Executing: $command"
    eval "$command"
    local exit_code=$?

    # Report results
    if [ $exit_code -eq 0 ]; then
        success "Tests completed successfully!"
    else
        error "Tests failed with exit code $exit_code"
    fi

    exit $exit_code
}

# Trap Ctrl+C to provide clean exit
trap 'echo -e "\n${YELLOW}Test execution interrupted${NC}"; exit 130' INT

# Run main function
main "$@"
