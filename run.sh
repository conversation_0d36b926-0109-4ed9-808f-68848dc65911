#!/bin/bash

# --------------------------------------------------------------------------
#
# This script manages the startup and shutdown of a development environment
# with MySQL database.
#
# Available flags:
#   --omit-db       : <PERSON>p starting MySQL service at startup
#   --omit-db-stop  : Skip stopping MySQL service at shutdown
#   --no-browser    : Skip opening the application in browser
#   -h, --help      : Display this help message and exit
#
# --------------------------------------------------------------------------

# Function to display help message
show_help() {
    sed -n '/^# ----/,/^# ----/p' "$0" | sed 's/^# \{0,1\}//'
    exit 0
}

# Initialize flags
OMIT_DB=false
OMIT_DB_STOP=false
NO_BROWSER=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --help|-h)
            show_help
            ;;
        --omit-db)
            OMIT_DB=true
            ;;
        --omit-db-stop)
            OMIT_DB_STOP=true
            ;;
        --no-browser)
            NO_BROWSER=true
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            ;;
    esac
    shift
done

# Start MySQL service, if the flag `--omit-db` is NOT present
if [[ "$OMIT_DB" == "false" ]]; then
    brew services restart mysql@8.0
fi

# Open the application in the default web browser, if --no-browser is NOT present
if [[ "$NO_BROWSER" == "false" ]]; then
    open http://localhost:9000/
fi

# Start the application using Activator with the following configuration:
# - Remote debugging enabled on port 9999
# - Development environment
# - UTC timezone
./activator -jvm-debug 9999 -Denv=dev -Duser.timezone=UTC run

# Stop MySQL service, if flags `--omit-db` and `--omit-db-stop` are NOT present
if [[ "$OMIT_DB" == "false" && "$OMIT_DB_STOP" == "false" ]]; then
    brew services stop mysql@8.0
fi
