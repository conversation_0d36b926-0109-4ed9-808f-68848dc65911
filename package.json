{"name": "lm_app", "version": "3.0.0", "engines": {"node": "14.18.2"}, "scripts": {"postinstall": "husky install", "check": "ncp", "build": "grunt uglify --force", "start": "node server.js", "heroku-postbuild": "cd public &&  ../node_modules/bower/bin/bower install", "format:play": "prettier --write 'app/**/*.java'", "format:ng": "prettier --write 'public/**/*.{js,html}'", "format:doc": "prettier --write 'doc_web/**/*.{js,html}'", "format": "prettier --write '**/*.{java,js,html}'", "format:check": "prettier --check '**/*.{java,js,html}'"}, "dependencies": {"-": "0.0.1", "bower": "1.8.14", "g": "^2.0.1", "grunt": "^1.5.3", "grunt-banner": "0.6.0", "grunt-bump": "0.8.0", "grunt-cli": "^1.4.3", "grunt-contrib-clean": "2.0.1", "grunt-contrib-compress": "^2.0.0", "grunt-contrib-concat": "2.1.0", "grunt-contrib-copy": "1.0.0", "grunt-contrib-cssmin": "^4.0.0", "grunt-contrib-imagemin": "^4.0.0", "grunt-contrib-jshint": "3.2.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-watch": "^1.1.0", "grunt-filerev": "2.3.1", "grunt-grep": "^0.7.1", "grunt-html-snapshot": "^0.6.1", "grunt-ng-constant": "^2.0.3", "grunt-prettify": "latest", "grunt-sass": "^3.1.0", "grunt-usemin": "3.1.1", "load-grunt-configs": "1.0.0", "load-grunt-tasks": "^5.1.0"}, "devDependencies": {"angular-html-parser": "^1.8.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "node-sass": "^9.0.0", "npm-check-updates": "^15.2.6", "prettier": "^2.7.1", "prettier-plugin-java": "^1.6.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{java}": ["prettier --write", "git add"], "*.{js,html,json}": ["prettier --write", "git add"]}}