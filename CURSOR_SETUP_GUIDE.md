# Cursor IDE Setup Guide for Java Play Framework Project

This guide provides step-by-step instructions to complete the Cursor IDE setup for your Java Play Framework application.

## Automated Setup Complete ✅

The following have been automatically configured:

- ✅ VS Code workspace settings (`.vscode/settings.json`)
- ✅ Recommended extensions (`.vscode/extensions.json`)
- ✅ Debug configuration (`.vscode/launch.json`)
- ✅ Build tasks (`.vscode/tasks.json`)
- ✅ Java workspace file (`.vscode/java.code-workspace`)
- ✅ Checkstyle configuration (`checkstyle.xml`)
- ✅ SonarLint configuration (`sonar-project.properties`)
- ✅ SpotBugs exclusions (`spotbugs-exclude.xml`)
- ✅ Eclipse project metadata generated
- ✅ Java formatter settings

## Manual Setup Steps Required

### 1. Install Required Cursor Extensions

Open Cursor and install these essential extensions:

```bash
# Core Java Development Pack
ext install redhat.java
ext install vscjava.vscode-java-pack

# Linting & Code Quality
# ext install sonarsource.sonarlint-vscode
# ext install shengchen.vscode-checkstyle
# ext install spotbugs.spotbugs

# Formatting & Utilities
ext install esbenp.prettier-vscode
ext install editorconfig.editorconfig
# ext install usernamehw.errorlens
ext install eamodio.gitlens
```

**Alternative**: Cursor should automatically prompt you to install the recommended extensions from `.vscode/extensions.json`.

### 2. Configure Java Home Path

Update the Java home path in `.vscode/settings.json` if your Java 8 installation is in a different location:

```bash
# Find your Java 8 installation
/usr/libexec/java_home -v 1.8

# Update the path in .vscode/settings.json:
# "java.configuration.runtimes": [
#     {
#         "name": "JavaSE-1.8",
#         "path": "/YOUR_JAVA_8_PATH_HERE"
#     }
# ]
```

### 3. Open Project in Cursor

1. Open Cursor
2. File → Open Workspace from File
<!-- 3. Select `.vscode/java.code-workspace` -->
3. Or simply open the project folder directly

### 4. Initialize Java Language Server

1. Open any Java file (e.g., `app/models/Unit.java`)
2. Wait for the Java language server to initialize (bottom-right status bar)
3. If prompted, allow the language server to build the project
4. You should see IntelliSense working after initialization

### 5. Test Build Integration

Run these commands to test build integration:

```bash
# In Cursor terminal (Ctrl/Cmd + `)
./activator compile
./activator test
```

Or use the built-in tasks:

- `Ctrl/Cmd + Shift + P` → "Tasks: Run Task" → "sbt: compile"

### 6. Configure Debugging

#### Option A: Using run.sh script (Recommended)

1. **Start the Play app with debug enabled:**

   ```bash
   ./run.sh --no-browser
   ```

   Wait for "Listening for transport dt_socket at address: 9999"

2. **Attach debugger in Cursor:**
   - Go to Run and Debug view (`Ctrl/Cmd + Shift + D`)
   - Select "Attach to Play App (use with run.sh)"
   - Click "Start Debugging" (▶️)
   - You should see "Debugger attached" message

#### Option B: Using VS Code Tasks

1. **Run the debug task:**

   - `Ctrl/Cmd + Shift + P` → "Tasks: Run Task" → "Start Play App with run.sh"

2. **Then attach debugger** (same as Option A step 2)

#### Setting Breakpoints

- Click in the left margin of any Java file to set breakpoints
- Breakpoints will be hit when you interact with the running application
- Variables and call stack will appear in the Debug sidebar

### 7. Test Linting Features

1. Open any Java file with code issues
2. You should see:
   - ✅ Checkstyle warnings/errors
   - ✅ SonarLint code smell detection
   - ✅ SpotBugs security warnings
   - ✅ Java compilation errors

### 8. Code Navigation Features

After setup, you should have:

- ✅ **Go to Definition** (`F12` or `Ctrl/Cmd + Click`)
- ✅ **Find All References** (`Shift + F12`)
- ✅ **Auto-completion** (`Ctrl + Space`)
- ✅ **Quick fixes** (`Ctrl/Cmd + .`)
- ✅ **Symbol search** (`Ctrl/Cmd + T`)
- ✅ **Outline view** (Explorer sidebar)

## Troubleshooting

### ⚠️ Metals Doctor Error - "No build targets detected"

If you see the Metals Doctor error message stating "No build targets were detected in this workspace so most functionality won't work", follow these steps:

1. **The issue**: Metals (Scala Language Server) is conflicting with Java Language Server

   - Metals is designed for Scala projects, not Java Play Framework
   - It's trying to use Java 17 instead of Java 8

2. **Remove conflicting cache directories**:

   ```bash
   rm -rf .metals .bloop target/streams
   ```

3. **Clean and regenerate the project**:

   ```bash
   ./activator clean
   ./activator eclipse
   ./activator compile
   ```

4. **Disable Metals extension** (if installed):

   - Go to Extensions (`Ctrl/Cmd + Shift + X`)
   - Search for "Scala (Metals)"
   - Disable or uninstall the extension

5. **Reload Cursor completely**:

   - `Ctrl/Cmd + Shift + P` → "Developer: Reload Window"

6. **Verify Java Language Server is active**:
   - Open any Java file (e.g., `app/models/Unit.java`)
   - Check bottom-right status bar for "Java Projects"
   - Should show Java 8 being used, not Java 17

### Java Language Server Not Working

1. Check Java installation:

   ```bash
   java -version  # Should show Java 8
   echo $JAVA_HOME
   ```

2. Reload window: `Ctrl/Cmd + Shift + P` → "Developer: Reload Window"

3. Clean workspace: `Ctrl/Cmd + Shift + P` → "Java: Clean Workspace"

4. Force Java Language Server restart:
   - `Ctrl/Cmd + Shift + P` → "Java: Restart Language Server"

### Project Not Building

1. Ensure dependencies are installed:

   ```bash
   ./activator update
   ./activator reload
   ```

2. Check if MySQL is running (required for tests):
   ```bash
   brew services start mysql@8.0
   ```

### Linting Not Working

1. Restart Cursor after installing extensions
2. Check extension status in Extensions view
3. Verify configuration files exist in project root

### Performance Issues

1. Exclude build directories from file watchers (already configured)
2. Increase JVM memory for language server:
   ```json
   "java.jdt.ls.vmargs": "-Xmx2G"
   ```

## Additional Features

### Custom Code Snippets

Create Java snippets: `Ctrl/Cmd + Shift + P` → "Preferences: Configure User Snippets" → "java"

### Git Integration

- Built-in Git support with GitLens extension
- View file history, blame annotations, and commit details
- Stage/unstage changes directly in editor

### Database Integration

Install MySQL extension for database queries:

```bash
# ext install mtxr.sqltools
# ext install mtxr.sqltools-driver-mysql
```

## Project-Specific Commands

### Available SBT Tasks

- `./activator compile` - Compile the project
- `./activator test` - Run tests
- `./activator run` - Start the Play application
- `./activator jacoco` - Run tests with coverage
- `./activator eclipse` - Regenerate Eclipse metadata

### Formatting Commands

- `npm run format:play` - Format Java files with Prettier
- `npm run format:check` - Check formatting without modifying files

## Verification Checklist

After completing setup, verify these features work:

- [ ] Auto-completion when typing Java code
- [ ] Error highlighting for syntax errors
- [ ] Go to definition on method calls
- [ ] Find references for classes and methods
- [ ] Debugging with breakpoints
- [ ] Build tasks execution
- [ ] Linting warnings in Problems panel
- [ ] Code formatting on save

## Support

If you encounter issues:

1. Check the Output panel (`View` → `Output`) for error messages
2. Look at Java language server logs
3. Verify all extensions are installed and enabled
4. Restart Cursor and try again

---

**Setup Complete!** 🎉

Your Java Play Framework project is now optimized for development with Cursor IDE, featuring full IntelliSense, linting, debugging, and build integration.
