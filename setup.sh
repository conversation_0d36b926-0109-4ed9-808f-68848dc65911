#!/bin/bash
echo "Starting setup..."

echo "Configuring git..."
git config user.name "[Your first name] [Your last name]"
git config user.email "[<EMAIL>]"

echo "Installing and configuring Java..."
brew install jenv

echo 'export PATH="$HOME/.jenv/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(jenv init -)"' >> ~/.zshrc
source ~/.zshrc

brew tap adoptopenjdk/openjdk
brew install --cask adoptopenjdk8

java_version=$(cat .java-version)
java_home=$(/usr/libexec/java_home -v "$java_version")
jenv add "$java_home"
jenv global "$java_version"

echo "Installing MySQL 8.0..."
brew install mysql@8.0
brew services start mysql@8.0
echo 'alias mysql="$HOMEBREW_PREFIX/opt/mysql@8.0/bin/mysql"' >> ~/.zshrc
source ~/.zshrc
echo "Creating database..."
mysql -u root -e "CREATE DATABASE IF NOT EXISTS lavomat_dev;"

echo "Installing Node and NVM..."
brew install nvm
echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.zshrc
echo '[ -s "$HOMEBREW_PREFIX/opt/nvm/nvm.sh" ] && . "$HOMEBREW_PREFIX/opt/nvm/nvm.sh"' >> ~/.zshrc
echo '[ -s "$HOMEBREW_PREFIX/opt/nvm/etc/bash_completion.d/nvm" ] && . "$HOMEBREW_PREFIX/opt/nvm/etc/bash_completion.d/nvm"' >> ~/.zshrc
source ~/.zshrc
nvm install

echo "Copying configuration files..."
cp conf/application.conf.example conf/application.conf

echo "Installing play4jpa..."
cd repo/play4jpa/
../../activator publishLocal
cd ../../

chmod +x ./run.sh

echo "Installing frontend tools..."
npm install bower -g
npm install grunt -g

npm install

echo "Installing Bower components..."
cd ./public/
bower install
cd ..

echo "Running Grunt dist..."
grunt dist

echo "Setup completed successfully!"
