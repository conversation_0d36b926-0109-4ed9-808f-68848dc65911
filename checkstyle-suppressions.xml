<?xml version="1.0"?>
<!DOCTYPE suppressions PUBLIC
    "-//Checkstyle//DTD SuppressionFilter Configuration 1.2//EN"
    "https://checkstyle.org/dtds/suppressions_1_2.dtd">

<suppressions>
    <!-- Suppress checks for generated files -->
    <suppress files="app[/\\]com[/\\].*\.java" checks=".*"/>
    <suppress files="app[/\\]org[/\\].*\.java" checks=".*"/>
    <suppress files="target[/\\].*\.java" checks=".*"/>

    <!-- Suppress Javadoc checks for test files -->
    <suppress files="test[/\\].*\.java" checks="JavadocMethod"/>
    <suppress files="test[/\\].*\.java" checks="JavadocType"/>
    <suppress files="test[/\\].*\.java" checks="JavadocVariable"/>
    <suppress files="test[/\\].*\.java" checks="MissingJavadocMethod"/>

    <!-- Suppress specific checks for models (JPA entities) -->
    <suppress files="app[/\\]models[/\\].*\.java" checks="HiddenField"/>
    <suppress files="app[/\\]models[/\\].*\.java" checks="DesignForExtension"/>
    <suppress files="app[/\\]models[/\\].*\.java" checks="FinalParameters"/>

    <!-- Suppress magic number checks for controllers -->
    <suppress files="app[/\\].*Controller\.java" checks="MagicNumber"/>

    <!-- Play Framework specific suppressions -->
    <suppress files="app[/\\]controllers[/\\].*\.java" checks="DesignForExtension"/>
    <suppress files="app[/\\]services[/\\].*\.java" checks="DesignForExtension"/>
</suppressions>
