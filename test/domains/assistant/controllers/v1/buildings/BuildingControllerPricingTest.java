package domains.assistant.controllers.v1.buildings;

import static org.junit.Assert.*;

import common.helpers.QueryParam;
import factories.*;
import global.APIException;
import models.*;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class BuildingControllerPricingTest extends common.RequestBaseTest {

    private void getPricing(String slug, int level) {
        Call call = generateCall("GET", "/asst/v1/buildings/" + slug + "/pricing");

        QueryParam queryString = new QueryParam("level", level);

        request(call, queryString);
    }

    @Test
    public void testGetInfoLevelZero() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().with("building", building).create();

        getPricing(building.getSlug(), 0);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());

        Rate rate = building.getRate();
        assertEquals(rate.getName(), json().get(0).get("name").asText());
    }

    @Test
    public void testGetInfoLevelOne() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().with("building", building).create();

        getPricing(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());

        Rate rate = reload(building.getRate());
        assertEquals(rate.getName(), json().get(0).get("name").asText());

        assertEquals(machine.getCapacity(), json().get(0).get("capacity").asInt());
        assertEquals(rate.getPriceCustomer(), json().get(0).get("priceCustomer").asDouble(), 0.01);
        assertFalse(json().get(0).get("hasSoapDispenser").asBoolean());
    }

    @Test
    public void testGetInfoLevelOneWithSoapDispenser() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().with("building", building).create();
        SoapDispenser soapDispenser = new SoapDispenserFactory().with("machine", machine).create();

        getPricing(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());

        Rate rate = reload(building.getRate());
        assertEquals(rate.getName(), json().get(0).get("name").asText());

        assertEquals(machine.getCapacity(), json().get(0).get("capacity").asInt());
        assertEquals(rate.getPriceCustomer(), json().get(0).get("priceCustomer").asDouble(), 0.01);
        assertTrue(json().get(0).get("hasSoapDispenser").asBoolean());
    }

    @Test
    public void testGetInfoLevelOneWithDifferentCapacities() throws FactoryException {
        Rate buildingRate = new RateFactory().create();
        RateEvent buildingRateEvent = new RateEventFactory().withRate(buildingRate).create();
        Building building = new BuildingFactory().with("rate", buildingRate).create();
        Machine machine = new MachineFactory().with("building", building).create();

        Rate biggerMachineRate = new RateFactory().create();
        RateEvent biggerMachineRateEvent = new RateEventFactory()
            .withRate(biggerMachineRate)
            .create();
        Machine biggerMachine = new MachineFactory()
            .with("machineRate", biggerMachineRate)
            .with("building", building)
            .with("capacity", machine.getCapacity() * 2)
            .create();

        getPricing(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(2, jsonSize());

        reload(buildingRate);
        reload(biggerMachineRate);

        int buildingIndex, biggerMachineIndex;
        if (buildingRate.getName().equals(json().get(0).get("name").asText())) {
            buildingIndex = 0;
            biggerMachineIndex = 1;
        } else {
            buildingIndex = 1;
            biggerMachineIndex = 0;
        }

        assertEquals(buildingRate.getName(), json().get(buildingIndex).get("name").asText());
        assertEquals(machine.getCapacity(), json().get(buildingIndex).get("capacity").asInt());
        assertEquals(
            buildingRate.getPriceCustomer(),
            json().get(buildingIndex).get("priceCustomer").asDouble(),
            0.01
        );

        assertEquals(
            biggerMachineRate.getName(),
            json().get(biggerMachineIndex).get("name").asText()
        );
        assertEquals(
            biggerMachine.getCapacity(),
            json().get(biggerMachineIndex).get("capacity").asInt()
        );
        assertEquals(
            biggerMachineRate.getPriceCustomer(),
            json().get(biggerMachineIndex).get("priceCustomer").asDouble(),
            0.01
        );
    }

    @Test
    public void testGetInfoLevelOneWithDifferentSoapDispenserAvailability()
        throws FactoryException {
        Rate buildingRate = new RateFactory().create();
        RateEvent buildingRateEvent = new RateEventFactory().withRate(buildingRate).create();
        Building building = new BuildingFactory().with("rate", buildingRate).create();
        Machine machine = new MachineFactory().with("building", building).create();

        Rate soapDispenserMachineRate = new RateFactory().create();
        RateEvent soapDispenserMachineRateEvent = new RateEventFactory()
            .withRate(soapDispenserMachineRate)
            .create();
        Machine soapDispenserMachine = new MachineFactory()
            .with("machineRate", soapDispenserMachineRate)
            .with("building", building)
            .create();
        SoapDispenser soapDispenser = new SoapDispenserFactory()
            .with("machine", soapDispenserMachine)
            .create();

        getPricing(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(2, jsonSize());

        reload(buildingRate);
        reload(soapDispenserMachineRate);

        int buildingIndex, soapDispenserMachineIndex;
        if (buildingRate.getName().equals(json().get(0).get("name").asText())) {
            buildingIndex = 0;
            soapDispenserMachineIndex = 1;
        } else {
            buildingIndex = 1;
            soapDispenserMachineIndex = 0;
        }

        assertEquals(buildingRate.getName(), json().get(buildingIndex).get("name").asText());
        assertEquals(
            buildingRate.getPriceCustomer(),
            json().get(buildingIndex).get("priceCustomer").asDouble(),
            0.01
        );
        assertFalse(json().get(buildingIndex).get("hasSoapDispenser").asBoolean());

        assertEquals(
            soapDispenserMachineRate.getName(),
            json().get(soapDispenserMachineIndex).get("name").asText()
        );
        assertEquals(
            soapDispenserMachineRate.getPriceCustomer(),
            json().get(soapDispenserMachineIndex).get("priceCustomer").asDouble(),
            0.01
        );
        assertTrue(json().get(soapDispenserMachineIndex).get("hasSoapDispenser").asBoolean());
    }

    @Test
    public void testGetInfoLevelOneUniqueValues() throws FactoryException {
        Rate buildingRate = new RateFactory().create();
        RateEvent buildingRateEvent = new RateEventFactory().withRate(buildingRate).create();
        Building building = new BuildingFactory().with("rate", buildingRate).create();
        Machine machine = new MachineFactory().with("building", building).create();
        Machine machineDuplicated = new MachineFactory()
            .with("building", building)
            .with("capacity", machine.getCapacity())
            .create();

        Rate biggerMachineRate = new RateFactory().create();
        RateEvent biggerMachineRateEvent = new RateEventFactory()
            .withRate(biggerMachineRate)
            .create();
        Machine biggerMachine = new MachineFactory()
            .with("machineRate", biggerMachineRate)
            .with("building", building)
            .with("capacity", machine.getCapacity() * 2)
            .create();
        Machine biggerMachineDuplicated = new MachineFactory()
            .with("machineRate", biggerMachineRate)
            .with("building", building)
            .with("capacity", machine.getCapacity() * 2)
            .create();

        getPricing(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(2, jsonSize());

        reload(buildingRate);
        reload(biggerMachineRate);

        int buildingIndex, biggerMachineIndex;
        if (buildingRate.getName().equals(json().get(0).get("name").asText())) {
            buildingIndex = 0;
            biggerMachineIndex = 1;
        } else {
            buildingIndex = 1;
            biggerMachineIndex = 0;
        }

        assertEquals(buildingRate.getName(), json().get(buildingIndex).get("name").asText());
        assertEquals(machine.getCapacity(), json().get(buildingIndex).get("capacity").asInt());
        assertEquals(
            buildingRate.getPriceCustomer(),
            json().get(buildingIndex).get("priceCustomer").asDouble(),
            0.01
        );

        assertEquals(
            biggerMachineRate.getName(),
            json().get(biggerMachineIndex).get("name").asText()
        );
        assertEquals(
            biggerMachine.getCapacity(),
            json().get(biggerMachineIndex).get("capacity").asInt()
        );
        assertEquals(
            biggerMachineRate.getPriceCustomer(),
            json().get(biggerMachineIndex).get("priceCustomer").asDouble(),
            0.01
        );
    }

    @Test
    public void testGetInfoNotFound() {
        Building building = new BuildingFactory().build();

        getPricing(building.getSlug(), 0);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.message(),
            json().get("result_message").asText()
        );
    }
}
