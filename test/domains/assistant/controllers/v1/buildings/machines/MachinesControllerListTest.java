package domains.assistant.controllers.v1.buildings.machines;

import static org.junit.Assert.*;

import com.fasterxml.jackson.databind.JsonNode;
import common.helpers.QueryParam;
import factories.*;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import models.*;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class MachinesControllerListTest extends common.RequestBaseTest {

    private void getList(String token, String buildingSlug) {
        Call call = generateCall("GET", "/asst/v1/buildings/" + buildingSlug + "/machines");

        QueryParam queryString = new QueryParam("level", "1");

        request(call, token, queryString);
    }

    @Test
    public void testGetMachines() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().washer().with("building", building).create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        JsonNode jsonMachine = json().get(0);
        assertEquals(machine.getSortIndex(), jsonMachine.get("index").asInt());
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("type").asText());
        assertEquals("AVAILABLE", jsonMachine.get("status").asText());
        assertEquals(0, jsonMachine.get("remainingTime").asLong());
        assertEquals(machine.getCapacity(), jsonMachine.get("capacity").asInt());

        JsonNode jsonRate = jsonMachine.get("rate");
        assertNotNull(jsonRate);
        Rate rate = reload(building.getRate());
        assertEquals(rate.getName(), jsonRate.get("name").asText());
        assertEquals(rate.getPriceCustomer(), jsonRate.get("priceCustomer").asDouble(), 0.01);
    }

    @Test
    public void testGetMachinesMultipleTypesSortBySortIndex() throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerSortIndex = 2;
        Machine washer = new MachineFactory()
            .washer()
            .with("sortIndex", washerSortIndex)
            .with("building", building)
            .create();
        int dryerSortIndex = 1;
        Machine dryer = new MachineFactory()
            .dryer()
            .with("sortIndex", dryerSortIndex)
            .with("building", building)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(2, jsonSize());
        JsonNode jsonDryer = json().get(0);
        assertEquals(dryerSortIndex, jsonDryer.get("index").asInt());
        assertEquals(dryer.getMachineType().toString(), jsonDryer.get("type").asText());
        assertEquals("AVAILABLE", jsonDryer.get("status").asText());
        assertEquals(0, jsonDryer.get("remainingTime").asLong());
        JsonNode jsonWasher = json().get(1);
        assertEquals(washerSortIndex, jsonWasher.get("index").asInt());
        assertEquals(washer.getMachineType().toString(), jsonWasher.get("type").asText());
        assertEquals("AVAILABLE", jsonWasher.get("status").asText());
        assertEquals(0, jsonWasher.get("remainingTime").asLong());
    }

    @Test
    public void testGetMachinesFilterDisabledSortIndex() throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerSortIndex = 0;
        Machine washerDisabled = new MachineFactory()
            .washer()
            .with("sortIndex", washerSortIndex)
            .with("building", building)
            .create();
        int dryerSortIndex = 1;
        Machine dryer = new MachineFactory()
            .dryer()
            .with("sortIndex", dryerSortIndex)
            .with("building", building)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        assertCollection(
            Arrays.asList(dryerSortIndex),
            jsonStream().map(x -> x.get("index").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testGetMachinesMultipleRates() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        int washerSortIndex = 2;
        Machine washer = new MachineFactory()
            .washer()
            .withRate()
            .with("sortIndex", washerSortIndex)
            .with("building", building)
            .create();
        int dryerSortIndex = 1;
        Machine dryer = new MachineFactory()
            .dryer()
            .with("sortIndex", dryerSortIndex)
            .with("building", building)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(2, jsonSize());
        JsonNode jsonDryer = json().get(0);
        assertEquals(dryerSortIndex, jsonDryer.get("index").asInt());
        assertEquals(dryer.getMachineType().toString(), jsonDryer.get("type").asText());
        assertEquals("AVAILABLE", jsonDryer.get("status").asText());
        assertEquals(0, jsonDryer.get("remainingTime").asLong());

        JsonNode jsonRate = jsonDryer.get("rate");
        assertNotNull(jsonRate);
        Rate buildingRate = reload(building.getRate());
        assertEquals(buildingRate.getName(), jsonRate.get("name").asText());
        assertEquals(
            buildingRate.getPriceCustomer(),
            jsonRate.get("priceCustomer").asDouble(),
            0.01
        );

        JsonNode jsonWasher = json().get(1);
        assertEquals(washerSortIndex, jsonWasher.get("index").asInt());
        assertEquals(washer.getMachineType().toString(), jsonWasher.get("type").asText());
        assertEquals("AVAILABLE", jsonWasher.get("status").asText());
        assertEquals(0, jsonWasher.get("remainingTime").asLong());

        jsonRate = jsonWasher.get("rate");
        assertNotNull(jsonRate);
        Rate machineRate = reload(washer.getMachineRate());
        assertEquals(machineRate.getName(), jsonRate.get("name").asText());
        assertEquals(
            machineRate.getPriceCustomer(),
            jsonRate.get("priceCustomer").asDouble(),
            0.01
        );
    }

    @Test
    public void testGetMachinesParentChildRelationship() throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerSortIndex = 2;
        Machine washer = new MachineFactory()
            .washer()
            .withoutKeepAlive()
            .with("sortIndex", washerSortIndex)
            .with("building", building)
            .create();
        int dryerSortIndex = 1;
        Machine dryer = new MachineFactory()
            .dryer()
            .with("sortIndex", dryerSortIndex)
            .with("building", building)
            .with("rpiChild", washer)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(2, jsonSize());
        assertCollection(
            Arrays.asList(dryerSortIndex, washerSortIndex),
            jsonStream().map(x -> x.get("index").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testGetMachinesParentChildRelationshipFilterDisabledSortIndex()
        throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerSortIndex = 0;
        Machine washer = new MachineFactory()
            .washer()
            .withoutKeepAlive()
            .with("sortIndex", washerSortIndex)
            .with("building", building)
            .create();
        int dryerSortIndex = 1;
        Machine dryer = new MachineFactory()
            .dryer()
            .with("sortIndex", dryerSortIndex)
            .with("building", building)
            .with("rpiChild", washer)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        assertCollection(
            Arrays.asList(dryerSortIndex),
            jsonStream().map(x -> x.get("index").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testGetMachinesBelongToBuilding() throws FactoryException {
        Building building = new BuildingFactory().create();
        List<Machine> machines = new MachineFactory().with("building", building).create(2);
        Building otherBuilding = new BuildingFactory().create();
        List<Machine> otherMachines = new MachineFactory()
            .with("sortIndex", () -> faker.number().randomDigitNotZero() * 100)
            .with("building", otherBuilding)
            .create(2);

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(machines.size() + otherMachines.size(), Machine.findAll().size());

        assertEquals(machines.size(), jsonSize());
        assertCollection(
            machines.stream().map(Machine::getSortIndex).collect(Collectors.toList()),
            jsonStream().map(x -> x.get("index").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testGetMachinesWithInUseStatusAndRemainingTime() throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerAverageUseTimeMinutes = 30;
        Machine machine = new MachineFactory()
            .washer()
            .with("building", building)
            .with("averageUseTime", washerAverageUseTimeMinutes)
            .create();
        int lastUsageTimeAgoMinutes = 2;
        new MachineUsesFactory()
            .withTimestampAgo(lastUsageTimeAgoMinutes, TimeUnit.MINUTES)
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        JsonNode jsonMachine = json().get(0);
        assertEquals(machine.getSortIndex(), jsonMachine.get("index").asInt());
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("type").asText());

        assertEquals("IN_USE", jsonMachine.get("status").asText());
        assertEqualsWithinDeviation(
            TimeUnit.MILLISECONDS.convert(
                washerAverageUseTimeMinutes - lastUsageTimeAgoMinutes,
                TimeUnit.MINUTES
            ),
            jsonMachine.get("remainingTime").asLong(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testGetMachinesWithAvailableStatusDespiteMachineUse() throws FactoryException {
        Building building = new BuildingFactory().create();
        int washerAverageUseTimeMinutes = 30;
        Machine machine = new MachineFactory()
            .washer()
            .with("building", building)
            .with("averageUseTime", washerAverageUseTimeMinutes)
            .create();
        int lastUsageTimeAgoMinutes = 40;
        new MachineUsesFactory()
            .withTimestampAgo(lastUsageTimeAgoMinutes, TimeUnit.MINUTES)
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        JsonNode jsonMachine = json().get(0);
        assertEquals(machine.getSortIndex(), jsonMachine.get("index").asInt());
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("type").asText());

        assertEquals("AVAILABLE", jsonMachine.get("status").asText());
        assertEquals(0, jsonMachine.get("remainingTime").asLong());
    }

    @Test
    public void testGetMachinesWithSoapDispensers() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().washer().with("building", building).create();
        SoapDispenser soapDispenser = new SoapDispenserFactory().with("machine", machine).create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        getList(token, building.getSlug());

        assertEquals(Http.Status.OK, status());

        assertEquals(1, jsonSize());
        JsonNode jsonMachine = json().get(0);
        assertEquals(machine.getSortIndex(), jsonMachine.get("index").asInt());
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("type").asText());
        assertEquals("AVAILABLE", jsonMachine.get("status").asText());
        assertEquals(0, jsonMachine.get("remainingTime").asLong());
        assertEquals(machine.getCapacity(), jsonMachine.get("capacity").asInt());
        assertTrue(jsonMachine.get("hasSoapDispensers").asBoolean());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        new UserFactory().create();

        getList(null, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
