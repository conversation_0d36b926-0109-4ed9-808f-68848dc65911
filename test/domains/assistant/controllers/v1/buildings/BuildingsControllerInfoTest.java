package domains.assistant.controllers.v1.buildings;

import static org.junit.Assert.*;

import common.helpers.QueryParam;
import factories.BuildingFactory;
import factories.FactoryException;
import factories.SoapDispenserFactory;
import global.APIException;
import models.Building;
import models.SoapDispenser;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class BuildingsControllerInfoTest extends common.RequestBaseTest {

    private void getInfo(String slug, int level) {
        Call call = generateCall("GET", "/asst/v1/buildings/" + slug);

        QueryParam queryString = new QueryParam("level", level);

        request(call, queryString);
    }

    @Test
    public void testGetInfoLevelZero() {
        Building building = new BuildingFactory().create();

        getInfo(building.getSlug(), 0);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertEquals(building.getName(), json().get("name").asText());
    }

    @Test
    public void testGetInfoLevelOne() {
        Building building = new BuildingFactory().create();

        getInfo(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertEquals(building.getCity(), json().get("city").asText());
        assertEquals(building.getBuildingType().toString(), json().get("type").asText());
        assertEquals(
            building.isRemoteActivationEnabled(),
            json().get("isRemoteActivationEnabled").asBoolean()
        );
    }

    @Test
    public void testGetInfoLevelOneWithRate() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();

        getInfo(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertEquals(
            reload(building.getRate()).getPriceCustomer(),
            json().get("rate").get("priceCustomer").asDouble(),
            0.001
        );
    }

    @Test
    public void testGetInfoLevelOneWithSoapDispenserIndicator() throws FactoryException {
        Building building = new BuildingFactory().withRate().withMachine().create();
        SoapDispenser soapDispenser = new SoapDispenserFactory()
            .with("machine", building.getMachines().get(0))
            .create();

        getInfo(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertTrue(json().get("hasSoapDispensers").asBoolean());
    }

    @Test
    public void testGetInfoLevelOneWithActivationDisabled() throws FactoryException {
        Building building = new BuildingFactory().with("isRemoteActivationEnabled", false).create();

        getInfo(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertFalse(json().get("isRemoteActivationEnabled").asBoolean());
    }

    @Test
    public void testGetInfoLevelOneWithActivationEnabled() throws FactoryException {
        Building building = new BuildingFactory().with("isRemoteActivationEnabled", true).create();

        getInfo(building.getSlug(), 1);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(building.getSlug(), json().get("slug").asText());
        assertTrue(json().get("isRemoteActivationEnabled").asBoolean());
    }

    @Test
    public void testGetInfoNotFound() {
        Building building = new BuildingFactory().build();

        getInfo(building.getSlug(), 0);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.message(),
            json().get("result_message").asText()
        );
    }
}
