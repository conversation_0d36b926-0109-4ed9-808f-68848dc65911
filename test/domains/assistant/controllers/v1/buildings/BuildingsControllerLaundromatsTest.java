package domains.assistant.controllers.v1.buildings;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.fasterxml.jackson.databind.JsonNode;
import common.helpers.QueryParam;
import factories.BuildingFactory;
import factories.FactoryException;
import java.util.List;
import java.util.stream.Collectors;
import models.Building;
import models.BuildingType;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class BuildingsControllerLaundromatsTest extends common.RequestBaseTest {

    private void getLaundromats() {
        Call call = generateCall("GET", "/asst/v1/buildings/laundromats");

        QueryParam queryString = new QueryParam("level", "1");

        request(call, queryString);
    }

    @Test
    public void testGetLaundromats() {
        Building laundromat = new BuildingFactory().laundromat().withGoogleMapsSettings().create();
        Building building = new BuildingFactory().create();

        getLaundromats();

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(jsonSize(), 1);

        JsonNode first = json().get(0);
        assertEquals(laundromat.getName(), first.get("name").asText());
        JsonNode googleMaps = first.get("googleMaps");
        assertEquals(
            laundromat.getBuildingSetting().getGoogleMapsDescription(),
            googleMaps.get("name").asText()
        );
        assertEquals(
            laundromat.getBuildingSetting().getGoogleMapsLink(),
            googleMaps.get("link").asText()
        );
    }

    @Test
    public void testGetLaundromatsForMany() throws FactoryException {
        int itemCount = 3;
        List<Building> laundromats = new BuildingFactory()
            .with("buildingType", BuildingType.LAUNDROMAT)
            .create(itemCount);
        List<Building> buildings = new BuildingFactory().create(2);

        getLaundromats();

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(jsonSize(), itemCount);
        assertCollection(
            laundromats.stream().map(Building::getName).collect(Collectors.toList()),
            jsonStream().map(j -> j.get("name").asText()).collect(Collectors.toList())
        );
    }

    @Test
    public void testGetLaundromatsNonExist() {
        Building building = new BuildingFactory().laundromat().build();

        getLaundromats();

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 0);
    }
}
