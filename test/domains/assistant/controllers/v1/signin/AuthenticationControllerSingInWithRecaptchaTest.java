package domains.assistant.controllers.v1.signin;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static play.inject.Bindings.bind;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import factories.UserFactory;
import models.Session;
import models.User;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.api.inject.Binding;
import play.mvc.Call;
import play.mvc.Http;
import policies.ApiClient;
import services.google.GoogleReCaptchaService;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ GoogleReCaptchaService.class })
@PowerMockIgnore({ "javax.management.*" })
public class AuthenticationControllerSingInWithRecaptchaTest extends common.RequestBaseTest {

    GoogleReCaptchaService mockService;

    @Override
    public Binding<?>[] bindingInstances() {
        super.bindingInstances();
        this.mockService = PowerMock.createMock(GoogleReCaptchaService.class);
        return new Binding[] { bind(GoogleReCaptchaService.class).toInstance(this.mockService) };
    }

    private void postSignInWithRecaptcha(JsonNode json) {
        Call call = generateCall("POST", "/asst/v1/signin");

        request(call, null, json);
    }

    @Test
    public void testSignInWithRecaptcha() throws Exception {
        String password = faker.internet().password();
        User user = new UserFactory().withPassword(password).withAccount().create();
        String recaptchaToken = faker.internet().uuid();

        PowerMock.expectNew(GoogleReCaptchaService.class).andReturn(this.mockService);
        expect(
            this.mockService.validate(eq(recaptchaToken), anyString(), eq(ApiClient.ASSISTANT_WEB))
        )
            .andAnswer(() -> {
                String token = getCurrentArgument(0).toString();

                assertEquals(recaptchaToken, token);

                return true;
            })
            .times(1);
        PowerMock.replay(this.mockService, GoogleReCaptchaService.class);

        postSignInWithRecaptcha(jsonize(user, password, recaptchaToken));

        PowerMock.verify(this.mockService);
        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        reload(user);
        assertEquals(user.getFirstName(), json().get("account").get("user").get("name").asText());
        assertEquals(
            user.getLastName(),
            json().get("account").get("user").get("lastname").asText()
        );
        assertEquals(
            user.getEmailAddress(),
            json().get("account").get("user").get("email").asText()
        );
        assertEquals(
            user.getRole().toString(),
            json().get("account").get("user").get("role").asText()
        );
        Session session = user.getSessions().stream().reduce((first, second) -> second).get();
        assertNotNull(session);
        assertEquals(session.getToken(), json().get("token").asText());
    }

    @Test
    public void testSignInWithRecaptchaInvalidCredentials() throws Exception {
        String password = faker.internet().password();
        User user = new UserFactory().withPassword(password).withAccount().create();
        String recaptchaToken = faker.internet().uuid();
        String anotherPassword = faker.internet().password();

        PowerMock.expectNew(GoogleReCaptchaService.class).andReturn(this.mockService);
        expect(
            this.mockService.validate(eq(recaptchaToken), anyString(), eq(ApiClient.ASSISTANT_WEB))
        )
            .andStubThrow(
                new AssertionError("GoogleReCaptchaService#validate should not be called")
            );
        PowerMock.replay(this.mockService, GoogleReCaptchaService.class);

        postSignInWithRecaptcha(jsonize(user, anotherPassword, recaptchaToken));

        PowerMock.verify(this.mockService);
        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    @Test
    public void testSignInWithRecaptchaInvalidRecaptcha() throws Exception {
        String password = faker.internet().password();
        User user = new UserFactory().withPassword(password).withAccount().create();
        String recaptchaToken = faker.internet().uuid();

        PowerMock.expectNew(GoogleReCaptchaService.class).andReturn(this.mockService);
        expect(
            this.mockService.validate(eq(recaptchaToken), anyString(), eq(ApiClient.ASSISTANT_WEB))
        )
            .andAnswer(() -> {
                String token = getCurrentArgument(0).toString();

                assertEquals(recaptchaToken, token);

                return false;
            })
            .times(1);
        PowerMock.replay(this.mockService, GoogleReCaptchaService.class);

        postSignInWithRecaptcha(jsonize(user, password, recaptchaToken));

        PowerMock.verify(this.mockService);
        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(User user, String password, String recaptchaToken) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("emailAddress", user.getEmailAddress())
            .put("password", password)
            .put("recaptchaToken", recaptchaToken);
    }
}
