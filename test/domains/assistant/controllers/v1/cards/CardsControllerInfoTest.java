package domains.assistant.controllers.v1.cards;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Iterators;
import common.helpers.DateTimer;
import common.helpers.QueryParam;
import domains.assistant.dto.cards.GetCardByUidParameters;
import factories.*;
import global.exceptions.CardNotFoundException;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import models.*;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;
import services.google.GoogleReCaptchaService;

@RunWith(PowerMockRunner.class)
@PrepareForTest(GetCardByUidParameters.class)
@PowerMockIgnore("javax.management.*")
public class CardsControllerInfoTest extends common.RequestBaseTest {

    private void getInfo(String token, String uid, int level, String recaptchaToken) {
        Call call = generateCall("GET", "/asst/v1/cards/" + uid);

        QueryParam[] queryString = new QueryParam[] {
            new QueryParam("level", level),
            new QueryParam("recaptchaToken", recaptchaToken),
        };

        request(call, token, queryString);
    }

    private GoogleReCaptchaService mockRecaptchaService(
        String recaptchaToken,
        boolean returnValue,
        int calls
    ) throws Exception {
        GoogleReCaptchaService mockService = PowerMock.createMock(GoogleReCaptchaService.class);
        PowerMock.expectNew(GoogleReCaptchaService.class).andReturn(mockService);
        if (calls > 0) {
            expect(mockService.validate(eq(recaptchaToken), anyString()))
                .andAnswer(() -> {
                    String token = getCurrentArgument(0).toString();

                    assertEquals(recaptchaToken, token);

                    return returnValue;
                })
                .times(calls);
        } else {
            expect(mockService.validate(eq(recaptchaToken), anyString()))
                .andStubThrow(
                    new AssertionError("GoogleReCaptchaService#validate should not be called")
                );
        }
        PowerMock.replay(mockService, GoogleReCaptchaService.class);

        return mockService;
    }

    @Test
    public void testGetInfoLevelZero() throws Exception {
        Card card = new CardFactory().create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 0, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());
    }

    @Test
    public void testGetInfoLevelOne() throws Exception {
        Card card = new CardFactory().create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 1, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());

        assertEquals(card.getState().toString(), json().get("state").asText());
        assertEquals(card.getBalance(), json().get("balance").asDouble(), 0.01);
        assertEquals(card.getContractType().toString(), json().get("contractType").asText());
        assertEquals(card.belongsToLaundromat(), json().get("belongsToLaundromat").asBoolean());
    }

    @Test
    public void testGetInfoLevelTwo() throws Exception {
        Card card = new CardFactory().create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 2, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());
        assertEquals(card.getState().toString(), json().get("state").asText());
        assertEquals(card.getBalance(), json().get("balance").asDouble(), 0.01);
        assertEquals(card.getContractType().toString(), json().get("contractType").asText());
        assertNull(json().get("activity").get(0));
        assertNull(json().get("buildings").get(0));
    }

    @Test
    public void testGetInfoLevelTwoWithActivity() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().prepaid().with("unit", unit).create();
        Machine washer = new MachineFactory()
            .washer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        Machine dryer = new MachineFactory()
            .dryer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        // Activity
        Transaction t2 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(2, TimeUnit.DAYS))
            .create();
        MachineUse mu1 = new MachineUsesFactory()
            .with("machine", dryer)
            .with("card", card)
            .with("timestamp", DateTimer.ago(1, TimeUnit.DAYS))
            .create();
        Transaction t5 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(5, TimeUnit.DAYS))
            .create();
        MachineUse mu4 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("timestamp", DateTimer.ago(4, TimeUnit.DAYS))
            .create();
        MachineUse mu3 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("timestamp", DateTimer.ago(3, TimeUnit.DAYS))
            .create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 2, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());
        assertEquals(card.getState().toString(), json().get("state").asText());
        assertEquals(card.getBalance(), json().get("balance").asDouble(), 0.01);
        assertEquals(card.getContractType().toString(), json().get("contractType").asText());

        assertNotNull(json().get("activity").get(0));
        Iterator<JsonNode> activity = json().get("activity").elements();
        assertEquals(5, Iterators.size(activity));
        JsonNode record = json().get("activity").get(0);
        assertEquals("Secado", record.get("type").asText());
        assertEquals(mu1.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu1.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get("activity").get(1);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t2.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t2.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
        record = json().get("activity").get(2);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu3.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu3.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get("activity").get(3);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu4.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu4.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get("activity").get(4);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t5.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t5.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
    }

    @Test
    public void testGetInfoLevelTwoWithBuilding() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 2, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());
        assertEquals(card.getState().toString(), json().get("state").asText());
        assertEquals(card.getBalance(), json().get("balance").asDouble(), 0.01);
        assertEquals(card.getContractType().toString(), json().get("contractType").asText());

        assertNotNull(json().get("buildings").get(0));
        Iterator<JsonNode> buildings = json().get("buildings").elements();
        assertEquals(1, Iterators.size(buildings));
        JsonNode buildingJson = json().get("buildings").get(0);
        assertEquals(building.getName(), buildingJson.get("name").asText());
        assertEquals(
            reload(building.getRate()).getPriceCustomer(),
            buildingJson.get("rate").get("priceCustomer").asDouble(),
            0.001
        );
    }

    @Test
    public void testGetInfoNotFound() throws Exception {
        Card card = new CardFactory().prepaid().build();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 1);

        getInfo(null, card.getUuid(), 0, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(
            new CardNotFoundException().getErrorMessage(),
            json().get("result_message").asText()
        );
    }

    @Test
    public void testGetInfoMissingRecaptchaError() throws Exception {
        Card card = new CardFactory().create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, true, 0);

        getInfo(null, card.getUuid(), 0, null);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testGetInfoUnsafeRecaptchaError() throws Exception {
        Card card = new CardFactory().create();

        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = mockRecaptchaService(recaptchaToken, false, 1);

        getInfo(null, card.getUuid(), 0, recaptchaToken);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testGetInfoLevelZeroAuthenticated() throws Exception {
        Card card = new CardFactory().create();

        GoogleReCaptchaService mockService = mockRecaptchaService(null, false, 0);

        String token = new UserFactory().withAuthentication().authenticate();

        getInfo(token, card.getUuid(), 0, null);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(card.getUuid(), json().get("uid").asText());
    }
}
