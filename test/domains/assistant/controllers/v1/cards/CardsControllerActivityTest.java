package domains.assistant.controllers.v1.cards;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Iterators;
import common.helpers.DateTimer;
import common.helpers.QueryParam;
import domains.assistant.dto.cards.GetCardByUidParameters;
import factories.*;
import global.exceptions.CardNotFoundException;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import models.*;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;
import services.google.GoogleReCaptchaService;

@RunWith(PowerMockRunner.class)
@PrepareForTest(GetCardByUidParameters.class)
@PowerMockIgnore("javax.management.*")
public class CardsControllerActivityTest extends common.RequestBaseTest {

    private void getActivity(String token, String uid, String slug) {
        Call call = generateCall("GET", "/asst/v1/cards/" + uid + "/activity");

        QueryParam[] queryString = new QueryParam[] { new QueryParam("building", slug) };

        request(call, token, queryString);
    }

    private GoogleReCaptchaService mockRecaptchaService() throws Exception {
        String recaptchaToken = faker.internet().uuid();
        GoogleReCaptchaService mockService = PowerMock.createMock(GoogleReCaptchaService.class);
        PowerMock.expectNew(GoogleReCaptchaService.class).andReturn(mockService);
        expect(mockService.validate(eq(recaptchaToken), anyString()))
            .andStubThrow(
                new AssertionError("GoogleReCaptchaService#validate should not be called")
            );
        PowerMock.replay(mockService, GoogleReCaptchaService.class);

        return mockService;
    }

    @Test
    public void testGetActivityLevelZero() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        Card card = new CardFactory().virtual().withOwner(user).create();
        // activity
        Building building = new BuildingFactory().withRate().laundromat().create();
        Machine washer = new MachineFactory()
            .washer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        Machine dryer = new MachineFactory()
            .dryer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        Transaction t3 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(3, TimeUnit.DAYS))
            .create();
        MachineUse mu1 = new MachineUsesFactory()
            .with("machine", dryer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(1, TimeUnit.DAYS))
            .create();
        Transaction t7 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(7, TimeUnit.DAYS))
            .create();
        MachineUse mu6 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(6, TimeUnit.DAYS))
            .create();
        MachineUse mu4 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(4, TimeUnit.DAYS))
            .create();
        Building anotherBuilding = new BuildingFactory().withRate().laundromat().create();
        Machine anotherDryer = new MachineFactory()
            .dryer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", anotherBuilding)
            .create();
        Transaction t2 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(2, TimeUnit.DAYS))
            .create();
        MachineUse mu5 = new MachineUsesFactory()
            .with("machine", anotherDryer)
            .with("card", card)
            .with("building", anotherBuilding)
            .with("timestamp", DateTimer.ago(5, TimeUnit.DAYS))
            .create();

        GoogleReCaptchaService mockService = mockRecaptchaService();

        String token = factory.decorate().getToken();

        getActivity(token, card.getUuid(), null);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        Iterator<JsonNode> activity = json().elements();
        assertEquals(7, Iterators.size(activity));

        JsonNode record = json().get(0);
        assertEquals("Secado", record.get("type").asText());
        assertEquals(mu1.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu1.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(1);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t2.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t2.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
        record = json().get(2);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t3.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t3.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
        record = json().get(3);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu4.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu4.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(4);
        assertEquals("Secado", record.get("type").asText());
        assertEquals(mu5.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu5.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(5);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu6.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu6.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(6);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t7.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t7.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
    }

    @Test
    public void testGetActivityLevelZeroFilteredByBuilding() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        Card card = new CardFactory().virtual().withOwner(user).create();
        // activity
        Building building = new BuildingFactory().withRate().laundromat().create();
        Machine washer = new MachineFactory()
            .washer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        Machine dryer = new MachineFactory()
            .dryer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", building)
            .create();
        Transaction t3 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(3, TimeUnit.DAYS))
            .create();
        MachineUse mu1 = new MachineUsesFactory()
            .with("machine", dryer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(1, TimeUnit.DAYS))
            .create();
        Transaction t7 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(7, TimeUnit.DAYS))
            .create();
        MachineUse mu6 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(6, TimeUnit.DAYS))
            .create();
        MachineUse mu4 = new MachineUsesFactory()
            .with("machine", washer)
            .with("card", card)
            .with("building", building)
            .with("timestamp", DateTimer.ago(4, TimeUnit.DAYS))
            .create();
        Building anotherBuilding = new BuildingFactory().withRate().laundromat().create();
        Machine anotherDryer = new MachineFactory()
            .dryer()
            .with("lastAlive", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("building", anotherBuilding)
            .create();
        Transaction t2 = new TransactionFactory()
            .confirmed()
            .with("uid", card.getUuid())
            .with("creationDate", DateTimer.ago(2, TimeUnit.DAYS))
            .create();
        MachineUse anotherMachineUse5 = new MachineUsesFactory()
            .with("machine", anotherDryer)
            .with("card", card)
            .with("building", anotherBuilding)
            .with("timestamp", DateTimer.ago(5, TimeUnit.DAYS))
            .create();

        GoogleReCaptchaService mockService = mockRecaptchaService();

        String token = factory.decorate().getToken();

        getActivity(token, card.getUuid(), building.getSlug());

        PowerMock.verify(mockService);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        Iterator<JsonNode> activity = json().elements();
        assertEquals(6, Iterators.size(activity));

        JsonNode record = json().get(0);
        assertEquals("Secado", record.get("type").asText());
        assertEquals(mu1.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu1.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(1);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t2.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t2.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
        record = json().get(2);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t3.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t3.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
        record = json().get(3);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu4.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu4.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(4);
        assertEquals("Lavado", record.get("type").asText());
        assertEquals(mu6.getPriceCustomerWithDiscount(), record.get("amount").asDouble(), 0.001);
        assertEquals(mu6.getTimestamp().getTime(), record.get("timestamp").asLong());
        record = json().get(5);
        assertEquals("Acreditación", record.get("type").asText());
        assertEquals(t7.getAmount(), record.get("amount").asDouble(), 0.001);
        assertEquals(
            new DateTime(t7.getCreationDate()).minusHours(3).getMillis(),
            record.get("timestamp").asLong()
        );
    }

    @Test
    public void testGetActivityNotFound() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        Card card = new CardFactory().virtual().build();

        GoogleReCaptchaService mockService = mockRecaptchaService();

        String token = factory.decorate().getToken();

        getActivity(token, card.getUuid(), null);

        PowerMock.verify(mockService);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(
            new CardNotFoundException().getErrorMessage(),
            json().get("result_message").asText()
        );
    }

    @Test
    public void testUnauthorized() {
        Card card = new CardFactory().create();

        getActivity(null, card.getUuid(), null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
