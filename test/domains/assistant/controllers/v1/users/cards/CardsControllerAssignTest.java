package domains.assistant.controllers.v1.users.cards;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import factories.*;
import models.Building;
import models.Card;
import models.Unit;
import models.User;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class CardsControllerAssignTest extends common.RequestBaseTest {

    private void postAssign(String token, JsonNode json) {
        Call call = generateCall("POST", "/asst/v1/users/cards");

        request(call, token, json);
    }

    @Test
    public void testAssignPrepaidCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        String alias = faker.lorem().word();

        Card card = new CardFactory().prepaid().create();

        String token = factory.decorate().getToken();
        postAssign(token, jsonize(card, alias));

        assertEquals(Http.Status.CREATED, status());
        reload(card);
        assertEquals(user.getId(), card.getOwner().getId());
        assertEquals(user.getId(), card.getPrePaidCardholder().getId());
        assertEquals(alias, card.getAlias());
    }

    @Test
    public void testAssignPostpaidCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        String alias = faker.lorem().word();

        Building building = new BuildingFactory().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();

        String token = factory.decorate().getToken();
        postAssign(token, jsonize(card, alias));

        assertEquals(Http.Status.CREATED, status());
        reload(unit);
        reload(card);
        assertEquals(user.getId(), card.getOwner().getId());
        assertEquals(user.getId(), unit.getOwner().getId());
        assertEquals(alias, card.getAlias());
    }

    @Test
    public void testAssignVirtualCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        Card card = new CardFactory().prepaid().virtual().create();
        String alias = faker.lorem().word();

        String token = factory.decorate().getToken();
        postAssign(token, jsonize(card, alias));

        assertEquals(Http.Status.CREATED, status());
        reload(card);
        assertEquals(user.getId(), card.getOwner().getId());
        assertEquals(user.getId(), card.getPrePaidCardholder().getId());
        assertEquals(alias, card.getAlias());
    }

    @Test
    public void testAssignPrepaidCardByOwner() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User owner = factory.create();
        String alias = faker.lorem().word();

        Card card = new CardFactory().prepaid().withOwner(owner).create();

        String token = factory.decorate().getToken();
        postAssign(token, jsonize(card, alias));

        assertEquals(Http.Status.CREATED, status());
        reload(card);
        assertEquals(owner.getId(), card.getOwner().getId());
        assertEquals(owner.getId(), card.getPrePaidCardholder().getId());
        assertEquals(alias, card.getAlias());
    }

    @Test
    public void testAssignCardForbidden() throws FactoryException {
        User owner = new UserFactory().create();
        Card card = new CardFactory().prepaid().withOwner(owner).create();

        String token = new UserFactory().withAuthentication().authenticate();
        postAssign(token, jsonize(card, "fake-alias"));

        assertEquals(Http.Status.FORBIDDEN, status());

        // card was not changed
        reload(card);
        assertEquals(owner.getId(), card.getOwner().getId());
        assertNotEquals("fake-alias", card.getAlias());
    }

    @Test
    public void testCardNotFound() throws FactoryException {
        Card card = new CardFactory().prepaid().build();

        String token = new UserFactory().withAuthentication().authenticate();
        postAssign(token, jsonize(card, card.getAlias()));

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    private JsonNode jsonize(Card card, String alias) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper.createObjectNode().put("uid", card.getUuid()).put("alias", alias);
    }
}
