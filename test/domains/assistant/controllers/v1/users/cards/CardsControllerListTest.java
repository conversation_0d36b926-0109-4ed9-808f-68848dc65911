package domains.assistant.controllers.v1.users.cards;

import static org.junit.Assert.assertEquals;

import factories.*;
import java.util.Arrays;
import java.util.stream.Collectors;
import models.Building;
import models.Card;
import models.Unit;
import models.User;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class CardsControllerListTest extends common.RequestBaseTest {

    private void getList(String token) {
        Call call = generateCall("GET", "/asst/v1/users/cards?level=2");

        request(call, token);
    }

    @Test
    public void testListsPostpaidCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();

        String token = factory.decorate().getToken();
        getList(token);

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 1);
        assertEquals(card.getUuid(), json().get(0).get("uid").asText());
        assertEquals(unit.getNumber(), json().get(0).get("unit").get("number").asText());
    }

    @Test
    public void testListPrepaidCards() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Card card = new CardFactory().prepaid().with("prePaidCardholder", user).create();

        String token = factory.decorate().getToken();
        getList(token);

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 1);
        assertEquals(card.getUuid(), json().get(0).get("uid").asText());
    }

    @Test
    public void testListVirtualCards() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Card card = new CardFactory().prepaid().virtual().with("prePaidCardholder", user).create();

        String token = factory.decorate().getToken();
        getList(token);

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 1);
        // response does not include 0x
        assertEquals(card.getUuid().substring(2), json().get(0).get("uid").asText());
    }

    @Test
    public void testListMixedCards() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card postpaidCard = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(postpaidCard);
        unit.update();
        Card prepaidCard = new CardFactory().prepaid().with("prePaidCardholder", user).create();
        Card virtualCard = new CardFactory()
            .prepaid()
            .virtual()
            .with("prePaidCardholder", user)
            .create();

        String token = factory.decorate().getToken();
        getList(token);

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 3);
        assertCollection(
            Arrays.asList(
                postpaidCard.getUuid(),
                prepaidCard.getUuid(),
                virtualCard.getUuid().substring(2)
            ),
            jsonStream().map(j -> j.get("uid").asText()).collect(Collectors.toList())
        );
    }

    @Test
    public void testListCardNotBelongingToUser() throws FactoryException {
        User user = new UserFactory().create();
        Card card = new CardFactory().prepaid().with("prePaidCardholder", user).create();

        String token = new UserFactory().withAuthentication().authenticate();

        getList(token);

        assertEquals(Http.Status.OK, status());
        assertEquals(jsonSize(), 0);
    }

    @Test
    public void testUnauthorized() throws FactoryException {
        User user = new UserFactory().create();
        Card card = new CardFactory().prepaid().with("prePaidCardholder", user).create();

        getList(null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
