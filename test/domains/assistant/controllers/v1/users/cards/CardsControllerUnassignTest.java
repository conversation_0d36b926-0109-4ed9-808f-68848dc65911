package domains.assistant.controllers.v1.users.cards;

import static org.junit.Assert.*;

import factories.*;
import models.Building;
import models.Card;
import models.Unit;
import models.User;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class CardsControllerUnassignTest extends common.RequestBaseTest {

    private void deleteUnassign(String token, String uid) {
        Call call = generateCall("DELETE", "/asst/v1/users/cards/" + uid);

        request(call, token);
    }

    @Test
    public void testUnassignPrepaidCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Card card = new CardFactory().prepaid().withOwner(user).create();

        assertNotNull(card.getOwner());
        assertNotNull(card.getPrePaidCardholder());

        String token = factory.decorate().getToken();
        deleteUnassign(token, card.getUuid());

        assertEquals(Http.Status.NO_CONTENT, status());
        reload(card);
        assertNull(card.getOwner());
        assertNull(card.getPrePaidCardholder());
    }

    @Test
    public void testUnassignPostpaidCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();

        assertNotNull(card.getOwner());
        assertNotNull(unit.getOwner());

        String token = factory.decorate().getToken();
        deleteUnassign(token, card.getUuid());

        assertEquals(Http.Status.NO_CONTENT, status());
        reload(card);
        reload(unit);
        assertNull(card.getOwner());
        assertNull(unit.getOwner());
    }

    @Test
    public void testUnassignVirtualCard() throws FactoryException {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        Card card = new CardFactory().prepaid().virtual().withOwner(user).create();

        assertNotNull(card.getOwner());
        assertNotNull(card.getPrePaidCardholder());

        String token = factory.decorate().getToken();
        deleteUnassign(token, card.getUuid());

        assertEquals(Http.Status.NO_CONTENT, status());
        reload(card);
        assertNull(card.getOwner());
        assertNull(card.getPrePaidCardholder());
    }

    @Test
    public void testUnassignCardForbidden() throws FactoryException {
        User owner = new UserFactory().create();
        Card card = new CardFactory().prepaid().withOwner(owner).create();

        String token = new UserFactory().withAuthentication().authenticate();
        deleteUnassign(token, card.getUuid());

        assertEquals(Http.Status.FORBIDDEN, status());

        // card was not changed
        reload(card);
        assertEquals(owner.getId(), card.getOwner().getId());
    }

    @Test
    public void testCardNotFound() throws FactoryException {
        Card card = new CardFactory().prepaid().build();

        String token = new UserFactory().withAuthentication().authenticate();
        deleteUnassign(token, card.getUuid());

        assertEquals(Http.Status.NOT_FOUND, status());
    }
}
