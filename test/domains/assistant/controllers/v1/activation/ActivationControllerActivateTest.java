package domains.assistant.controllers.v1.activation;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import domains.activations.mqtt.LMMQTTProducer;
import factories.*;
import models.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;

@RunWith(PowerMockRunner.class)
@PrepareForTest(fullyQualifiedNames = "domains.activations.internal.Publisher")
@PowerMockIgnore("javax.management.*")
public class ActivationControllerActivateTest extends common.RequestBaseTest {

    private void postActivate(String token, JsonNode json) {
        Call call = generateCall("POST", "/asst/v1/activate");

        request(call, token, json);
    }

    @Test
    public void testActivateUnitOwner() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"0\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        expect(mockProducer.onPublishing(anyObject())).andReturn(mockProducer).times(1);
        expect(mockProducer.onPublished(anyObject())).andReturn(mockProducer).times(1);
        // this does not mean an error happened, it means the callback on error case was set
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer).times(1);

        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.OK, status());

        assertEquals(MachineUseResult.POSTPAID_ACTIVATION.getName(), json().get("result").asText());
        assertEquals(
            (int) MachineUseResult.POSTPAID_ACTIVATION.getCode(),
            json().get("code").asInt()
        );
        assertEquals(machine.getAverageUseTime(), json().get("cycleTime").asInt());
        AuditWorkflow lastAudit = AuditWorkflow
            .findAll()
            .stream()
            .reduce((first, second) -> second)
            .get();
        assertEquals(lastAudit.getId(), json().get("reference").asInt());
    }

    @Test
    public void testActivateCardOwner() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"1\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        expect(mockProducer.onPublishing(anyObject())).andReturn(mockProducer).times(1);
        expect(mockProducer.onPublished(anyObject())).andReturn(mockProducer).times(1);
        // this does not mean an error happened, it means the callback on error case was set
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer).times(1);

        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.OK, status());

        assertEquals(
            MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE.getName(),
            json().get("result").asText()
        );
        assertEquals(
            (int) MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE.getCode(),
            json().get("code").asInt()
        );
        assertEquals(machine.getAverageUseTime(), json().get("cycleTime").asInt());
        AuditWorkflow lastAudit = AuditWorkflow
            .findAll()
            .stream()
            .reduce((first, second) -> second)
            .get();
        assertEquals(lastAudit.getId(), json().get("reference").asInt());
    }

    @Test
    public void testActivateWithBuildingNotFound() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().build();
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", 1000)
            .create();
        Machine machine = new MachineFactory().create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("BUILDING_NOT_FOUND", json().get("result_message").asText());
    }

    @Test
    public void testActivateWithMachineNotFound() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).build();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("MACHINE_NOT_FOUND", json().get("result_message").asText());
    }

    @Test
    public void testActivateWithMachinePlacedInAnotherBuilding() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Building anotherBuilding = new BuildingFactory().laundromat().withRate().create();
        Machine machine = new MachineFactory().with("building", anotherBuilding).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("MACHINE_NOT_FOUND", json().get("result_message").asText());
    }

    @Test
    public void testActivateWithDisabledMachine() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory()
            .with("sortIndex", 0)
            .with("building", building)
            .create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testActivateWithCardNotBelongToUser() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();
        User anotherUser = new UserFactory().create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(anotherUser)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("CARD_NOT_FOUND", json().get("result_message").asText());
    }

    @Test
    public void testActivateWithDeactivatedCard() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .deactivated()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("INACTIVE_CARD", json().get("result_message").asText());
    }

    @Test
    public void testActivateWithoutEnoughCardBalance() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() - 1)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("MACHINE_ACTIVATION_FAILED", json().get("result_message").asText());
        assertTrue(json().get("result_detail").asText().contains("INSUFFICIENT_CARD_BALANCE"));
    }

    @Test
    public void testActivateWithCardBelongsToAnotherBuilding() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.create();

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        Building anotherBuilding = new BuildingFactory().withRate().create();
        Unit anotherUnit = new UnitFactory()
            .with("building", anotherBuilding)
            .with("owner", user)
            .create();
        Card anotherCard = new CardFactory().postpaid().with("unit", anotherUnit).create();
        // workaround to get card and unit related
        anotherUnit.getAssignedCards().add(anotherCard);
        anotherUnit.update();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, anotherCard, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("MACHINE_ACTIVATION_FAILED", json().get("result_message").asText());
        assertTrue(json().get("result_detail").asText().contains("NOT_VALID_RESULT_TYPE"));
    }

    @Test
    public void testActivateWithInactiveUser() throws Exception {
        UserFactory factory = new UserFactory().withAuthentication();
        User user = factory.with("isActiveUser", false).create();

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        String token = factory.decorate().getToken();
        postActivate(token, jsonize(building, card, machine));

        PowerMock.verify(mockProducer);
        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals("INACTIVE_USER", json().get("result_message").asText());
    }

    private JsonNode jsonize(Building building, Card card, Machine machine) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("slug", building.getSlug())
            .put("index", machine.getSortIndex())
            .put("uid", card.getUuid());
    }
}
