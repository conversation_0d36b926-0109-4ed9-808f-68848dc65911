package domains.payment_gateways.services.bancard;

import static org.junit.Assert.assertEquals;

import java.util.UUID;
import org.junit.Test;

public class ShopIdSerializerTest extends common.BaseTest {

    @Test
    public void testGetShopIdLength() throws Exception {
        String uuid = UUID.randomUUID().toString();

        String result = ShopIdSerializer.getShopId(uuid);

        assertEquals(15, (result + "").length());
    }

    @Test
    public void testSameUuidGivesSameShopId() throws Exception {
        String uuid = "2f3f05ed-4c77-4f2e-8da3-38a56cf9a8e8";

        String first = ShopIdSerializer.getShopId(uuid);
        String second = ShopIdSerializer.getShopId(uuid);

        assertEquals(first, second);
    }
}
