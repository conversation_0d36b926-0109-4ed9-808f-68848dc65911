package domains.activations.internal;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import common.helpers.DateTimer;
import common.helpers.Enumeration;
import domains.activations.ActivationResult;
import domains.activations.Channel;
import domains.activations.mqtt.LMMQTTProducer;
import domains.payment_gateways.services.mercado_pago.MercadoPagoTransactionFlow;
import factories.*;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import global.exceptions.machines.MachineAlreadyBooked;
import global.exceptions.machines.MachineNotFoundException;
import global.exceptions.machines.UserNotFoundException;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import models.*;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest(Publisher.class)
@PowerMockIgnore("javax.management.*")
public class ActivationServiceTest extends common.BaseTest {

    private LMMQTTProducer mockMessenger(
        boolean fails,
        MachineUseResult expectedResult,
        Building building,
        Machine machine
    ) throws Exception {
        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"" + expectedResult.getCodeString() + "\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        expect(mockProducer.onPublishing(anyObject()))
            .andAnswer(() -> {
                Runnable callback = getCurrentArgument(0);
                if (!fails) {
                    assertNotNull(callback);
                    callback.run();
                }

                return mockProducer;
            })
            .times(1);
        expect(mockProducer.onPublished(anyObject()))
            .andAnswer(() -> {
                Runnable callback = getCurrentArgument(0);
                if (!fails) {
                    assertNotNull(callback);
                    callback.run();
                }

                return mockProducer;
            })
            .times(1);
        // this does not mean an error happened, it means the callback on error case was set
        expect(mockProducer.onError(anyObject()))
            .andAnswer(() -> {
                Consumer<Exception> callback = getCurrentArgument(0);
                if (fails) {
                    assertNotNull(callback);
                    callback.accept(new Exception("mocked error"));
                }

                return mockProducer;
            })
            .times(1);

        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        return mockProducer;
    }

    private LMMQTTProducer mockMessengerWithoutCalling(Building building, Machine machine)
        throws Exception {
        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andStubThrow(new AssertionError("LMMQTTProducer#run should not be called"));
        expect(mockProducer.onPublishing(anyObject()))
            .andStubThrow(new AssertionError("LMMQTTProducer#onPublishing should not be called"));
        expect(mockProducer.onPublished(anyObject()))
            .andStubThrow(new AssertionError("LMMQTTProducer#onPublished should not be called"));
        expect(mockProducer.onError(anyObject()))
            .andStubThrow(new AssertionError("LMMQTTProducer#onError should not be called"));
        PowerMock.replay(mockProducer, LMMQTTProducer.class);
        return mockProducer;
    }

    @Test
    public void testActivatePostpaidViaWhatsappBot() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.WHATSAPP_POSTPAID_ACTIVATION;
        String channel = Channel.WHATSAPP_CHANNEL;

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card)
            .atBuilding(building)
            .forMachine(machine)
            .sendingMqttMessage()
            .viaChannel(Channel.WHATSAPP_CHANNEL)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertTrue(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(0, publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(0, publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        assertNull(result.getJson());
    }

    @Test
    public void testActivatePrepaidViaWhatsappBot() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.WHATSAPP_ACTIVATION_WITH_BALANCE;
        String channel = Channel.WHATSAPP_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card)
            .atBuilding(building)
            .forMachine(machine)
            .sendingMqttMessage()
            .viaChannel(Channel.WHATSAPP_CHANNEL)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertTrue(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(0, publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(0, publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        assertNull(result.getJson());
    }

    @Test
    public void testActivatePostpaidViaAssistant() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.POSTPAID_ACTIVATION;
        String channel = Channel.ASSISTANT_CHANNEL;

        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card)
            .atBuilding(building)
            .forMachine(machine)
            .initiatedBy(user)
            .sendingMqttMessage()
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(user.getId(), publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(user.getId(), publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivatePrepaidViaAssistant() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE;
        String channel = Channel.ASSISTANT_CHANNEL;

        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card)
            .atBuilding(building)
            .forMachine(machine)
            .initiatedBy(user)
            .sendingMqttMessage()
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(user.getId(), publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(user.getId(), publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivateViaQR() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.QR;
        String channel = Channel.QR_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();
        Transaction transaction = new TransactionFactory()
            .with("machine", machine)
            .with("origin", MercadoPagoTransactionFlow.QR_ORIGIN)
            .create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .atBuilding(building)
            .forMachine(machine)
            .sendingMqttMessage()
            .throughTransaction(transaction)
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            null,
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEquals(transaction.getId(), prePublishingAudit.getPaymentId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(0, publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(0, publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        assertNull(result.getJson());
    }

    @Test
    public void testActivatePrepaidViaRPI() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE;
        String channel = Channel.RPI_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card.getUuid())
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
        assertEquals((int) expectedResult.getCode(), resultJson.get("result").asInt());
        assertEquals(machine.getAverageUseTime(), resultJson.get("BLOCKED_TIME").asInt());
    }

    @Test
    public void testActivatePostpaidViaRPI() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.POSTPAID_ACTIVATION;
        String channel = Channel.RPI_CHANNEL;

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card.getUuid())
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
        assertEquals((int) expectedResult.getCode(), resultJson.get("result").asInt());
        assertEquals(machine.getAverageUseTime(), resultJson.get("BLOCKED_TIME").asInt());
    }

    @Test
    public void testActivateViaTotem() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.LAUNDROMAT;
        String channel = Channel.TOTEM_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();
        Transaction transaction = new TransactionFactory()
            .with("machine", machine)
            .with("origin", "TransAct")
            .create();
        User activator = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .sendingMqttMessage(true)
            .viaChannel(channel)
            .throughTransaction(transaction.getPublicId())
            .initiatedBy(activator)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            null,
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEquals(transaction.getId(), prePublishingAudit.getPaymentId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(0, publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(0, publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivateWithPreviousBookingViaTotem() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.LAUNDROMAT;
        String channel = Channel.TOTEM_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();
        Transaction transaction = new TransactionFactory()
            .with("machine", machine)
            .with("origin", "TransAct")
            .create();
        User activator = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .with("user", activator)
            .create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .sendingMqttMessage(true)
            .viaChannel(channel)
            .throughTransaction(transaction.getPublicId())
            .initiatedBy(activator)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            null,
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEquals(transaction.getId(), prePublishingAudit.getPaymentId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(0, publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(0, publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());
        // machine booking
        assertEquals(MachineBooking.MachineBookingStatus.COMPLETED, booking.getStatus());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivatePrepaidViaMobileApp() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE;
        String channel = Channel.APPLICATION_CHANNEL;

        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();
        User user = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .create();
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        String expoDeviceToken = faker.internet().uuid();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card.getUuid())
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .sendingMqttMessage(true)
            .impersonates(user.getEmailAddress())
            .notifyDevice(expoDeviceToken)
            .viaChannel(channel)
            .throughTransaction("0")
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(user.getId(), publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(user.getId(), publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // user account
        reload(user);
        Account account = user.getMasterAccount();
        assertEquals(expoDeviceToken, account.getExpoDeviceToken());

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivatePostpaidViaMobileApp() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.POSTPAID_ACTIVATION;
        String channel = Channel.APPLICATION_CHANNEL;

        Building building = new BuildingFactory().withRate().create();
        User user = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .create();
        Rate rate = building.getRate();
        reload(rate);
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().withOwner(user).with("unit", unit).create();
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();
        String expoDeviceToken = faker.internet().uuid();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card.getUuid())
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .sendingMqttMessage(true)
            .impersonates(user.getEmailAddress())
            .notifyDevice(expoDeviceToken)
            .viaChannel(channel)
            .throughTransaction("0")
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishingAudit = audits.get(1);
        assertEquals(user.getId(), publishingAudit.getUser());
        assertEquals(0, publishingAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishingAudit.getStatus());
        assertEquals("Publishing Broker Message", publishingAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishingAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow publishedAudit = audits.get(2);
        assertEquals(user.getId(), publishedAudit.getUser());
        assertEquals(0, publishedAudit.getGroup());
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, publishedAudit.getStatus());
        assertEquals("Message published", publishedAudit.getDetails());
        assertEquals(prePublishingAudit.getId(), publishedAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            publishedAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // user account
        reload(user);
        Account account = user.getMasterAccount();
        assertEquals(expoDeviceToken, account.getExpoDeviceToken());

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    // --------- validations ---------

    @Test
    public void testActivateCardNotFoundError() throws Exception {
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .build();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building)
                    .forMachine(machine)
                    .sendingMqttMessage()
                    .viaChannel(Channel.WHATSAPP_CHANNEL)
                    .activate()
        );
        assertExceptionMessage(APIException.APIErrors.CARD_NOT_FOUND.message(), ex.getMessage());
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateInactiveCardError() throws Exception {
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .inactive()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .sendingMqttMessage()
                    .viaChannel(Channel.WHATSAPP_CHANNEL)
                    .activate()
        );
        assertExceptionMessage(APIException.APIErrors.INACTIVE_CARD.message(), ex.getMessage());
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateMachineNotFoundError() throws Exception {
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).build();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        assertThrows(
            MachineNotFoundException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine.getSerialNumber())
                    .sendingMqttMessage()
                    .viaChannel(Channel.WHATSAPP_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateBuildingNotFoundError() throws Exception {
        Building building = new BuildingFactory().laundromat().withRate().build();
        Card card = new CardFactory().prepaid().virtual().create();
        Machine machine = new MachineFactory().create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        assertThrows(
            BuildingNotFoundException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building.getId())
                    .forMachine(machine)
                    .sendingMqttMessage()
                    .viaChannel(Channel.WHATSAPP_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateUserNotFoundThroughEmailError() throws Exception {
        String channel = Channel.APPLICATION_CHANNEL;
        Building building = new BuildingFactory().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();
        String expoDeviceToken = faker.internet().uuid();
        User user = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .build();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .sendingMqttMessage(true)
                    .impersonates(user.getEmailAddress())
                    .forGroup(0)
                    .notifyDevice(expoDeviceToken)
                    .viaChannel(channel)
                    .throughTransaction("0")
                    .activate()
        );
        assertExceptionMessage(APIException.APIErrors.USER_NOT_FOUND.message(), ex.getMessage());
        assertExceptionMessage("USER_NOT_FOUND", ex.getDetailMessage());
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateUserInactiveThroughEmailError() throws Exception {
        String channel = Channel.APPLICATION_CHANNEL;
        Building building = new BuildingFactory().withRate().create();
        User user = new UserFactory()
            .inactive()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .create();
        Rate rate = building.getRate();
        reload(rate);
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().withOwner(user).with("unit", unit).create();
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();
        String expoDeviceToken = faker.internet().uuid();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .sendingMqttMessage(true)
                    .impersonates(user.getEmailAddress())
                    .forGroup(0)
                    .notifyDevice(expoDeviceToken)
                    .viaChannel(channel)
                    .throughTransaction("0")
                    .activate()
        );
        assertExceptionMessage(APIException.APIErrors.INACTIVE_USER.message(), ex.getMessage());
        assertExceptionMessage("INACTIVE_USER", ex.getDetailMessage());
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateActivatorUserNotFoundError() throws Exception {
        User user = null;
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        assertThrows(
            UserNotFoundException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .initiatedBy(user)
                    .sendingMqttMessage()
                    .viaChannel(Channel.ASSISTANT_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);
    }

    @Test
    public void testActivateActivatorUserInactiveError() throws Exception {
        User user = new UserFactory().withAuthentication().inactive().create();
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .initiatedBy(user)
                    .sendingMqttMessage()
                    .viaChannel(Channel.ASSISTANT_CHANNEL)
                    .activate()
        );
        assertExceptionMessage(APIException.APIErrors.INACTIVE_USER.message(), ex.getMessage());
        assertExceptionMessage("INACTIVE_USER", ex.getDetailMessage());
        PowerMock.verify(mockProducer);
    }

    // -------- availability ---------

    @Test
    public void testActivateInsufficientCardBalanceError() throws Exception {
        String channel = Enumeration.sampleOf(
            Channel.APPLICATION_CHANNEL,
            Channel.ASSISTANT_CHANNEL,
            Channel.RPI_CHANNEL,
            Channel.WHATSAPP_CHANNEL
        );
        System.out.println("Channel: " + channel);
        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() - 1)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .initiatedBy(user)
                    .impersonates(user.getEmailAddress())
                    .sendingMqttMessage()
                    .viaChannel(channel)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.INSUFFICIENT_CARD_BALANCE.message(),
            cause.getMessage()
        );
        assertExceptionMessage("INSUFFICIENT_CARD_BALANCE", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateEnoughBalanceByCardDiscount() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE;
        String channel = Channel.ASSISTANT_CHANNEL;

        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() - 1)
            .with("discount", .5) // 50% off
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(false, expectedResult, building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card)
            .atBuilding(building)
            .forMachine(machine)
            .initiatedBy(user)
            .sendingMqttMessage()
            .viaChannel(channel)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // machine
        reload(machine);
        assertEquals(Machine.StatusActivation.ACTIVATED, machine.getStatusActivation());

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
    }

    @Test
    public void testActivateInvalidTimeError() throws Exception {
        String channel = Enumeration.sampleOf(
            Channel.APPLICATION_CHANNEL,
            Channel.ASSISTANT_CHANNEL,
            Channel.RPI_CHANNEL,
            Channel.WHATSAPP_CHANNEL
        );
        System.out.println("Channel: " + channel);
        Building building = new BuildingFactory().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Unit unit = new UnitFactory().with("building", building).create();
        User user = new UserFactory().withAuthentication().create();
        Card card = new CardFactory()
            .prepaid()
            .withOwner(user)
            .with("balance", rate.getPriceCustomer() * 2)
            .with("startTimeOfUse", DateTimer.ago(4, TimeUnit.HOURS))
            .with("endTimeOfUse", DateTimer.ago(1, TimeUnit.MINUTES))
            .with("unit", unit)
            .create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .sendingMqttMessage(true)
                    .impersonates(user.getEmailAddress())
                    .forGroup(0)
                    .viaChannel(channel)
                    .throughTransaction("0")
                    .initiatedBy(user)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.INVALID_USE_TIME.message(),
            cause.getMessage()
        );
        assertExceptionMessage("INVALID_USE_TIME", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateUnavailableByMachineBusyError() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        int machineUseTime = 40;
        Machine machine = new MachineFactory()
            .withUseTime(machineUseTime)
            .with("building", building)
            .create();
        MachineUse use = new MachineUsesFactory()
            .withTimestampAgo(faker.number().numberBetween(1, machineUseTime), TimeUnit.MINUTES)
            .with("machine", machine)
            .create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .viaChannel(Channel.RPI_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(APIException.APIErrors.MACHINE_BUSY.message(), cause.getMessage());
        assertExceptionMessage("MACHINE_BUSY", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    /**
     * TODO: this case is not reached in the code.
     * Case covered by {@link ActivationServiceTest#testActivateInactiveCardError}
     */
    @Ignore
    @Test
    public void testActivateUnavailableByInactiveCardError() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().inactive().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        int machineUseTime = 40;
        Machine machine = new MachineFactory()
            .withUseTime(machineUseTime)
            .with("building", building)
            .create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .viaChannel(Channel.RPI_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(APIException.APIErrors.INACTIVE_CARD.message(), cause.getMessage());
        assertExceptionMessage("INACTIVE_CARD", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateByPreBlockedCard() throws Exception {
        MachineUseResult expectedResult =
            MachineUseResult.POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE;
        String channel = Channel.RPI_CHANNEL;

        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory()
            .preBlocked()
            .postpaid()
            .with("balance", 0)
            .with("unit", unit)
            .create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        int machineUseTime = 40;
        Machine machine = new MachineFactory()
            .withUseTime(machineUseTime)
            .with("building", building)
            .create();

        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        ActivationResult result = new ActivationService()
            .usingCard(card.getUuid())
            .atBuilding(building.getId())
            .forMachine(machine.getSerialNumber())
            .viaChannel(Channel.RPI_CHANNEL)
            .activate();

        PowerMock.verify(mockProducer);

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(0, prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        // result
        assertEquals(machine.getAverageUseTime(), result.getMachineUseAverageTime());
        assertEquals(prePublishingAudit.getId(), result.getAuditId());
        assertEquals(expectedResult, result.getActivationResult());
        JsonNode resultJson = jsonize(result.getJson());
        assertEquals(prePublishingAudit.getId(), resultJson.get("Transaction_id").asInt());
        assertEquals((int) expectedResult.getCode(), resultJson.get("result").asInt());
        assertEquals(machine.getAverageUseTime(), resultJson.get("BLOCKED_TIME").asInt());
    }

    @Test
    public void testActivateByPreBlockedCardError() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory()
            .postpaid()
            .preBlocked()
            .with("preBlockedUses", 0)
            .with("balance", 0)
            .with("unit", unit)
            .create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        int machineUseTime = 40;
        Machine machine = new MachineFactory()
            .withUseTime(machineUseTime)
            .with("building", building)
            .create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .viaChannel(Channel.RPI_CHANNEL)
                    .activate()
        );

        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.INSUFFICIENT_CARD_BALANCE.message(),
            cause.getMessage()
        );
        assertExceptionMessage("INSUFFICIENT_CARD_BALANCE", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateUnavailableByCardNotBelongToBuildingError() throws Exception {
        Building building = new BuildingFactory().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();

        Building anotherBuilding = new BuildingFactory().withRate().create();
        Rate anotherRate = anotherBuilding.getRate();
        reload(anotherRate);
        Card card = new CardFactory()
            .prepaid()
            .virtual()
            .with("balance", rate.getPriceCustomer() * 2)
            .create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card.getUuid())
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .viaChannel(Channel.RPI_CHANNEL)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.CARD_NOT_FROM_BUILDING.message(),
            cause.getMessage()
        );
        assertExceptionMessage("CARD_NOT_FROM_BUILDING", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateMachineBookedError() throws Exception {
        Building building = new BuildingFactory().laundromat().withRate().create();
        Rate rate = building.getRate();
        reload(rate);
        Machine machine = new MachineFactory().with("building", building).create();
        Transaction transaction = new TransactionFactory()
            .with("machine", machine)
            .with("origin", "TransAct")
            .create();
        User activator = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .create();
        User anotherActivator = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .with("user", anotherActivator)
            .create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .atBuilding(building.getId())
                    .forMachine(machine.getSerialNumber())
                    .sendingMqttMessage(true)
                    .forGroup(0)
                    .viaChannel(Channel.TOTEM_CHANNEL)
                    .throughTransaction(transaction.getPublicId())
                    .initiatedBy(activator)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(MachineAlreadyBooked.class, ex.getCause().getClass());
        MachineAlreadyBooked cause = (MachineAlreadyBooked) ex.getCause();
        assertExceptionMessage("MACHINE_ALREADY_BOOKED", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Ignore // TODO: prevent Null Pointer Exception
    @Test
    public void testActivateBuildingWithoutRateError() throws Exception {
        String channel = Enumeration.sampleOf(Channel.TOTEM_CHANNEL, Channel.QR_CHANNEL);
        System.out.println("Channel: " + channel);
        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().create();
        Card card = new CardFactory().prepaid().virtual().withOwner(user).create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .sendingMqttMessage()
                    .viaChannel(channel)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.NOT_VALID_RESULT_TYPE.message(),
            cause.getMessage()
        );
        assertExceptionMessage("NOT_VALID_RESULT_TYPE", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    @Test
    public void testActivateBuildingWithoutExpiredRateEventError() throws Exception {
        String channel = Enumeration.sampleOf(Channel.TOTEM_CHANNEL, Channel.QR_CHANNEL);
        System.out.println("Channel: " + channel);
        User user = new UserFactory().withAuthentication().create();
        Rate rate = new RateFactory().create();
        RateEvent event = new RateEventFactory()
            .with("validFrom", DateTimer.ago(10, TimeUnit.DAYS))
            .with("validUntil", DateTimer.ago(1, TimeUnit.DAYS))
            .with("rate", rate)
            .create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Card card = new CardFactory().prepaid().virtual().withOwner(user).create();
        Machine machine = new MachineFactory().with("building", building).create();

        // mocked to prevent unexpected calls, but it should be called in this case
        LMMQTTProducer mockProducer = mockMessengerWithoutCalling(building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .sendingMqttMessage()
                    .viaChannel(channel)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertExceptionMessage(
            APIException.APIErrors.NOT_VALID_RESULT_TYPE.message(),
            cause.getMessage()
        );
        assertExceptionMessage("NOT_VALID_RESULT_TYPE", cause.getDetailMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(1, audits.size());

        AuditWorkflow errorAudit = audits.get(0);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(0, errorAudit.getOriginalTransactionId()); // main audit was not saved yet
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    // --------- mqtt error  ---------

    @Test
    public void testActivateMqttError() throws Exception {
        MachineUseResult expectedResult = MachineUseResult.POSTPAID_ACTIVATION;
        String channel = Channel.ASSISTANT_CHANNEL;

        User user = new UserFactory().withAuthentication().create();
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().postpaid().with("unit", unit).create();
        // workaround to get card and unit related
        unit.getAssignedCards().add(card);
        unit.update();
        Machine machine = new MachineFactory().with("building", building).create();

        LMMQTTProducer mockProducer = mockMessenger(true, expectedResult, building, machine);

        APIException ex = assertThrows(
            APIException.class,
            () ->
                new ActivationService()
                    .usingCard(card)
                    .atBuilding(building)
                    .forMachine(machine)
                    .initiatedBy(user)
                    .sendingMqttMessage()
                    .viaChannel(channel)
                    .activate()
        );
        PowerMock.verify(mockProducer);

        assertExceptionMessage(
            APIException.APIErrors.MACHINE_ACTIVATION_FAILED.message(),
            ex.getMessage()
        );

        // Original exception cause
        assertEquals(APIException.class, ex.getCause().getClass());
        APIException cause = (APIException) ex.getCause();
        assertNotNull(cause);
        Exception nestedCause = (Exception) cause.getCause();
        assertNotNull(nestedCause);
        assertExceptionMessage("mocked error", nestedCause.getMessage());

        // AuditWorkflow tracking
        List<AuditWorkflow> audits = AuditWorkflow.findAll();
        assertEquals(3, audits.size());

        AuditWorkflow prePublishingAudit = audits.get(0);
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertFalse(prePublishingAudit.getBotMachineActivation());
        assertEquals(
            "Starting activation process for " +
            channel +
            ": MachineID:" +
            machine.getId() +
            "|" +
            "BuildingID:" +
            building.getId() +
            "|" +
            "UUID:" +
            card.getUuid(),
            prePublishingAudit.getDetails()
        );
        assertEquals(AuditWorkflow.Status.IN_PROGRESS, prePublishingAudit.getStatus());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            prePublishingAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow callbackErrorAudit = audits.get(1);
        assertExceptionMessage(
            callbackErrorAudit.getDetails(),
            "Error Message was not published:null"
        );
        assertEquals(AuditWorkflow.Status.MQTT_ERROR, callbackErrorAudit.getStatus());
        assertEquals(prePublishingAudit.getId(), callbackErrorAudit.getOriginalTransactionId());
        assertEquals(user.getId(), prePublishingAudit.getUser());
        assertEquals(0, prePublishingAudit.getGroup());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            callbackErrorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );

        AuditWorkflow errorAudit = audits.get(2);
        assertExceptionMessage(errorAudit.getDetails(), ex.getMessage());
        assertEquals(AuditWorkflow.Status.ERROR, errorAudit.getStatus());
        assertEquals(prePublishingAudit.getId(), errorAudit.getOriginalTransactionId());
        assertEqualsWithinDeviation(
            new Date().getTime(),
            errorAudit.getDate().getTime(),
            TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS)
        );
    }

    private JsonNode jsonize(String text) {
        try {
            return new ObjectMapper().readTree(text);
        } catch (IOException e) {
            return new ObjectMapper().createObjectNode();
        }
    }

    private static void assertExceptionMessage(final String expected, final String actual) {
        assertTrue(
            "Expected exception content \"" +
            expected +
            "\" is not present on the actual exception details \"" +
            actual +
            "\"",
            actual.contains(expected)
        );
    }
}
