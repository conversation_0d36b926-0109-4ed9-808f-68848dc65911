package domains.activations.mqtt;

import static org.junit.Assert.*;

import common.helpers.Enumeration;
import factories.CardFactory;
import java.util.Iterator;
import models.Card;
import models.MachineUseResult;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Test;

public class MessageBuilderTest extends common.BaseTest {

    @Test
    public void testAllValuesAreStrings() throws JSONException {
        Card card = new CardFactory().build();

        MessageBuilder builder = new MessageBuilder()
            .setCardUuid(card)
            .setBuildingId(faker.number().randomDigit())
            .setMachineSerial(faker.internet().uuid())
            .setTransactionId(faker.number().randomDigit())
            .setResult(Enumeration.sampleOf(MachineUseResult.class))
            .setTimestamp()
            .setBlockedTime(faker.number().randomNumber())
            .setBookingId(faker.internet().uuid());

        String result = builder.build();

        assertNotNull(result);

        JSONObject json = new JSONObject(result);
        for (Iterator<String> it = json.keys(); it.hasNext();) {
            String key = it.next();
            assertFalse(json.isNull(key));

            Object value = json.get(key);
            assertTrue(value instanceof String);
        }
    }

    @Test
    public void testAllValuesAreStringsWithNullCatd() throws JSONException {
        Card card = null;

        MessageBuilder builder = new MessageBuilder()
            .setCardUuid(card)
            .setBuildingId(faker.number().randomDigit())
            .setMachineSerial(faker.internet().uuid())
            .setTransactionId(faker.number().randomDigit())
            .setResult(Enumeration.sampleOf(MachineUseResult.class))
            .setTimestamp()
            .setBlockedTime(faker.number().randomNumber())
            .setBookingId(faker.internet().uuid());

        String result = builder.build();

        assertNotNull(result);

        JSONObject json = new JSONObject(result);
        for (Iterator<String> it = json.keys(); it.hasNext();) {
            String key = it.next();
            assertFalse(json.isNull(key));

            Object value = json.get(key);
            assertTrue(value instanceof String);
        }
    }
}
