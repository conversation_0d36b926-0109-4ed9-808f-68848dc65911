package domains.public_site.controllers.v1.cards;

import static org.junit.Assert.*;

import domains.payment_gateways.services.bamboo_payment.PagosWebTransactionFlow;
import domains.payment_gateways.services.bamboo_payment.PhysicalAgentTransactionFlow;
import domains.payment_gateways.services.mercado_pago.MercadoPagoTransactionFlow;
import factories.*;
import java.util.Arrays;
import java.util.stream.Collectors;
import models.*;
import org.junit.Test;
import play.mvc.Http;

public class CardControllerPaymentMethodsTest extends common.RequestBaseTest {

    private void getPaymentMethods(String cardUid) {
        request(generateCall("GET", "/public-site/v1/cards/" + cardUid + "/methods"));
    }

    @Test
    public void testPostpaidCardUnassociatedToUnit() {
        Card card = new CardFactory().postpaid().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.CONFLICT, status());
        assertEquals(70, json().get("result_code").asInt());
    }

    @Test
    public void testPostpaidCardAssociatedToUnit() throws FactoryException {
        Building building = new BuildingFactory().withRate().withUnit().create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().postpaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.CONFLICT, status());

        assertEquals(70, json().get("result_code").asInt());

        Rate rate = building.getRate();
        reload(rate);
        assertEquals(
            "Su tarjeta es POSTPAGO, no requiere precarga. Los usos realizados con su tarjeta se cobran en los gastos comunes. " +
            "El costo de cada uso es de $" +
            String.format("%.0f", rate.getPriceCustomer()) +
            ",00.",
            json().get("result_detail").asText()
        );
    }

    @Test
    public void testPrepaidLostCard() {
        Card card = new CardFactory().prepaid().lost().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.BAD_REQUEST, status());
        assertEquals(166, json().get("result_code").asInt());
    }

    @Test
    public void testPrepaidBlockedCard() {
        Card card = new CardFactory().prepaid().deactivated().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.BAD_REQUEST, status());
        assertEquals(167, json().get("result_code").asInt());
    }

    @Test
    public void testPrepaidInactiveCard() {
        Card card = new CardFactory().prepaid().inactive().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(100, json().get("result_code").asInt());
    }

    @Test
    public void testPrepaidCardNonVirtualUnassociatedToUnit() {
        Card card = new CardFactory().prepaid().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.BAD_REQUEST, status());
        assertEquals(159, json().get("result_code").asInt());
    }

    @Test
    public void testPrepaidCardAssociatedToUnitWithoutSplitting() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().withUnit().with("rate", rate).create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(
                MercadoPagoTransactionFlow.ORIGIN,
                PagosWebTransactionFlow.ORIGIN,
                PhysicalAgentTransactionFlow.ORIGIN
            ),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );
        assertEquals(rateEvent.getPriceCustomer(), json().get("price_customer").asDouble(), 0.001);
    }

    @Test
    public void testPrepaidCardAssociatedToUnitWithoutSplittingAndSpecialMachineRate()
        throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().withUnit().with("rate", rate).create();
        Rate machineRate = new RateFactory().create();
        RateEvent machineRateEvent = new RateEventFactory()
            .nonSplittable()
            .withRate(machineRate)
            .create();
        Machine machine = new MachineFactory()
            .with("machineRate", machineRate)
            .with("building", building)
            .create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(
                MercadoPagoTransactionFlow.ORIGIN,
                PagosWebTransactionFlow.ORIGIN,
                PhysicalAgentTransactionFlow.ORIGIN
            ),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );
        assertEquals(rateEvent.getPriceCustomer(), json().get("price_customer").asDouble(), 0.001);
        assertEquals(
            machineRateEvent.getPriceCustomer(),
            json().get("machine_rate").asDouble(),
            0.001
        );
        assertEquals(
            machineRate.getDescriptiveMessage(),
            json().get("special_rate_message").asText()
        );
    }

    @Test
    public void testPrepaidCardAssociatedToUnitWithSplitting() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().splittable().withRate(rate).create();
        Building building = new BuildingFactory().withUnit().with("rate", rate).create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(MercadoPagoTransactionFlow.ORIGIN),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );

        assertEquals(rateEvent.getPriceCustomer(), json().get("price_customer").asDouble(), 0.001);
    }

    @Test
    public void testPrepaidCardAssociatedToUnitWithPagosWebSplitting() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().splittable().withRate(rate).create();
        Building building = new BuildingFactory()
            .withUnit()
            .withPagosWebSplittingEnabled()
            .with("rate", rate)
            .create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(MercadoPagoTransactionFlow.ORIGIN, PagosWebTransactionFlow.ORIGIN),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );

        assertEquals(rateEvent.getPriceCustomer(), json().get("price_customer").asDouble(), 0.001);
    }

    @Test
    public void testPrepaidCardAssociatedToUnitWithRedPagosSplitting() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().splittable().withRate(rate).create();
        Building building = new BuildingFactory()
            .withUnit()
            .withRedPagosSplittingEnabled()
            .with("rate", rate)
            .create();
        Unit unit = building.getUnits().get(0);
        Card card = new CardFactory().prepaid().with("unit", unit).create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(MercadoPagoTransactionFlow.ORIGIN, PhysicalAgentTransactionFlow.ORIGIN),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );

        assertEquals(rateEvent.getPriceCustomer(), json().get("price_customer").asDouble(), 0.001);
    }

    @Test
    public void testVirtualCard() throws FactoryException {
        Card card = new CardFactory().prepaid().virtual().create();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.OK, status());
        assertCollection(
            Arrays.asList(
                MercadoPagoTransactionFlow.ORIGIN,
                PagosWebTransactionFlow.ORIGIN,
                PhysicalAgentTransactionFlow.ORIGIN
            ),
            jsonStream("payment_methods")
                .map(j -> j.get("name").asText())
                .collect(Collectors.toList())
        );
        assertNull(json().get("price_customer"));
    }

    @Test
    public void testCardNotFound() {
        Card card = new CardFactory().build();

        getPaymentMethods(card.getUuid());

        assertEquals(Http.Status.NOT_FOUND, status());
    }
}
