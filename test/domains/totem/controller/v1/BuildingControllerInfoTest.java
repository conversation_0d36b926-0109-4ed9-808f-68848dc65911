package domains.totem.controller.v1;

import static org.junit.Assert.*;

import common.RequestBaseTest;
import factories.BuildingFactory;
import factories.BuildingSettingFactory;
import factories.FactoryException;
import factories.UserFactory;
import java.util.concurrent.TimeUnit;
import models.Building;
import models.BuildingSetting;
import models.Role;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class BuildingControllerInfoTest extends RequestBaseTest {

    private void getInfo(String token, int level) {
        Call call = generateCall("GET", "/totem/v1/building?level=" + level);

        request(call, token);
    }

    @Test
    public void testGetInfoLevelZero() throws FactoryException {
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, 0);

        assertEquals(Http.Status.OK, status());

        assertEquals(building.getId(), json().get("id").asInt());
    }

    @Test
    public void testGetInfoLevelOne() throws FactoryException {
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, 1);

        assertEquals(Http.Status.OK, status());

        assertEquals(building.getId(), json().get("id").asInt());
        assertEquals(building.getShowLastUsrInfo(), json().get("showLastUserInfo").asBoolean());
        assertEquals(building.getBuildingType().toString(), json().get("buildingType").asText());
    }

    @Test
    public void testGetInfoLevelOneWithSettings() throws FactoryException {
        BuildingSetting settings = new BuildingSettingFactory()
            .with("openingTime", faker.date().past(1, TimeUnit.HOURS))
            .with("closingTime", faker.date().future(1, TimeUnit.HOURS))
            .create();
        Building building = new BuildingFactory().with("buildingSetting", settings).create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, 1);

        assertEquals(building.getId(), json().get("id").asInt());
        assertEquals(building.getShowLastUsrInfo(), json().get("showLastUserInfo").asBoolean());
        assertEquals(building.getBuildingType().toString(), json().get("buildingType").asText());
        assertEquals(settings.getOpeningTime().getTime(), json().get("openingTime").asLong());
        assertEquals(settings.getClosingTime().getTime(), json().get("closingTime").asLong());
    }

    @Test
    public void testGetInfoLevelOneWithoutBuildingTime() throws FactoryException {
        BuildingSetting settings = new BuildingSettingFactory().create();
        settings.setOpeningTime(null);
        settings.setClosingTime(null);
        Building building = new BuildingFactory().with("buildingSetting", settings).create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, 1);

        assertEquals(building.getId(), json().get("id").asInt());
        assertEquals(building.getShowLastUsrInfo(), json().get("showLastUserInfo").asBoolean());
        assertEquals(building.getBuildingType().toString(), json().get("buildingType").asText());
        assertTrue(json().get("openingTime").isNull());
        assertTrue(json().get("closingTime").isNull());
    }

    @Test
    public void testGetInfoUnauthorized() {
        getInfo(null, 0);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
