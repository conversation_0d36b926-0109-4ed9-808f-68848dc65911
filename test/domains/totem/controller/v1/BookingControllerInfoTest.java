package domains.totem.controller.v1;

import static org.junit.Assert.assertEquals;

import factories.*;
import models.Building;
import models.Machine;
import models.MachineBooking;
import models.Role;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class BookingControllerInfoTest extends common.RequestBaseTest {

    private void getInfo(String token, String id) {
        Call call = generateCall("GET", "/totem/v1/booking/" + id);

        request(call, token);
    }

    @Test
    public void testGetInfo() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, booking.getPublicId());

        assertEquals(Http.Status.OK, status());

        assertEquals(booking.getPublicId(), json().get("id").asText());
        assertEquals(machine.getSerialNumber(), json().get("serial").asText());
        assertEquals(
            MachineBooking.MachineBookingStatus.IN_PROGRESS.toString(),
            json().get("status").asText()
        );
    }

    @Test
    public void testGetInfoWithNotFoundBooking() throws FactoryException {
        MachineBooking booking = new MachineBookingFactory().inProgress().build();
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, booking.getPublicId());

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        MachineBooking booking = new MachineBookingFactory().inProgress().create();
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        getInfo(token, booking.getPublicId());

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        MachineBooking booking = new MachineBookingFactory().inProgress().create();

        getInfo(null, booking.getPublicId());

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
