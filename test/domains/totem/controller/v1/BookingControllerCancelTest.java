package domains.totem.controller.v1;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import domains.activations.mqtt.LMMQTTProducer;
import factories.*;
import models.Building;
import models.Machine;
import models.MachineBooking;
import models.Role;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;
import services.machine.TotemBookingService;

@RunWith(PowerMockRunner.class)
@PrepareForTest(TotemBookingService.class)
@PowerMockIgnore("javax.management.*")
public class BookingControllerCancelTest extends common.RequestBaseTest {

    private void deleteCancel(String token, String id) {
        Call call = generateCall("DELETE", "/totem/v1/booking/" + id);

        request(call, token);
    }

    @Test
    public void testCancelInProgressBooking() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"64\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        deleteCancel(token, booking.getPublicId());

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.NO_CONTENT, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.CANCELLED, booking.getStatus());
    }

    @Test
    public void testCancelActivationPendentBooking() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .activationPendent()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"64\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        deleteCancel(token, booking.getPublicId());

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.NO_CONTENT, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.CANCELLED, booking.getStatus());
    }

    @Test
    public void testCancelCancelledBookingWithoutAdditionalBehaviour() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .cancelled()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andThrow(new AssertionError("Calls are not expected."))
            .anyTimes();
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        deleteCancel(token, booking.getPublicId());

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.NO_CONTENT, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.CANCELLED, booking.getStatus());
    }

    @Test
    public void testCancelWithBookingNotFound() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .build();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        deleteCancel(token, booking.getPublicId());

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testCancelWithBookingNotBelongingToUser() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .with("machine", machine)
            .build();

        Building userBuilding = new BuildingFactory().create();
        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", userBuilding)
            .withAuthentication()
            .authenticate();

        deleteCancel(token, booking.getPublicId());

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        MachineBooking booking = new MachineBookingFactory().inProgress().create();
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        deleteCancel(token, booking.getPublicId());

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        MachineBooking booking = new MachineBookingFactory().inProgress().create();

        deleteCancel(null, booking.getPublicId());

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
