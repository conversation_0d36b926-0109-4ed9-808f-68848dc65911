package domains.totem.controller.v1;

import static org.easymock.EasyMock.*;
import static org.junit.Assert.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import domains.activations.mqtt.LMMQTTProducer;
import factories.*;
import models.*;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;
import queries.booking.MachineBookingQuery;
import services.machine.TotemBookingService;

@RunWith(PowerMockRunner.class)
@PrepareForTest(TotemBookingService.class)
@PowerMockIgnore("javax.management.*")
public class BookingControllerCreateTest extends common.RequestBaseTest {

    private void postCreate(String token, JsonNode json) {
        Call call = generateCall("POST", "/totem/v1/booking");

        request(call, token, json);
    }

    @Test
    public void testBookMachine() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();

        UserFactory factory = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication();
        User totem = factory.create();
        String token = factory.decorate().getToken();

        // avoid calling MQTT client
        // https://github.com/powermock/powermock/wiki/MockConstructor
        // https://easymock.org/user-guide.html
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"61\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));
                assertTrue(json.matches("(.*)\"BOOKING_ID\":\"[a-fA-F0-9\\-]+\"(.*)"));

                return "no log entries.";
            })
            .times(1);
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        postCreate(token, jsonize(machine));

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        MachineBooking booking = new MachineBookingQuery().last();
        assertEquals(booking.getPublicId(), json().get("id").asText());
        assertEquals(machine.getSerialNumber(), json().get("serial").asText());

        assertEquals(MachineBooking.MachineBookingStatus.IN_PROGRESS, booking.getStatus());
        assertEquals(machine.getId(), booking.getMachine().getId());
        assertEquals(new DateTime().getMillis(), booking.getStartDate().getTime(), 1000 * 3);
        assertEquals(totem.getId(), booking.getUser().getId());

        PowerMock.reset(LMMQTTProducer.class);
    }

    @Test
    public void testBookMachineWithMissingSerialError() throws FactoryException {
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        postCreate(token, jsonize(null));

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testBookMachineWithNotFoundMachineError() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().build();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        postCreate(token, jsonize(machine));

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testBookMachineWithNotBelongingMachineToUserTotemError() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();

        Building totemBuilding = new BuildingFactory().create();
        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", totemBuilding)
            .withAuthentication()
            .authenticate();

        postCreate(token, jsonize(machine));

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testBookMachineIgnoringConcurrentBookingCancelled() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking cancelledBooking = new MachineBookingFactory()
            .inTheLastSeconds(5)
            .cancelled()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        // https://github.com/powermock/powermock/wiki/MockConstructor
        // https://easymock.org/user-guide.html
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andReturn("no log entries.")
            .times(1);
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        postCreate(token, jsonize(machine));

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        MachineBooking booking = new MachineBookingQuery().last();
        assertEquals(booking.getPublicId(), json().get("id").asText());

        assertEquals(MachineBooking.MachineBookingStatus.IN_PROGRESS, booking.getStatus());
        assertEquals(machine.getId(), booking.getMachine().getId());
        assertEquals(new DateTime().getMillis(), booking.getStartDate().getTime(), 1000 * 3);

        PowerMock.reset(LMMQTTProducer.class);
    }

    @Test
    public void testBookMachineIgnoringConcurrentBookingExpiredByStatus() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking expiredBooking = new MachineBookingFactory()
            .expired()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        // https://github.com/powermock/powermock/wiki/MockConstructor
        // https://easymock.org/user-guide.html
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andReturn("no log entries.")
            .times(1);
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        postCreate(token, jsonize(machine));

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        MachineBooking booking = new MachineBookingQuery().last();
        assertEquals(booking.getPublicId(), json().get("id").asText());

        assertEquals(MachineBooking.MachineBookingStatus.IN_PROGRESS, booking.getStatus());
        assertEquals(machine.getId(), booking.getMachine().getId());
        assertEquals(new DateTime().getMillis(), booking.getStartDate().getTime(), 1000 * 3);

        PowerMock.reset(LMMQTTProducer.class);
    }

    @Test
    public void testBookMachineIgnoringConcurrentBookingExpiredByTime() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking expiredBooking = new MachineBookingFactory()
            .inTheLastSeconds(16)
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        // avoid calling MQTT client
        // https://github.com/powermock/powermock/wiki/MockConstructor
        // https://easymock.org/user-guide.html
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andReturn("no log entries.")
            .times(1);
        expect(mockProducer.onError(anyObject())).andReturn(mockProducer);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        postCreate(token, jsonize(machine));

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());

        MachineBooking booking = new MachineBookingQuery().last();
        assertEquals(booking.getPublicId(), json().get("id").asText());
        assertEquals(machine.getSerialNumber(), json().get("serial").asText());

        assertEquals(MachineBooking.MachineBookingStatus.IN_PROGRESS, booking.getStatus());
        assertEquals(machine.getId(), booking.getMachine().getId());
        assertEquals(new DateTime().getMillis(), booking.getStartDate().getTime(), 1000 * 3);

        PowerMock.reset(LMMQTTProducer.class);
    }

    @Test
    public void testBookMachineWithConcurrentBookingError() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking inProgressBooking = new MachineBookingFactory()
            .inTheLastSeconds(13)
            .inProgress()
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.TOTEM)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        postCreate(token, jsonize(machine));

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .with("building", building)
            .withAuthentication()
            .authenticate();

        postCreate(token, jsonize(machine));

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();

        postCreate(null, jsonize(machine));

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(Machine machine) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("serial", machine == null ? null : machine.getSerialNumber());
    }
}
