package domains.back_office.controllers.v1;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import common.helpers.QueryParam;
import factories.FactoryException;
import factories.UserFactory;
import java.util.List;
import java.util.UUID;
import models.Role;
import models.User;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;
import queries.users.UserQuery;

public class UsersControllerTest extends common.RequestBaseTest {

    private void listUsers(String token, QueryParam[] params) {
        Call call = generateCall("GET", "/api/v1/users");

        request(call, token, params);
    }

    @Test
    public void testList() throws FactoryException {
        List<User> users = new UserFactory().create(10);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assertTrue(StringUtils.isNotBlank(token));

        listUsers(token, null);

        assertEquals(Http.Status.OK, status());

        assertEquals(users.size() + 1, jsonSize("users"));
    }

    @Test
    public void testListFilteringByKeyword() throws FactoryException {
        final String keyword = "this_is_a_long_keyword_to_match_by_accident";
        List<User> matchedUserByFirstName = new UserFactory().with("firstName", keyword).create(4);
        List<User> matchedUserByLastName = new UserFactory().with("lastName", keyword).create(2);
        List<User> matchedUserByEmail = new UserFactory()
            .with(
                "emailAddress",
                () -> {
                    return keyword + UUID.randomUUID() + "@mail.com";
                }
            )
            .create(3);
        List<User> unmatchedUser = new UserFactory().create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assertTrue(StringUtils.isNotBlank(token));

        listUsers(token, params(keyword));

        assertEquals(Http.Status.OK, status());

        assertEquals(
            matchedUserByFirstName.size() +
            matchedUserByLastName.size() +
            matchedUserByEmail.size(),
            jsonSize("users")
        );
        assertEquals(
            matchedUserByFirstName.size() +
            matchedUserByLastName.size() +
            matchedUserByEmail.size() +
            unmatchedUser.size() +
            1, // authenticated user
            new UserQuery().count()
        );
    }

    @Test
    public void testListFilteringByRole() throws FactoryException {
        List<User> matchedDeveloperUser = new UserFactory().with("role", Role.DEVELOPER).create(4);
        List<User> unmatchedUser = new UserFactory().create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assertTrue(StringUtils.isNotBlank(token));

        listUsers(token, params("", Role.DEVELOPER.name()));

        assertEquals(Http.Status.OK, status());

        assertEquals(matchedDeveloperUser.size(), jsonSize("users"));
        assertEquals(
            matchedDeveloperUser.size() + unmatchedUser.size() + 1, // authenticated user
            new UserQuery().count()
        );
    }

    @Test
    public void testListFilteringByKeywordAndRole() throws FactoryException {
        final String keyword = "this_is_a_long_keyword_to_match_by_accident";
        List<User> unmatchedUserByFirstName = new UserFactory()
            .with("firstName", keyword)
            .create(4);
        List<User> matchedUserByLastNameAndRole = new UserFactory()
            .with("lastName", keyword)
            .with("role", Role.DEVELOPER)
            .create(2);
        List<User> unmatchedDeveloperUser = new UserFactory()
            .with("role", Role.DEVELOPER)
            .create(4);
        List<User> unmatchedUser = new UserFactory().create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assertTrue(StringUtils.isNotBlank(token));

        listUsers(token, params(keyword, Role.DEVELOPER.name()));

        assertEquals(Http.Status.OK, status());

        assertEquals(matchedUserByLastNameAndRole.size(), jsonSize("users"));
        assertEquals(
            matchedUserByLastNameAndRole.size() +
            unmatchedUserByFirstName.size() +
            unmatchedDeveloperUser.size() +
            unmatchedUser.size() +
            1, // authenticated user
            new UserQuery().count()
        );
    }

    @Test
    public void testForbidden() throws FactoryException {
        String token = new UserFactory().withAuthentication().authenticate();

        assertTrue(StringUtils.isNotBlank(token));

        listUsers(token, null);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        new UserFactory().create(10);

        listUsers(null, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    /**
     * [keyword, role]
     */
    private QueryParam[] params(String... values) {
        if (values.length == 0) {
            return new QueryParam[] {};
        }

        QueryParam[] params = new QueryParam[values.length];
        params[0] = new QueryParam("keyword", values[0]);
        if (params.length >= 2) {
            params[1] = new QueryParam("role", values[1]);
        }

        return params;
    }
}
