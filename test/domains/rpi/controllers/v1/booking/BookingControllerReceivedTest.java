package domains.rpi.controllers.v1.booking;

import static org.easymock.EasyMock.*;
import static org.easymock.EasyMock.getCurrentArgument;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import domains.activations.mqtt.LMMQTTProducer;
import factories.*;
import models.Building;
import models.Machine;
import models.MachineBooking;
import models.Role;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.easymock.PowerMock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import play.mvc.Call;
import play.mvc.Http;
import services.machine.TotemBookingService;

@RunWith(PowerMockRunner.class)
@PrepareForTest(TotemBookingService.class)
@PowerMockIgnore("javax.management.*")
public class BookingControllerReceivedTest extends common.RequestBaseTest {

    private void postReceived(String token, String publicId, JsonNode json) {
        Call call = generateCall("POST", "/rpi/v1/booking/" + publicId + "/received");

        request(call, token, json);
    }

    @Test
    public void testBookingBookableReceived() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        postReceived(token, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.NO_CONTENT, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.ACTIVATION_PENDENT, booking.getStatus());
    }

    @Test
    public void testBookingBookableReceivedWithNotFoundBookingError() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        postReceived(token, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testBookingBookableReceivedWithNonPendingBookingError() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .cancelled()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        postReceived(token, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testBookingBookableReceivedOutOfTimeError() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(16)
            .with("machine", machine)
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        postReceived(token, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.BAD_REQUEST, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.EXPIRED, booking.getStatus());
    }

    @Test
    public void testBookingUnbookableReceived() throws Exception {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        // avoid calling MQTT client
        LMMQTTProducer mockProducer = PowerMock.createMock(LMMQTTProducer.class);
        PowerMock.expectNew(LMMQTTProducer.class).andReturn(mockProducer);
        expect(mockProducer.run(eq(building.getId()), eq(machine), anyString()))
            .andAnswer(() -> {
                String json = getCurrentArgument(2).toString();

                assertTrue(json.contains("\"RESULT\":\"64\""));
                assertTrue(
                    json.contains("\"MACHINE_SERIAL\":\"" + machine.getSerialNumber() + "\"")
                );
                assertTrue(json.contains("\"BUILDING_ID\":\"" + building.getId() + "\""));

                return "no log entries.";
            })
            .times(1);
        PowerMock.replay(mockProducer, LMMQTTProducer.class);

        postReceived(token, booking.getPublicId(), jsonize(false, "fake reason"));

        PowerMock.verify(mockProducer);

        assertEquals(Http.Status.NO_CONTENT, status());

        booking.refresh();
        assertEquals(MachineBooking.MachineBookingStatus.CANCELLED, booking.getStatus());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .create();

        String token = new UserFactory()
            .with("role", Role.USER)
            .withAuthentication()
            .authenticate();

        postReceived(token, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().with("building", building).create();
        MachineBooking booking = new MachineBookingFactory()
            .inProgress()
            .inTheLastSeconds(5)
            .with("machine", machine)
            .create();

        postReceived(null, booking.getPublicId(), jsonize(true));

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(boolean bookable) {
        return jsonize(bookable, StringUtils.EMPTY);
    }

    private JsonNode jsonize(boolean bookable, String reason) {
        return new ObjectMapper().createObjectNode().put("bookable", bookable);
    }
}
