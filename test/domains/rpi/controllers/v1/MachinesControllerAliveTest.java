package domains.rpi.controllers.v1;

import static org.junit.Assert.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import factories.*;
import java.util.concurrent.TimeUnit;
import models.*;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class MachinesControllerAliveTest extends common.RequestBaseTest {

    private void postAlive(String token, JsonNode json) {
        Call call = generateCall("POST", "/api/v1/alive");

        request(call, token, json);
    }

    @Test
    public void testAliveRecord() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .create();
        Firmware firmware = new FirmwareFactory().build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(mainMachine, privateIp, port, pendingUses, null, firmware.getFullVersion())
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());
    }

    @Test
    public void testAliveRecordWithSecondMachine() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .create();
        Machine secondMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .create();
        Firmware firmware = new FirmwareFactory().build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(
                mainMachine,
                privateIp,
                port,
                pendingUses,
                secondMachine,
                firmware.getFullVersion()
            )
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertEquals(rate.getPriceCustomer(), json().get("rate2").asDouble(), 0.01);
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());

        secondMachine.refresh();
        assertTrue(interval.contains(new DateTime(secondMachine.getLastAlive())));
    }

    @Test
    public void testAliveRecordIgnoringSecondMachineNotFound() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .create();
        Machine secondMachine = new MachineFactory().build();
        Firmware firmware = new FirmwareFactory().build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(
                mainMachine,
                privateIp,
                port,
                pendingUses,
                secondMachine,
                firmware.getFullVersion()
            )
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());
    }

    @Test
    public void testAliveRecordWithChildMachine() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine childMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .with("rpiChild", childMachine)
            .create();
        Firmware firmware = new FirmwareFactory().build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(mainMachine, privateIp, port, pendingUses, null, firmware.getFullVersion())
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());

        childMachine.refresh();
        assertTrue(interval.contains(new DateTime(childMachine.getLastAlive())));
    }

    @Test
    public void testAliveRecordOmitNullValues() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Firmware firmware = new FirmwareFactory().build();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .with("firmwareVersion", firmware.getFullVersion())
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        postAlive(token, jsonize(mainMachine, null, null, null, null, null));

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        String previousPrivateIp = mainMachine.getPrivateIp();
        String previousPort = mainMachine.getPort() + "";
        int previousPendingUses = mainMachine.getPendingUses();
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(previousPrivateIp, mainMachine.getPrivateIp());
        assertEquals(previousPort, mainMachine.getPort());
        assertEquals(previousPendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());
    }

    @Test
    public void testAliveRecordUpgradeFirmwareDone() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Firmware firmware = new FirmwareFactory().create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .with("firmware", firmware)
            .with("upgradeTo", firmware.getFullVersion())
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(mainMachine, privateIp, port, pendingUses, null, firmware.getFullVersion())
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertNull(json().get("frame_type"));
        assertNull(json().get("fw_url"));

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(firmware.getId(), mainMachine.getFirmware().getId());
        assertNull(mainMachine.getUpgradeTo());
        assertEquals(firmware.getFullVersion(), mainMachine.getFirmwareVersion());
    }

    @Test
    public void testAliveRecordUpgradeFirmwarePending() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Firmware currentFirmware = new FirmwareFactory().create();
        Firmware upgradeFirmware = new FirmwareFactory().create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .with("firmware", currentFirmware)
            .with("upgradeTo", upgradeFirmware.getFullVersion())
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(
            token,
            jsonize(
                mainMachine,
                privateIp,
                port,
                pendingUses,
                null,
                currentFirmware.getFullVersion()
            )
        );

        assertEquals(Http.Status.OK, status());

        rate.refresh();
        assertEquals(rate.getPriceCustomer(), json().get("rate1").asDouble(), 0.01);
        assertNull(json().get("rate2"));
        assertEquals("FIRMWARE_UPGRADE", json().get("frame_type").asText());
        assertEquals(upgradeFirmware.getUrl(), json().get("fw_url").asText());

        DateTime start = DateTime.now().minusMinutes(1);
        DateTime end = DateTime.now().plusMinutes(5);
        Interval interval = new Interval(start, end);
        mainMachine.refresh();
        assertTrue(interval.contains(new DateTime(mainMachine.getLastAlive())));
        assertFalse(mainMachine.getPublicIp().isEmpty());
        assertEquals(privateIp, mainMachine.getPrivateIp());
        assertEquals(port + "", mainMachine.getPort());
        assertEquals(pendingUses, mainMachine.getPendingUses());
        assertEquals(currentFirmware.getId(), mainMachine.getFirmware().getId());
        assertEquals(upgradeFirmware.getFullVersion(), mainMachine.getUpgradeTo());
        assertEquals(currentFirmware.getFullVersion(), mainMachine.getFirmwareVersion());
    }

    @Test
    public void testAliveRecordUpgradeFirmwarePendingWithMissingFirmwareVersionError()
        throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Firmware currentFirmware = new FirmwareFactory().create();
        Firmware upgradeFirmware = new FirmwareFactory().create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .with("firmware", currentFirmware)
            .with("upgradeTo", upgradeFirmware.getFullVersion())
            .create();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(token, jsonize(mainMachine, privateIp, port, pendingUses, null, null));

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testAliveRecordWithMissingMainMachineError() throws FactoryException {
        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(token, jsonize(null, privateIp, port, pendingUses, null, null));

        assertEquals(Http.Status.BAD_REQUEST, status());
    }

    @Test
    public void testAliveRecordWithNotFoundMainMachineError() throws FactoryException {
        Machine mainMachine = new MachineFactory().build();

        String token = new UserFactory().with("role", Role.RPI).withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(token, jsonize(mainMachine, privateIp, port, pendingUses, null, null));

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .create();

        String token = new UserFactory().withAuthentication().authenticate();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(token, jsonize(mainMachine, privateIp, port, pendingUses, null, null));

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Rate rate = new RateFactory().create();
        RateEvent rateEvent = new RateEventFactory().nonSplittable().withRate(rate).create();
        Building building = new BuildingFactory().with("rate", rate).create();
        Machine mainMachine = new MachineFactory()
            .with("building", building)
            .with("lastAlive", faker.date().past(2, TimeUnit.DAYS))
            .with("publicIp", "")
            .create();

        String privateIp = faker.internet().privateIpV4Address();
        int port = faker.number().numberBetween(1000, 9999);
        int pendingUses = faker.number().numberBetween(1000, 9999);

        postAlive(null, jsonize(mainMachine, privateIp, port, pendingUses, null, null));

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(
        Machine mainMachine,
        String privateIp,
        Integer port,
        Integer pendingUses,
        Machine secondMachine,
        String firmwareVersion
    ) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("serial", mainMachine == null ? null : mainMachine.getSerialNumber())
            .put("serial2", secondMachine == null ? null : secondMachine.getSerialNumber())
            .put("private_ip", privateIp)
            .put("port", port)
            .put("pending_uses", pendingUses)
            .put("fw_version", firmwareVersion);
    }
}
