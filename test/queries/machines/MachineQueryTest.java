package queries.machines;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import factories.CardFactory;
import factories.FactoryException;
import factories.MachineFactory;
import java.util.List;
import java.util.stream.Collectors;
import models.Machine;
import org.junit.Test;

public class MachineQueryTest extends common.BaseTest {

    @Test
    public void testMachineCount() throws FactoryException {
        Long machinesCount = 10L;
        new MachineFactory().create(machinesCount.intValue());

        MachineQuery query = new MachineQuery();
        Long count = query.count();

        assertEquals(machinesCount, count);
    }

    @Test
    public void testMachineAll() throws FactoryException {
        int machinesCount = 10;
        List<Machine> machines = new MachineFactory().create(machinesCount);

        MachineQuery query = new MachineQuery();
        List<Machine> result = query.all();

        assertEquals(machinesCount, result.size());
        assertCollection(
            machines.stream().map(Machine::getId).collect(Collectors.toList()),
            result.stream().map(Machine::getId).collect(Collectors.toList())
        );
    }

    @Test
    public void testMachineGet() throws FactoryException {
        int machinesCount = 10;
        Machine machine = new MachineFactory()
            .create(machinesCount)
            .get(faker.number().numberBetween(0, machinesCount - 1));

        MachineQuery query = new MachineQuery();
        Machine result = query.get(machine.getId());

        assertEquals(machine.getId(), result.getId());
    }

    @Test
    public void testFilterBySerialNumber() throws FactoryException {
        new MachineFactory().create(10);
        Machine machine = new MachineFactory().create();

        MachineQuery query = new MachineQuery().filterBySerialNumber(machine.getSerialNumber());

        Machine result = query.single();

        assertNotNull(machine);
        assertEquals(machine.getId(), result.getId());
    }

    @Test
    public void testPartHeirsAreNotMixed() throws FactoryException {
        Long machinesCount = 10L;
        new MachineFactory().create(machinesCount.intValue());
        new CardFactory().create(machinesCount.intValue());

        MachineQuery query = new MachineQuery();
        Long count = query.count();

        assertEquals(machinesCount, count);
    }
}
