package queries.buildings;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import factories.BuildingFactory;
import factories.FactoryException;
import java.util.List;
import java.util.stream.Collectors;
import models.Building;
import org.junit.Test;

public class BuildingQueryTest extends common.BaseTest {

    @Test
    public void testBuildingCount() throws FactoryException {
        Long buildingsCount = 10L;
        new BuildingFactory().create(10);

        BuildingQuery query = new BuildingQuery();
        Long count = query.count();

        assertEquals(buildingsCount, count);
    }

    @Test
    public void testBuildingAll() throws FactoryException {
        int buildingsCount = 10;
        List<Building> buildings = new BuildingFactory().create(buildingsCount);

        BuildingQuery query = new BuildingQuery();
        List<Building> result = query.all();

        assertEquals(buildingsCount, result.size());
        assertCollection(
            buildings.stream().map(Building::getId).collect(Collectors.toList()),
            result.stream().map(Building::getId).collect(Collectors.toList())
        );
    }

    @Test
    public void testBuildingGet() throws FactoryException {
        int buildingsCount = 10;
        Building building = new BuildingFactory()
            .create(buildingsCount)
            .get(faker.number().numberBetween(0, buildingsCount - 1));

        BuildingQuery query = new BuildingQuery();
        Building result = query.get(building.getId());

        assertEquals(building.getId(), result.getId());
    }

    @Test
    public void testFilterBySlug() throws FactoryException {
        new BuildingFactory().create(10);
        Building building = new BuildingFactory().create();

        BuildingQuery query = new BuildingQuery().filterBySlug(building.getSlug());

        Building result = query.single();

        assertNotNull(building);
        assertEquals(building.getId(), result.getId());
    }
}
