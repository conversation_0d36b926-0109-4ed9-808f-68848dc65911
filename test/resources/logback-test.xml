<configuration>

  <conversionRule conversionWord="coloredLevel" converterClass="play.api.Logger$ColoredLevel" />

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date - %thread - %level - %logger{35} - %message%n%ex{full,stacktrace}</pattern>
    </encoder>
  </appender>

  <!--
    The logger name is typically the Java/Scala package name.
    This configures the log level to log at for a package and its children packages.
  -->
  <logger name="play" level="INFO" />
  <logger name="application" level="DEBUG" />

  <logger name="org.junit" level="INFO" />

  <root level="ERROR">
    <appender-ref ref="STDOUT" />
  </root>

</configuration>
