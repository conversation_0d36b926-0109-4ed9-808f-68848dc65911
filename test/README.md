# Unit test

**Principles to write tests**

- all tests are independent
- the order of how they are run must not impact in the result
- we are better writing code than testing manually
- cover as many cases as you can

## BaseTest class

Give us the configuration that test classes need to run each test. All test have to inherit from this class (directly o indirectly before).

### Factories

Factories are the set of classes that help us to build the data ecosystem required to run a certain test.
As a good practice, we should create a new whole set of data each time we run an individual test.

#### First step

Extends from `BaseModelFactory` indicating the model

```java
public class UserFactory extends BaseModelFactory<User> {
    ...
}
```

The next that we have to do is to implement the methods `initializeEntity` and `setDefaultProps`.

`initializeEntity` expects to create a new instance of the model

```java
public class UserFactory extends BaseModelFactory<User> {

  @Override
  protected void initializeEntity() {
    this.entity = new User();
  }
}

```

`setDefaultProps` expects to set all the default values of the model with the help of [faker](https://github.com/DiUS/java-faker), that generates fake data for us. [[doc]](http://dius.github.io/java-faker/apidocs/index.html)

```java
public class UserFactory extends BaseModelFactory<User> {
    @Override
    protected void setDefaultProps() {
        this.entity.setEmailAddress(faker.internet().emailAddress());
        this.entity.setFirstName(faker.name().firstName());
        ...
    }
}
```

#### Define associations

The associations or relations with other classes, it is recommended to set following the patter `with<another-model>`.
Inside that method the most common operation is to call another factory and set that value to the local entity.

```java
public UserFactory withSession() {
    this.entity.getSessions().add(
        new SessionFactory().withUser(this.entity).build()
    );

    return this;
}
```

Also, it is valid to mix some associations to represent states:

```java
public UserFactory withAuthentication() {
    return withAccount().withSession();
}
```

**NOTE**: take a look at the return type of all these methods, they return the factory itself to be able to concatenate the different modifiers.

#### Actions

**`build()`**: this action just build the new entity without saving it.
**`create()`**: this action does both, build and save it.

Both actions accept as a parameter a number to indicate if we want to create a list of entities.

```java
List<User> users = new UserFactory().create(10);
Building building = new BuildingFactory().create();
```

#### `with` method

This method allows us to edit any attribute of the entity when the default values defined are not enough to represents the reality that we need.
It accepts two type of values, mostly thinking in the case of creating a batch of entities.

- fixed - any object known.
- dynamic - lambda, which will be run each time the value is going to be needed.

```java
// Fixed
List<User> matchedUserByLastName = new UserFactory()
    .with("lastName", keyword)
    .create(2);
```

```java
// Dynamic - Otherwise, the db throws an exception becuase we are saving the same email more than once.
List<User> matchedUserByEmail = new UserFactory()
    .with("emailAddress", () -> {
        return keyword + UUID.randomUUID() + "@mail.com";
    })
    .create(3);
```

<!---

#### Decorator patter

If you are in the situation where you need some information from your factory but it is nested in associations or has to be calculated.
You could use the decorator pattern. A brief example how from the user factory, we can get the token for authentication:

--->

### Models test

#### First step

Create a class with the same name of the model and add the word `Test` at the end, and extend from `common.BaseTest`

```java
public class UserTest extends common.BaseTest {
    ...
}
```

#### Create a new test

1. Create method that start with the word `test` and indicate what you are going to test and which the state is.
2. Add the annotation `@Test`

```java
@Test
public void testFindByEmailAddress() {
    ...
}
```

Then use the resources mentioned in this guide to create different scenarios, and then check the results against the expected ones.

```java
@Test
public void testFindByEmailAddress() {
    // Preconditions: Existing user
    User storedUser = new UserFactory().create();

    // Make action call
    User user = User.findByEmailAddress(storedUser.getEmailAddress());

    // Comparte results
    assertNotNull(user);
    assertEquals(storedUser.getEmailAddress(), user.getEmailAddress());
    assertEquals(storedUser.getFirstName(), user.getFirstName());
    assertEquals(storedUser.getLastName(), user.getLastName());
    assertEquals(storedUser.getRole(), user.getRole());
}
```

## RequestBaseTest class

<!---
TODO: explain what this class does
--->

Give us most of the helpers that are needed to create a new test class based on a request.

Some of them are:

- `request`: make a request to the action indicated.
- `status`: get the status code of the request's response.
- `json`: get the body of the request's response parsed as a json.
- `jsonSize`: get the length of the json response when it's a list.
- `text`: get the body of the request's response parsed as a text.

### Controllers test

#### First step

Create a class with the same name of the controller and add the word `Test` at the end, and extend from `common.RequestBaseTest`

```java
public class UsersControllerTest extends common.RequestBaseTest {
    ...
}
```

**NOTE**: Keep in mind that controllers usually have many actions. It is a good idea to create a file per action!
For example `PartsControllerListPartsMachineTypeTest` class is in charge of testing just one of the actions of the `PartsController`, and only testing one of the possible types.
Following this practice will help us to keep test files small as possible.

#### Create a new test

1. Create method that start with the word `test` and indicate what you are going to test and which the state is.
2. Add the annotation `@Test`

```java
@Test
public void testList() {
    ...
}
```

Then use the resources mentioned in this guide to create different scenarios, and then check the results against the expected ones.

```java
@Test
public void testList() throws FactoryException {
    // Preconditions: List of users
    List<User> users = new UserFactory().create(10);

    // Authenticate user
    String token = new UserFactory()
        .with("role", Role.MASTER)
        .withAuthentication()
        .authenticate();

    assertTrue(StringUtils.isNotBlank(token));

    // Make request
    listUsers(token, null);

    // Comparte results
    assertEquals(Http.Status.OK, status());
    assertEquals(users.size() + 1, jsonSize("users"));
}
```

#### Routes

In order to be more expressive, declare them directly.
Use the method `generateCall(<method>, <path>)`, ex: `generateCall("GET", "/api/v1/users");`

It is recommended to create a method which describes the operation tested and encapsulates the logic around making the endpoint call.

```java
private void listUsers(String token, QueryParam[] params) {
    Call call = generateCall("GET", "/api/v1/users");

    request(call, token, params);
}
```

#### Reloading entities

Most of cases when entities are changed during a request call, the entities are not up-to-date with the last values.
For that reason, they have to be reloaded, and to do it there is a method called `reload` which accepts as a param the entity itself.

```java
@Test
public void testWasherAssignation() throws FactoryException {
    ...

    // endpoint which set the building to a machine
    assignMachine(token, building, machine);

    reload(machine);

    assertNotNull(machine.getBuilding());
    ...
}
```

## Running Tests via Command Line

This project uses **JUnit** for testing (not ScalaTest), which affects the command syntax for running specific tests.

### **🚀 Basic Test Commands:**

#### **Run All Tests:**

```bash
# Run all tests in the project
./activator test
```

#### **Run Specific Test Class:**

```bash
# Run a specific test class (recommended approach)
./activator "testOnly controllers.WebControllerTest"
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest"

# Run multiple specific test classes
./activator "testOnly controllers.WebControllerTest controllers.PartsControllerListPartsMachineTypeTest"
```

#### **Run Tests by Pattern:**

```bash
# Run all tests in a specific package
./activator "testOnly controllers.*"
./activator "testOnly domains.rpi.*"
```

### **🎯 Running Specific Test Methods:**

Since this project uses **JUnit**, use the `--tests=` syntax (not ScalaTest's `-z` option):

#### **Single Test Method:**

```bash
# ✅ CORRECT - JUnit syntax
./activator "testOnly controllers.WebControllerTest -- --tests=testPing"
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=testMultipleSearchAsMASTER"
```

#### **Multiple Test Methods with Regex Pattern:**

```bash
# Run methods matching a regex pattern
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=.*Multiple.*"

# This runs both:
# - testMultipleSearchAsMASTER
# - testMultipleSearchAsASSISTANT

# Run all test methods starting with "testSingle"
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=testSingle.*"

# Run all test methods containing "Unauthorized"
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=.*Unauthorized.*"

# Run all test methods ending with "AsMASTER"
./activator "testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=.*AsMASTER"
```

## Watch Mode (Continuous Testing)

The `~` (tilde) prefix in sbt/activator commands enables **watch mode**, which automatically reruns the specified tests whenever relevant source files change. This is especially useful for Test-Driven Development (TDD) and rapid feedback cycles, as you don't need to manually re-run your test commands after every code edit.

You can use `~` with any supported test command to continuously monitor your codebase and keep your test results up-to-date as you work.

### **🎯 Watch Test Commands:**

#### **Watch All Tests:**

```bash
# Rerun all tests when any file changes
./activator "~test"

# Rerun only failed/changed tests (faster)
./activator "~testQuick"
```

#### **Watch Specific Test Class:**

```bash
# Watch and rerun specific test class
./activator "~testOnly controllers.WebControllerTest"
./activator "~testOnly controllers.PartsControllerListPartsMachineTypeTest"
```

#### **Watch Specific Test Method:**

```bash
# Watch and rerun specific test method
./activator "~testOnly controllers.WebControllerTest -- --tests=testPing"
./activator "~testOnly controllers.PartsControllerListPartsMachineTypeTest -- --tests=.*Multiple.*"
```

#### **Watch Tests by Pattern:**

```bash
# Watch all controller tests
./activator "~testOnly *ControllerTest"

# Watch all tests in specific package
./activator "~testOnly controllers.*"
./activator "~testOnly domains.rpi.*"
```

### **🛑 Stopping Watch Mode:**

- **Press `Enter`** or **`Ctrl+D`** to stop watch mode and return to shell
- **Press `Ctrl+C`** to force terminate if needed

### **💡 Watch Mode Tips:**

1. **Start specific**: Use `~testOnly` for the class you're working on
2. **Use testQuick**: `~testQuick` only runs tests affected by changes
3. **Combine with patterns**: `~testOnly *ControllerTest` watches all controller tests
4. **Terminal setup**: Run watch mode in a dedicated terminal window

#### 🚀 **Simplified Test Script**

For a more user-friendly testing experience, use the `./test.sh` script that simplifies the command syntax:

##### **📋 Quick Start:**

```bash
# Run all tests
./test.sh

# Run specific test class
./test.sh controllers.WebControllerTest

# Run specific test method (auto-detects pattern)
./test.sh controllers.WebControllerTest.testPing

# Run with watch mode (continuous testing)
# -w, --watch
./test.sh -w controllers.WebControllerTest

# Run only quick/changed tests
# -q, --quick
./test.sh -q

# Run all controller tests
./test.sh controllers.*

# Silent mode (disable verbose output)
# -s, --silent
./test.sh -s controllers.WebControllerTest.testPing

# Get help
./test.sh --help
```
