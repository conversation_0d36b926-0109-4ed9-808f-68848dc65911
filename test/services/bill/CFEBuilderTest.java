package services.bill;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;

import domains.billing.dto.Branch;
import domains.billing.invoicing.sicfe.types.SicfeInvoicingServiceConfig;
import domains.exchange_rate.services.exceptions.ExchangeRateNotFoundException;
import factories.CfeFactory;
import factories.ExchangeRateFactory;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import javax.xml.transform.*;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import models.ExchangeRate;
import org.junit.Test;

public class CFEBuilderTest extends common.BaseTest {

    SicfeInvoicingServiceConfig config = new TestInvoicingServiceConfig();

    private String build(CFE cfe) throws Exception {
        String xml = new CFEBuilder(cfe, this.config).build();
        return formatXml(xml); // format to compare the same style
    }

    // --------------------- eTck --------------------- //

    @Test
    public void testETicket() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketCreditNote() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().creditNote().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-credit_note");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketDebitNote() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().debitNote().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-debit_note");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketReceipt() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().receipt().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-receipt");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketReceiptCancelled() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().receiptCancelled().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-receipt-cancelled");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketTaxFree() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().taxFree().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular-tax_free");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketBooked() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().booked().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular-booked");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketWithRepeatedItems() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().withDuplication().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular-multiple");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketDifferentBranch() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().withBranch().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular-branch");

        assertEquals(expected, result);
    }

    @Test
    public void testETickEmptyBranch() throws Exception {
        Branch emptyBranch = new Branch();
        CFE cfe = new CfeFactory().e_ticket().periodical().withBranch(emptyBranch).build();

        String result = build(cfe);

        String expected = getResource("e_ticket-regular");

        assertEquals(expected, result);
    }

    @Test
    public void testETicketCreditNoteWithoutReference() throws Exception {
        CFE cfe = new CfeFactory().e_ticket().periodical().creditNote().withoutReference().build();

        String result = build(cfe);

        String expected = getResource("e_ticket-credit_note-unreferenced");

        assertEquals(expected, result);
    }

    // --------------------- eFact --------------------- //

    @Test
    public void testEFact() throws Exception {
        CFE cfe = new CfeFactory().e_factura().build();

        String result = build(cfe);

        String expected = getResource("e_fact-regular");

        assertEquals(expected, result);
    }

    @Test
    public void testEFactCreditNote() throws Exception {
        CFE cfe = new CfeFactory().e_factura().creditNote().build();

        String result = build(cfe);

        String expected = getResource("e_fact-credit_note");

        assertEquals(expected, result);
    }

    @Test
    public void testEFactDebitNote() throws Exception {
        CFE cfe = new CfeFactory().e_factura().debitNote().build();

        String result = build(cfe);

        String expected = getResource("e_fact-debit_note");

        assertEquals(expected, result);
    }

    @Test
    // --------------------- eFact_Exp --------------------- //

    public void testEFactExport() throws Exception {
        ExchangeRate exchangeRate = new ExchangeRateFactory().withUSD().create();

        CFE cfe = new CfeFactory().e_factura().export().build();

        String result = build(cfe);

        String expected = getResource("e_fact_exp-regular");

        assertEquals(expected, result);
    }

    @Test
    public void testEFactExportMemorizingExchangeRate() throws Exception {
        ExchangeRate exchangeRate = new ExchangeRateFactory().withUSD().create();

        CFE cfe = new CfeFactory().e_factura().export().build();

        String result = build(cfe);
        String expected = getResource("e_fact_exp-regular");
        assertEquals(expected, result);

        exchangeRate.setValue(2.0);
        exchangeRate.update();

        result = build(cfe);
        assertEquals(expected, result);

        assertEquals(2.0, exchangeRate.getValue(), 0.0);
    }

    @Test
    public void testEFactExportWithoutExchangeRateError() {
        CFE cfe = new CfeFactory().e_factura().export().build();

        assertThrows(
            ExchangeRateNotFoundException.class,
            () -> {
                build(cfe);
            }
        );
    }

    /**
     * Get the content from XML files
     */
    private String getResource(String name) {
        try {
            Path path = Paths.get("test/resources/services/bill/CFEBuilderTest/" + name + ".xml");
            String content = new String(Files.readAllBytes(path));
            return formatXml(content);
        } catch (IOException e) {
            System.out.println("Error reading resource file: " + e.getMessage());
            return "";
        }
    }

    /**
     * Formats the XML string to the same format always
     */
    private String formatXml(String xml) {
        try {
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

            String input = xml
                .replaceAll(">\\s+<", "><")
                .replace("\t", "")
                .replace("\n", "")
                .replace("\r", "");
            StreamSource source = new StreamSource(new StringReader(input));
            StringWriter stringWriter = new StringWriter();
            StreamResult result = new StreamResult(stringWriter);

            transformer.transform(source, result);

            return stringWriter.toString();
        } catch (TransformerException e) {
            System.out.println("Error formatting XML: " + e.getMessage());
            return "";
        }
    }
}
