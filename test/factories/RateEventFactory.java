package factories;

import java.util.concurrent.TimeUnit;
import models.Rate;
import models.RateEvent;

public class RateEventFactory extends BaseModelFactory<RateEvent> {

    @Override
    protected void initializeEntity() {
        this.entity = new RateEvent();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setPriceCustomer(faker.number().randomDouble(0, 1, 1000));
        this.entity.setPriceCompany(faker.number().randomDouble(0, 1, 1000));
        this.entity.setPriceM3(faker.number().randomDouble(0, 1, 1000));
        this.entity.setPriceKWh(faker.number().randomDouble(0, 1, 1000));
        this.entity.setPriceCardReplacement(faker.number().randomDouble(0, 1, 1000));
        this.entity.setMinUsesPerWasher(faker.number().numberBetween(0, 100));
        this.entity.setValidFrom(faker.date().past(1, TimeUnit.DAYS));
        this.entity.setValidUntil(faker.date().future(90, TimeUnit.DAYS));
    }

    public RateEventFactory withRate(Rate rate) {
        this.entity.setRate(rate);

        return this;
    }

    public RateEventFactory splittable() {
        this.entity.setPriceCustomer(faker.number().randomDouble(0, 1, 1000));
        double price = 0.0;
        do {
            price = faker.number().randomDouble(0, 1, 1000);
            this.entity.setPriceCompany(price);
        } while (price == this.entity.getPriceCustomer());

        return this;
    }

    public RateEventFactory nonSplittable() {
        double price = faker.number().randomDouble(0, 1, 1000);
        this.entity.setPriceCustomer(price);
        this.entity.setPriceCompany(price);

        return this;
    }
}
