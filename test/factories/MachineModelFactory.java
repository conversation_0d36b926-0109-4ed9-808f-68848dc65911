package factories;

import models.MachineModel;

public class MachineModelFactory extends BaseModelFactory<MachineModel> {

    @Override
    protected void initializeEntity() {
        this.entity = new MachineModel();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.funnyName().name());
    }

    public MachineModelFactory withWasherParameters() {
        this.entity.setParameter(new MaintenanceParameterFactory().withMp1200(0).build());

        return this;
    }

    public MachineModelFactory withDryerParameters() {
        this.entity.setParameter(
                new MaintenanceParameterFactory().withMp100(0).withMp500(0).build()
            );

        return this;
    }
}
