package factories;

import models.BillingSetting;

public class BillingSettingFactory extends BaseModelFactory<BillingSetting> {

    @Override
    protected void initializeEntity() {
        this.entity = new BillingSetting();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setEnabledPagosWebWithSplit(false);
    }

    public BillingSettingFactory enabledPagosWebWithSplit() {
        this.entity.setEnabledPagosWebWithSplit(true);

        return this;
    }

    public BillingSettingFactory enabledRedPagosWithSplit() {
        this.entity.setEnabledRedPagosWithSplit(true);

        return this;
    }
}
