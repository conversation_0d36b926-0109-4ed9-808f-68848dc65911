package factories;

import java.util.concurrent.TimeUnit;
import models.TinyUrl;

public class TinyUrlFactory extends BaseModelFactory<TinyUrl> {

    @Override
    protected void initializeEntity() {
        this.entity = new TinyUrl();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setToken(faker.random().hex());
        this.entity.setIsActive(false);
        this.entity.setDescription(faker.lorem().sentence());
        this.entity.setDestinationUrl(faker.internet().url());
        this.entity.setCreatedAt(faker.date().past(1, TimeUnit.MINUTES));
        this.entity.setUpdatedAt(faker.date().past(1, TimeUnit.MINUTES));
    }

    public TinyUrlFactory active() {
        this.entity.setIsActive(true);

        return this;
    }
}
