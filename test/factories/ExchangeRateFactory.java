package factories;

import models.Currency;
import models.ExchangeRate;

public class ExchangeRateFactory extends BaseModelFactory<ExchangeRate> {

    @Override
    protected void initializeEntity() {
        this.entity = new ExchangeRate();
    }

    @Override
    protected void setDefaultProps() {}

    public ExchangeRateFactory withUSD() {
        this.entity = new ExchangeRate(Currency.USD.getIsoCode());
        this.entity.setValue(1.5);

        return this;
    }
}
