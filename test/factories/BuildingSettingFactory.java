package factories;

import java.util.concurrent.TimeUnit;
import models.BuildingSetting;

public class BuildingSettingFactory extends BaseModelFactory<BuildingSetting> {

    @Override
    protected void initializeEntity() {
        this.entity = new BuildingSetting();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setPreBlockedUseEnabled(0);
        this.entity.setGoogleMapsDescription(faker.address().fullAddress());
        this.entity.setGoogleMapsLink(faker.internet().url());
        this.entity.setOpeningTime(faker.date().past(1, TimeUnit.HOURS));
        this.entity.setClosingTime(faker.date().future(2, TimeUnit.HOURS));
    }
}
