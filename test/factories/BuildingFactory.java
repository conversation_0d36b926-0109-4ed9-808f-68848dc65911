package factories;

import java.util.concurrent.TimeUnit;
import models.*;

public class BuildingFactory extends BaseModelFactory<Building> {

    @Override
    protected void initializeEntity() {
        this.entity = new Building();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.funnyName().name());
        this.entity.setAddress(faker.address().streetAddress());
        this.entity.setCity(faker.address().cityName());
        this.entity.setCountry(faker.address().country());
        this.entity.setDepartment(faker.address().state());
        this.entity.setLastBillingDate(faker.date().future(1, TimeUnit.DAYS));
        this.entity.setIsRemoteActivationEnabled(true);
        this.entity.generateSlug();
        this.entity.setRut(faker.number().digits(10));
        this.entity.setContact(faker.phoneNumber().phoneNumber());
    }

    public BuildingFactory postpaid() {
        this.entity.setContractType(Card.ContractType.POSTPAID);
        this.entity.setInvoicingMethod(InvoicePaymentMethod.CREDIT);

        return this;
    }

    public BuildingFactory prepaid() {
        this.entity.setContractType(Card.ContractType.PREPAID);
        this.entity.setInvoicingMethod(InvoicePaymentMethod.CASH);

        return this;
    }

    public BuildingFactory withAdministration() {
        this.entity.setAdministration(new AdministrationFactory().build());

        return this;
    }

    public BuildingFactory withRate() throws FactoryException {
        this.entity.setRate(new RateFactory().withRateEvents().build());

        return this;
    }

    public BuildingFactory withUnit() {
        this.entity.getUnits().add(new UnitFactory().build());

        return this;
    }

    public BuildingFactory withMachine() {
        this.entity.getMachines().add(new MachineFactory().build());

        return this;
    }

    public BuildingFactory withPagosWebSplittingEnabled() {
        BillingSetting billingSetting = new BillingSettingFactory()
            .enabledPagosWebWithSplit()
            .create();
        this.entity.setBillingSetting(billingSetting);

        return this;
    }

    public BuildingFactory withRedPagosSplittingEnabled() {
        BillingSetting billingSetting = new BillingSettingFactory()
            .enabledRedPagosWithSplit()
            .create();
        this.entity.setBillingSetting(billingSetting);

        return this;
    }

    public BuildingFactory laundromat() {
        this.entity.setBuildingType(BuildingType.LAUNDROMAT);

        return this;
    }

    public BuildingFactory withGoogleMapsSettings() {
        BuildingSetting setting = new BuildingSettingFactory().build();
        this.entity.getBuildingSetting().setGoogleMapsLink(setting.getGoogleMapsLink());
        this.entity.getBuildingSetting()
            .setGoogleMapsDescription(setting.getGoogleMapsDescription());

        return this;
    }
}
