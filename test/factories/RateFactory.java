package factories;

import models.Rate;

public class RateFactory extends BaseModelFactory<Rate> {

    @Override
    protected void initializeEntity() {
        this.entity = new Rate();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.funnyName().name());
        this.entity.setDescriptiveMessage(faker.lorem().sentence());
    }

    public RateFactory withRateEvents() throws FactoryException {
        new RateEventFactory().with("rate", this.entity).create();

        return this;
    }
}
