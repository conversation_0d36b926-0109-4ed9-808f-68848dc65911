package factories;

import models.Unit;

public class UnitFactory extends BaseModelFactory<Unit> {

    @Override
    protected void initializeEntity() {
        this.entity = new Unit();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setTower(faker.number().digit());
        this.entity.setNumber(String.format("%03d", faker.number().randomDigitNotZero()));
        this.entity.setContact(faker.phoneNumber().cellPhone());
        this.entity.setAccredited(true);
    }
}
