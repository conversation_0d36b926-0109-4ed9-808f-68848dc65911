package factories;

import java.util.concurrent.TimeUnit;
import models.Account;
import models.Role;
import models.Session;
import models.User;
import org.apache.commons.lang3.StringUtils;

public class UserFactory extends BaseModelFactory<User> {

    private Account account;

    @Override
    protected void initializeEntity() {
        this.entity = new User();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setEmailAddress(faker.internet().emailAddress());
        this.entity.setFirstName(faker.name().firstName());
        this.entity.setLastName(faker.name().lastName());
        this.entity.setRole(Role.USER);
        this.entity.setCreationDate(faker.date().past(100, 50, TimeUnit.DAYS));
        this.entity.setFechaNacimiento(faker.date().birthday());
        this.entity.setIsActiveUser(true);
    }

    public UserFactory withAuthentication() {
        return withAccount().withSession();
    }

    public UserFactory withAccount() {
        this.account = new AccountFactory().withOwner(this.entity).build();

        return this;
    }

    public UserFactory withSession() {
        this.entity.getSessions().add(new SessionFactory().withUser(this.entity).build());

        return this;
    }

    public UserFactory withPassword(String password) {
        this.entity.setPassword(password);
        return this;
    }

    public UserFactory inactive() {
        this.entity.setIsActiveUser(false);
        return this;
    }

    @Override
    public User create() {
        super.create();

        if (this.account != null) {
            this.account.save();
        }

        return this.entity;
    }

    /**
     * Create the user and return the valid token to use during the session
     */
    public String authenticate() throws FactoryException {
        this.create();
        return decorate().getToken();
    }

    @Override
    public UserFactory with(String name, Object value) throws FactoryException {
        return (UserFactory) super.with(name, value);
    }

    public UserDecorated decorate() throws FactoryException {
        return new UserDecorated(this.entity);
    }

    public class UserDecorated extends User {

        public UserDecorated(User user) throws FactoryException {
            try {
                org.apache.commons.beanutils.BeanUtils.copyProperties(this, user);
            } catch (Exception e) {
                throw FactoryException.whileDecorating().withCause(e);
            }
        }

        public String getToken() {
            if (this.getSessions() != null && !this.getSessions().isEmpty()) {
                for (Session session : this.getSessions()) {
                    return session.getToken();
                }
            }

            return StringUtils.EMPTY;
        }

        @Override
        public Account getMasterAccount() {
            return UserFactory.this.account;
        }
    }
}
