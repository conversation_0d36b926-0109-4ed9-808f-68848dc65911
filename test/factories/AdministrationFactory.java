package factories;

import java.util.concurrent.TimeUnit;
import models.Administration;

public class AdministrationFactory extends BaseModelFactory<Administration> {

    @Override
    protected void initializeEntity() {
        this.entity = new Administration();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.funnyName().name());
        this.entity.setAddress(faker.address().streetAddress());
        this.entity.setContact(faker.phoneNumber().phoneNumber());
        this.entity.setAddress(faker.address().streetAddress());
        this.entity.setClosureDay(faker.number().numberBetween(1, 28));
        this.entity.setMpCreationDate(faker.date().past(1, TimeUnit.DAYS));
    }
}
