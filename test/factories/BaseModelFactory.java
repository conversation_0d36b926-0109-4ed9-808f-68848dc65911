package factories;

import com.github.javafaker.Faker;
import com.play4jpa.jpa.models.Model;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

public abstract class BaseModelFactory<T extends Model<? super T>> implements Cloneable {

    protected T entity = null;
    protected Map<String, Object> attributes = new HashMap<>();
    protected Map<String, Supplier<Object>> attributesGetter = new HashMap<>();

    /**
     * lib to generate testing closureData.
     * http://dius.github.io/java-faker/apidocs/index.html
     * https://github.com/faker-ruby/faker/tree/master/doc/default
     */
    protected static final Faker faker = new Faker();

    protected BaseModelFactory() {
        initializeEntity();

        assert this.entity != null;

        setDefaultProps();
    }

    /**
     * Build a new entity
     */
    public T build() {
        return this.entity;
    }

    /**
     * Build and Save a new entity
     */
    public T create() {
        this.beforeCreate();
        this.entity.save();

        return this.entity;
    }

    protected void beforeCreate() {}

    /**
     * Build a series of entities
     */
    public List<T> build(int count) {
        List<T> items = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            try {
                BaseModelFactory<T> factory = this.clone();
                items.add(factory.build());
            } catch (CloneNotSupportedException e) {
                play.Logger.error("Unable to execute batch action");
            }
        }

        return items;
    }

    /**
     * Build and Save a series of entities
     */
    public List<T> create(int count) throws FactoryException {
        List<T> items = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            try {
                BaseModelFactory<T> factory = this.clone();
                items.add(factory.create());
            } catch (Exception e) {
                throw FactoryException.whileBatchCreation().withCause(e);
            }
        }

        return items;
    }

    /**
     * Set any attributes without having a set method
     *
     * @param attribute name of the class attribute
     * @param value     value of the attribute
     */
    public BaseModelFactory<T> with(String attribute, Object value) throws FactoryException {
        this.attributes.put(attribute, value);

        setAttribute(attribute, value);

        return this;
    }

    /**
     * Set any attributes without having a set method
     *
     * @param attribute name of the class attribute
     * @param func      function to calculate the value of the attribute in runtime
     */
    public BaseModelFactory<T> with(String attribute, Supplier<Object> func)
        throws FactoryException {
        this.attributes.put(attribute, func);

        setAttribute(attribute, func.get());

        return this;
    }

    /**
     *
     * @param attribute
     * @param value
     * @throws FactoryException
     */
    private void setAttribute(String attribute, Object value) throws FactoryException {
        assert this.entity != null;

        try {
            Object result = value;
            if (result instanceof Supplier) {
                result = ((Supplier<Object>) result).get();
            }
            org.apache.commons.beanutils.BeanUtils.copyProperty(this.entity, attribute, result);
        } catch (Exception e) {
            throw FactoryException.whileSettingAttribute(attribute).withCause(e);
        }
    }

    /**
     * Create instance of T.
     * Expected code inside `entity = new T();`
     */
    protected abstract void initializeEntity();

    protected abstract void setDefaultProps();

    @Override
    protected BaseModelFactory<T> clone() throws CloneNotSupportedException {
        BaseModelFactory<T> factory = (BaseModelFactory<T>) super.clone();

        factory.initializeEntity();
        factory.setDefaultProps();

        try {
            for (Map.Entry<String, Object> entry : this.attributes.entrySet()) {
                Object result = entry.getValue();
                if (result instanceof Supplier) {
                    result = ((Supplier<Object>) result).get();
                }
                org.apache.commons.beanutils.BeanUtils.copyProperty(
                    factory.entity,
                    entry.getKey(),
                    result
                );
            }
        } catch (Exception e) {
            play.Logger.error("Unable to clone factory entity");
        }

        return factory;
    }
}
