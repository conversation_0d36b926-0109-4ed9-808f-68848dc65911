package factories;

import java.util.concurrent.TimeUnit;
import models.MachineBooking;
import org.joda.time.DateTime;

public class MachineBookingFactory extends BaseModelFactory<MachineBooking> {

    @Override
    protected void initializeEntity() {
        this.entity = new MachineBooking();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.IN_PROGRESS);
        this.entity.setStartDate(faker.date().past(1, TimeUnit.SECONDS));
    }

    public MachineBookingFactory inTheLastSeconds(int seconds) {
        this.entity.setStartDate(new DateTime().minusSeconds(seconds).toDate());
        return this;
    }

    public MachineBookingFactory inProgress() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.IN_PROGRESS);
        return this;
    }

    public MachineBookingFactory cancelled() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.CANCELLED);
        return this;
    }

    public MachineBookingFactory completed() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.COMPLETED);
        return this;
    }

    public MachineBookingFactory activationPendent() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.ACTIVATION_PENDENT);
        return this;
    }

    public MachineBookingFactory expired() {
        this.entity.setStatus(MachineBooking.MachineBookingStatus.EXPIRED);
        return this;
    }
}
