package factories;

import common.helpers.DateTimer;
import java.util.concurrent.TimeUnit;
import models.Card;
import models.Part;
import models.User;

public class CardFactory extends BaseModelFactory<Card> {

    @Override
    protected void initializeEntity() {
        this.entity = new Card();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setUuid(faker.internet().uuid());
        this.entity.setStartTimeOfUse(DateTimer.ago(1, TimeUnit.HOURS));
        this.entity.setEndTimeOfUse(DateTimer.advance(1, TimeUnit.HOURS));
        this.entity.setDiscount(0.0);
        this.entity.setMaster(false);
        this.entity.setBalance(faker.number().randomDouble(0, 0, 1000));
        this.entity.setState(Part.PartState.ACTIVE);
        this.entity.setSubState(Part.PartState.ACTIVE);
        this.entity.setAlias(faker.lorem().word());
    }

    public CardFactory master() {
        this.entity.setMaster(true);

        return this;
    }

    public CardFactory prepaid() {
        this.entity.setContractType(Card.ContractType.PREPAID);

        return this;
    }

    public CardFactory postpaid() {
        this.entity.setContractType(Card.ContractType.POSTPAID);

        return this;
    }

    public CardFactory virtual() {
        this.entity.setUuid(Card.VIRTUAL_CARD_PREFIX + this.entity.getUuid());

        return this;
    }

    public CardFactory deactivated() {
        this.entity.setState(Part.PartState.INACTIVE);
        this.entity.setSubState(Part.PartState.SUSPENDED);

        return this;
    }

    public CardFactory lost() {
        this.entity.setState(Part.PartState.INACTIVE);
        this.entity.setSubState(Part.PartState.LOST);

        return this;
    }

    public CardFactory inactive() {
        this.entity.setState(Part.PartState.INACTIVE);

        return this;
    }

    public CardFactory preBlocked() {
        this.entity.setState(Part.PartState.PRE_BLOCKED);
        this.entity.setPreBlockedUses(faker.number().numberBetween(1, 10));

        return this;
    }

    public CardFactory withEvent() throws FactoryException {
        this.entity.setCardEvents(
                new CardEventFactory().with("uid", this.entity.getUuid()).build(1)
            );

        return this;
    }

    public CardFactory withOwner(User user) {
        this.entity.setPrePaidCardholder(user);

        return this;
    }
}
