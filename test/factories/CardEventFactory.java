package factories;

import java.util.concurrent.TimeUnit;
import models.CardEvent;
import models.CardEventType;

public class CardEventFactory extends BaseModelFactory<CardEvent> {

    @Override
    protected void initializeEntity() {
        this.entity = new CardEvent();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setTimestamp(faker.date().past(1, TimeUnit.DAYS));
        this.entity.setHeadline(faker.lorem().sentence());
        this.entity.setEventType(CardEventType.CARD_CREATED);
    }
}
