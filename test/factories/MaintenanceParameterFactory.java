package factories;

import models.MaintenanceParameter;

public class MaintenanceParameterFactory extends BaseModelFactory<MaintenanceParameter> {

    @Override
    protected void initializeEntity() {
        this.entity = new MaintenanceParameter();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setMp100(faker.number().numberBetween(1, 1000));
        this.entity.setMp500(faker.number().numberBetween(1, 1000));
        this.entity.setMp1200(faker.number().numberBetween(1, 1000));
    }

    public MaintenanceParameterFactory withMp100(int number) {
        this.entity.setMp100(number);

        return this;
    }

    public MaintenanceParameterFactory withMp500(int number) {
        this.entity.setMp500(number);

        return this;
    }

    public MaintenanceParameterFactory withMp1200(int number) {
        this.entity.setMp1200(number);

        return this;
    }
}
