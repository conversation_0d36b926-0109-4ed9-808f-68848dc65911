package factories;

import java.util.concurrent.TimeUnit;
import models.Currency;
import models.Transaction;

public class TransactionFactory extends BaseModelFactory<Transaction> {

    @Override
    protected void initializeEntity() {
        this.entity = new Transaction();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.lorem().sentence());
        this.entity.setAddress(faker.address().fullAddress());
        this.entity.setEmail(faker.internet().emailAddress());
        this.entity.setUid(faker.internet().uuid());
        this.entity.setProviderTransactionId(faker.internet().uuid());
        this.entity.setReasonType(Transaction.ReasonType.CREDIT);
        this.entity.setAuthorizationresultmessage("Transacción realizada con éxito");
        this.entity.setAuthorizationresult(Transaction.AuthorizationResult.AUTHORIZED);
        this.entity.setErrorcode(Transaction.ErrorCode.OK);
        this.entity.setAuthorizationcode("0");
        this.entity.setAmount(faker.number().randomNumber(3, false));
        this.entity.setCreationDate(faker.date().past(1, TimeUnit.DAYS));
        this.entity.setCurrency(Currency.UYU);
    }

    public TransactionFactory confirmed() {
        this.entity.confirm();

        return this;
    }
}
