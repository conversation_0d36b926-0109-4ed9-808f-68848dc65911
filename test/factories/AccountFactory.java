package factories;

import java.util.concurrent.TimeUnit;
import models.Account;
import models.User;

public class AccountFactory extends BaseModelFactory<Account> {

    @Override
    protected void initializeEntity() {
        this.entity = new Account();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setCreationTimestamp(faker.date().past(100, 50, TimeUnit.DAYS));
        this.entity.setValidated(true);
        this.entity.setValidationDate(faker.date().past(50, 1, TimeUnit.DAYS));
    }

    public AccountFactory withOwner(User user) {
        this.entity.setOwner(user);

        return this;
    }
}
