package factories;

import java.util.concurrent.TimeUnit;
import models.SoapDispenser;

public class SoapDispenserFactory extends BaseModelFactory<SoapDispenser> {

    @Override
    protected void initializeEntity() {
        this.entity = new SoapDispenser();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setDescription(faker.lorem().sentence());
        this.entity.setModel(faker.lorem().word());
        this.entity.setUses(faker.number().randomDigit());
        this.entity.setReplenishDate(faker.date().past(1, TimeUnit.DAYS));
    }
}
