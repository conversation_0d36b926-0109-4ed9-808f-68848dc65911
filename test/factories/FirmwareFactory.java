package factories;

import models.Firmware;

public class FirmwareFactory extends BaseModelFactory<Firmware> {

    @Override
    protected void initializeEntity() {
        this.entity = new Firmware();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setMajorVersion(faker.number().numberBetween(1, 100));
        this.entity.setMinorVersion(faker.number().numberBetween(1, 100));
        this.entity.setBuildNumber(faker.number().numberBetween(1, 100));
        this.entity.setDescription(faker.lorem().sentence());
        this.entity.setUrl(faker.internet().url());
    }
}
