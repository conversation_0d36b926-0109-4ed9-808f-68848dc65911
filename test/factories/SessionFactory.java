package factories;

import java.util.concurrent.TimeUnit;
import models.Session;
import models.User;

public class SessionFactory extends BaseModelFactory<Session> {

    @Override
    protected void initializeEntity() {
        this.entity = new Session();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setToken(faker.random().hex());
        this.entity.setCreationTimestamp(faker.date().past(10, 1, TimeUnit.DAYS));
        this.entity.setValid(true);
        this.entity.setIp(faker.internet().ipV4Address());
        this.entity.setUserAgent(faker.internet().userAgentAny());
    }

    public SessionFactory withUser(User user) {
        this.entity.setUser(user);

        return this;
    }
}
