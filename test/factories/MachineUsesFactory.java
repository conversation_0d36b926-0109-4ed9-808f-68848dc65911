package factories;

import common.helpers.DateTimer;
import java.util.concurrent.TimeUnit;
import models.Card;
import models.Machine;
import models.MachineUse;
import models.MachineUseResult;

public class MachineUsesFactory extends BaseModelFactory<MachineUse> {

    @Override
    protected void initializeEntity() {
        this.entity = new MachineUse();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setTimestamp(faker.date().past(1, TimeUnit.DAYS));
        this.entity.setResult(MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE.getCodeString());
        this.entity.setWaterConsumption(faker.number().randomDouble(0, 0, 100));
        this.entity.setEnergyConsumption(faker.number().randomDouble(0, 0, 100));
        this.entity.setHeadline(faker.lorem().sentence());
        this.entity.setReason(faker.lorem().sentence());
    }

    public MachineUsesFactory withCard() {
        Card card = new CardFactory().build();
        this.entity.setCard(card);
        this.entity.setUid(card.getUuid());

        return this;
    }

    public MachineUsesFactory withMachine() {
        this.entity.setMachine(new MachineFactory().build());

        return this;
    }

    /**
     * Set the right timestamp value considering the required adjustment to
     * fit the GTM -3 time
     */
    public MachineUsesFactory withTimestampAgo(int timeAgo, TimeUnit timeUnit) {
        // (GTM -3 adjustment) + desired time ago
        int adjustment = (int) timeUnit.convert(3, TimeUnit.HOURS);
        this.entity.setTimestamp(DateTimer.ago(adjustment + timeAgo, timeUnit));

        return this;
    }

    @Override
    protected void beforeCreate() {
        if (this.entity.getCard() == null || this.entity.getMachine() == null) return;

        this.entity.setHeadline(
                (
                    this.entity.getMachine().getMachineType() == Machine.MachineType.WASHER
                        ? "Lavado"
                        : "Secado"
                ) +
                ", Máquina: " +
                this.entity.getMachine().getSerialNumber() +
                this.entity.getHeadline() +
                ", Tarjeta: " +
                this.entity.getCard().getUuid()
            );
    }
}
