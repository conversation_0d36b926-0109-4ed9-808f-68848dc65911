package factories;

public class FactoryException extends Exception {

    private FactoryException(String message) {
        super(message);
    }

    public static FactoryException whileDecorating() {
        return new FactoryException("Unable to decorate");
    }

    public static FactoryException whileBatchCreation() {
        return new FactoryException("Unable to execute batch action");
    }

    public static FactoryException whileSettingAttribute(String attribute) {
        return new FactoryException(
            "The setter method for the attribute \"" + attribute + "\"was not found"
        );
    }

    public FactoryException withCause(Exception e) {
        e.printStackTrace();

        return this;
    }
}
