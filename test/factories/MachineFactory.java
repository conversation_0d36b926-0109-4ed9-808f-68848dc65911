package factories;

import java.util.concurrent.TimeUnit;
import models.Firmware;
import models.Machine;
import models.Part;

public class MachineFactory extends BaseModelFactory<Machine> {

    @Override
    protected void initializeEntity() {
        this.entity = new Machine();
    }

    @Override
    protected void setDefaultProps() {
        this.entity.setName(faker.funnyName().name());
        this.entity.setModel(faker.lorem().word());
        this.entity.setDescription(faker.lorem().sentence());
        this.entity.setSerialNumber(faker.internet().uuid());
        this.entity.setState(Part.PartState.NEW);
        this.entity.setEnglishDescription(faker.lorem().sentence());
        this.entity.setUnitPrice(faker.number().randomDouble(4, 1, 9999));
        this.entity.setUyPrice(faker.number().randomDouble(4, 1, 9999));
        this.entity.setExpectedUses(faker.number().randomDigitNotZero());
        this.entity.setAverageUseTime(faker.number().randomDigitNotZero());
        this.entity.setReference(faker.lorem().sentence());
        this.entity.setSortIndex(faker.number().randomDigitNotZero());
        this.entity.setCapacity(faker.number().numberBetween(10, 99));
        this.entity.setMachineType(Machine.MachineType.WASHER);
        this.entity.setPublicIp(faker.internet().ipV4Address());
        this.entity.setPrivateIp(faker.internet().privateIpV4Address());
        this.entity.setPort(faker.number().numberBetween(1000, 9999) + "");
        this.entity.setLastAlive(faker.date().past(20, TimeUnit.MINUTES));
    }

    public MachineFactory washer() {
        this.entity.setMachineType(Machine.MachineType.WASHER);

        return this;
    }

    public MachineFactory dryer() {
        this.entity.setMachineType(Machine.MachineType.DRYER);

        return this;
    }

    public MachineFactory withRate() throws FactoryException {
        this.entity.setMachineRate(new RateFactory().withRateEvents().build());

        return this;
    }

    public MachineFactory withBuilding() {
        this.entity.setBuilding(new BuildingFactory().create());

        return this;
    }

    public MachineFactory withMachineModel() {
        if (this.entity.getMachineType() == Machine.MachineType.WASHER) {
            this.entity.setMachineModel(new MachineModelFactory().withWasherParameters().create());
        } else if (this.entity.getMachineType() == Machine.MachineType.DRYER) {
            this.entity.setMachineModel(new MachineModelFactory().withDryerParameters().create());
        }

        return this;
    }

    public MachineFactory withFirmware() {
        Firmware firmware = new FirmwareFactory().create();
        this.entity.setFirmware(firmware);
        this.entity.setFirmwareVersion(firmware.getFullVersion());

        return this;
    }

    public MachineFactory withoutKeepAlive() {
        this.entity.setLastAlive(null);

        return this;
    }

    public MachineFactory withUseTime(int minutes) {
        this.entity.setAverageUseTime(minutes);

        return this;
    }
}
