package factories;

import common.helpers.DateTimer;
import domains.billing.dto.Branch;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import models.Bill;
import models.BillItem;
import services.bill.CFE;
import services.bill.CFEItem;
import services.bill.CFERecipient;

/**
 * Factory class to create CFE objects for testing purposes.
 *
 * Internal values are fixed because it will be compared with the expected XML
 * files which are also fixed.
 */
public class CfeFactory {

    private final String NOT_USED_METHOD = "TODO";
    private final Integer NOT_USED_NUMBER = -100;

    private Bill.BillType billType;
    private boolean isCreditNote;
    private boolean isDebitNote;
    private boolean isReceipt;
    private boolean isReceiptCancelled;

    private final Date date = DateTimer.create("2025-04-05"); // 5th April 2025
    private Date from = null;
    private Date to = null;

    private String billSerial = null;
    private Integer billNumber = 0;
    private Integer paymentMethod;
    private String currency = "UYU";
    private Integer total = 100;
    private Integer tax = 22;
    private Integer billTotal = 122;
    private boolean isTaxFree = false;
    private String billReferenceSerial = null;
    private Integer billReferenceNumber = 0;
    private final Date billReferenceDate = DateTimer.create("2025-04-04"); // 4th April 2025
    private String billReason;

    List<CFEItem> items = new ArrayList<>();

    private Branch branch = null;

    private Integer recipientDocumentType = -1;
    private final String recipientDocument = "12345678";
    private final String recipientName = "Voonix";
    private final String recipientAddress = "Av. Libertador 1234";
    private String recipientCity = "Montevideo";
    private String recipientState = "Montevideo";
    private String recipientCountry = "Uruguay";

    // ------- modes ------- //

    public CfeFactory e_ticket() {
        this.billType = Bill.BillType.ETICK;
        this.cash();

        return this;
    }

    public CfeFactory e_factura() {
        this.billType = Bill.BillType.EFACT;
        this.recipientDocumentType = 2;
        this.credit();

        return this;
    }

    public CfeFactory export() {
        this.billType = Bill.BillType.EFACTEXPORTATION;

        this.credit();

        this.currency = "USD";

        this.recipientDocumentType = 4;
        this.recipientCity = "Asunción";
        this.recipientState = "Asunción";
        this.recipientCountry = "Paraguay";

        return this;
    }

    public CfeFactory creditNote() {
        this.isCreditNote = true;
        this.billReferenceSerial = "C";
        this.billReferenceNumber = 2;
        this.billReason =
            "Anulación de factura " + this.billReferenceSerial + "-" + this.billReferenceNumber;

        return this;
    }

    public CfeFactory debitNote() {
        this.isDebitNote = true;
        this.billReferenceSerial = "D";
        this.billReferenceNumber = 3;
        this.billReason =
            "Anulación de factura " + this.billReferenceSerial + "-" + this.billReferenceNumber;

        return this;
    }

    public CfeFactory booked() {
        this.billSerial = "B";
        this.billNumber = 10;

        return this;
    }

    public CfeFactory receipt() {
        this.isReceipt = true;
        this.billReferenceSerial = "R";
        this.billReferenceNumber = 1;

        return this;
    }

    public CfeFactory receiptCancelled() {
        this.isReceiptCancelled = true;
        this.billReferenceSerial = "RC";
        this.billReferenceNumber = 2;

        return this;
    }

    // ------- variants ------- //

    public CfeFactory periodical() {
        this.from = DateTimer.create("2025-03-01"); // 1st March 2025
        this.to = DateTimer.create("2025-03-31");
        // 31st March 2025

        return this;
    }

    public CfeFactory cash() {
        this.paymentMethod = 1;

        return this;
    }

    public CfeFactory credit() {
        this.paymentMethod = 2;

        return this;
    }

    public CfeFactory taxFree() {
        this.isTaxFree = true;

        return this;
    }

    public CfeFactory withBranch(Branch branch) {
        this.branch = branch;

        return this;
    }

    public CfeFactory withBranch() {
        this.branch = new Branch();
        this.branch.setNumber(11); // <> 5
        this.branch.setAddress("25 Blue Bill Park Center"); // <> "Avenida Brasil 3072 local 21"
        this.branch.setCity("Naples"); // <> "Montevideo"
        this.branch.setState("Florida"); // <> "Montevideo"

        return this;
    }

    public CfeFactory withDuplication() {
        this.items.add(createItem(1, 75));
        this.items.add(createItem(2, 25));

        this.total *= 2;
        this.tax *= 2;
        this.billTotal *= 2;

        return this;
    }

    public CfeFactory withoutReference() {
        this.billReferenceSerial = "";
        this.billReferenceNumber = 0;
        this.billReason = "";

        return this;
    }

    public CFE build() {
        this.items.add(createItem(1, 75));
        this.items.add(createItem(2, 25));

        return this.createBill();
    }

    private CFE createBill() {
        return new CFE() {
            @Override
            public String getSerie() {
                return CfeFactory.this.billSerial;
            }

            @Override
            public Integer getNumber() {
                return CfeFactory.this.billNumber;
            }

            @Override
            public String getTipoDoc() {
                return NOT_USED_METHOD;
            }

            @Override
            public double getMontoNetoIvaTasaBasica() {
                return CfeFactory.this.total;
            }

            @Override
            public double getMontoIvaTasaBasica() {
                return CfeFactory.this.tax;
            }

            @Override
            public double getMontoTotal() {
                return CfeFactory.this.billTotal;
            }

            @Override
            public int getCantidadLineas() {
                return CfeFactory.this.items.size();
            }

            @Override
            public List<? extends CFEItem> getItems() {
                return CfeFactory.this.items;
            }

            @Override
            public CFERecipient getRecipient() {
                return new CFERecipient() {
                    @Override
                    public int getTipoDoc() {
                        return CfeFactory.this.recipientDocumentType;
                    }

                    @Override
                    public String getBillingName() {
                        return CfeFactory.this.recipientName;
                    }

                    @Override
                    public String getDoc() {
                        return CfeFactory.this.recipientDocument;
                    }

                    @Override
                    public boolean hasRut() {
                        return false;
                    }

                    @Override
                    public String getDireccion() {
                        return CfeFactory.this.recipientAddress;
                    }

                    @Override
                    public String getCiudad() {
                        return CfeFactory.this.recipientCity;
                    }

                    @Override
                    public String getDepartamento() {
                        return CfeFactory.this.recipientState;
                    }

                    @Override
                    public String getPais() {
                        return CfeFactory.this.recipientCountry;
                    }

                    @Override
                    public String getCodPais() {
                        return CfeFactory.this.recipientCountry;
                    }
                };
            }

            @Override
            public boolean isCreditNote() {
                return CfeFactory.this.isCreditNote;
            }

            @Override
            public boolean isDebitNote() {
                return CfeFactory.this.isDebitNote;
            }

            @Override
            public boolean isCollectionReceipt() {
                return CfeFactory.this.isReceipt;
            }

            @Override
            public boolean isCancelCollectionReceipt() {
                return CfeFactory.this.isReceiptCancelled;
            }

            @Override
            public String getCreditNoteReason() {
                return CfeFactory.this.billReason;
            }

            @Override
            public CFE getBillReference() {
                if (
                    !CfeFactory.this.isDebitNote &&
                    !CfeFactory.this.isCreditNote &&
                    !CfeFactory.this.isReceipt &&
                    !CfeFactory.this.isReceiptCancelled
                ) {
                    return null;
                }

                return new CFE() {
                    @Override
                    public String getTipoDoc() {
                        return CfeFactory.this.recipientDocumentType.toString();
                    }

                    @Override
                    public String getSerie() {
                        return CfeFactory.this.billReferenceSerial;
                    }

                    @Override
                    public Integer getNumber() {
                        return CfeFactory.this.billReferenceNumber;
                    }

                    @Override
                    public Date getTimestamp() {
                        return CfeFactory.this.billReferenceDate;
                    }

                    // Below methods are not used.

                    @Override
                    public double getMontoNetoIvaTasaBasica() {
                        return NOT_USED_NUMBER;
                    }

                    @Override
                    public double getMontoIvaTasaBasica() {
                        return NOT_USED_NUMBER;
                    }

                    @Override
                    public double getMontoTotal() {
                        return NOT_USED_NUMBER;
                    }

                    @Override
                    public int getCantidadLineas() {
                        return NOT_USED_NUMBER;
                    }

                    @Override
                    public List<? extends CFEItem> getItems() {
                        return Collections.emptyList();
                    }

                    @Override
                    public CFERecipient getRecipient() {
                        return null;
                    }

                    @Override
                    public String getReference() {
                        return NOT_USED_METHOD;
                    }

                    @Override
                    public boolean isCreditNote() {
                        return false;
                    }

                    @Override
                    public boolean isDebitNote() {
                        return false;
                    }

                    @Override
                    public boolean isCollectionReceipt() {
                        return false;
                    }

                    @Override
                    public boolean isCancelCollectionReceipt() {
                        return false;
                    }

                    @Override
                    public String getCreditNoteReason() {
                        return CfeFactory.this.NOT_USED_METHOD;
                    }

                    @Override
                    public CFE getBillReference() {
                        return null;
                    }

                    @Override
                    public Date getPeriodoDesde() {
                        return null;
                    }

                    @Override
                    public Date getPeriodoHasta() {
                        return null;
                    }

                    @Override
                    public String getTipoMoneda() {
                        return NOT_USED_METHOD;
                    }

                    @Override
                    public String getFormaPago() {
                        return NOT_USED_METHOD;
                    }

                    @Override
                    public int getId() {
                        return NOT_USED_NUMBER;
                    }

                    @Override
                    public boolean isTaxFree() {
                        return false;
                    }

                    @Override
                    public Bill.BillType getBillType() {
                        return null;
                    }
                    // end Below methods are not used.
                };
            }

            @Override
            public Date getTimestamp() {
                return CfeFactory.this.date;
            }

            @Override
            public Date getPeriodoDesde() {
                return CfeFactory.this.from;
            }

            @Override
            public Date getPeriodoHasta() {
                return CfeFactory.this.to;
            }

            @Override
            public String getTipoMoneda() {
                return CfeFactory.this.currency;
            }

            @Override
            public String getFormaPago() {
                return CfeFactory.this.paymentMethod.toString();
            }

            @Override
            public boolean isTaxFree() {
                return CfeFactory.this.isTaxFree;
            }

            @Override
            public Bill.BillType getBillType() {
                return CfeFactory.this.billType;
            }

            @Override
            public Branch getBranch() {
                return CfeFactory.this.branch;
            }

            @Override
            public int getId() {
                return CfeFactory.this.NOT_USED_NUMBER;
            }

            @Override
            public String getReference() {
                return CfeFactory.this.NOT_USED_METHOD;
            }
        };
    }

    private CFEItem createItem(int index, int price) {
        return new CFEItem() {
            @Override
            public String getNomItem() {
                return "item nro " + index;
            }

            @Override
            public int getCantidad() {
                return 1;
            }

            @Override
            public String getUnidadMedida() {
                return BillItem.MEASURE_UNIT_BALANCE;
            }

            @Override
            public double getPrecioUnitario() {
                return price / 1.22;
            }

            @Override
            public int getIndDet() {
                return CfeFactory.this.isTaxFree ? 1 : 3;
            }

            @Override
            public int getId() {
                return CfeFactory.this.NOT_USED_NUMBER;
            }

            @Override
            public String getItemType() {
                return CfeFactory.this.NOT_USED_METHOD;
            }
        };
    }
}
