package common;

import static play.test.Helpers.contentAsString;
import static play.test.Helpers.route;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Iterators;
import common.helpers.QueryParam;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URLEncodedUtils;
import play.mvc.Call;
import play.mvc.Http;
import play.mvc.Result;

public abstract class RequestBaseTest extends BaseTest {

    protected Result response;

    protected Result request(
        play.mvc.Call action,
        String token,
        JsonNode body,
        QueryParam... params
    ) {
        this.tx.commit();

        String queryString = StringUtils.EMPTY;
        if (params != null && params.length > 0) {
            queryString += "?";
            queryString += URLEncodedUtils.format(Arrays.asList(params), StandardCharsets.UTF_8);
        }

        Http.RequestBuilder request = new Http.RequestBuilder()
            .method(action.method())
            .uri(action.path() + queryString);
        if (token != null) {
            request.header(security.v1.Secured.LM_AUTH_HEADER, token);
        }
        if (body != null) {
            request.bodyJson(body);
        }

        this.response = route(this.app, request);

        return this.response;
    }

    protected Result request(play.mvc.Call action) {
        return request(action, null, null, new QueryParam[] {});
    }

    protected Result request(play.mvc.Call action, String token) {
        return request(action, token, null, new QueryParam[] {});
    }

    protected Result request(play.mvc.Call action, String token, QueryParam... params) {
        return request(action, token, null, params);
    }

    protected Result request(play.mvc.Call action, String token, JsonNode body) {
        return request(action, token, body, new QueryParam[] {});
    }

    protected Result request(play.mvc.Call action, QueryParam... params) {
        return request(action, null, null, params);
    }

    protected Call generateCall(final String method, final String path) {
        return new Call() {
            @Override
            public String url() {
                return path;
            }

            @Override
            public String method() {
                return method;
            }

            @Override
            public String fragment() {
                return org.apache.commons.lang.StringUtils.EMPTY;
            }
        };
    }

    public int status() {
        return this.response.status();
    }

    public JsonNode json() {
        if (this.response.contentType().equals("application/json")) {
            try {
                return new ObjectMapper().readTree(this.text());
            } catch (IOException e) {
                throw new IllegalArgumentException("Cannot parse json response", e);
            }
        } else {
            throw new IllegalArgumentException(
                "Invalid content type, expected application/json but got " +
                this.response.contentType()
            );
        }
    }

    public int jsonSize() {
        return Iterators.size(json().elements());
    }

    public int jsonSize(String key) {
        return Iterators.size(json().get(key).elements());
    }

    public Stream<JsonNode> jsonStream() {
        return StreamSupport.stream(json().spliterator(), false);
    }

    public Stream<JsonNode> jsonStream(String key) {
        return StreamSupport.stream(json().get(key).spliterator(), false);
    }

    public String text() {
        return contentAsString(this.response);
    }
}
