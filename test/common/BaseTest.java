package common;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.github.javafaker.Faker;
import com.play4jpa.jpa.models.Model;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import java.io.File;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import org.apache.commons.collections.CollectionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.ComparisonFailure;
import org.junit.Rule;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;
import play.Application;
import play.Configuration;
import play.Logger;
import play.api.inject.Binding;
import play.db.jpa.JPA;
import play.inject.guice.GuiceApplicationBuilder;
import play.test.Helpers;

public abstract class BaseTest {

    /**
     * lib to generate testing closureData.
     * http://dius.github.io/java-faker/apidocs/index.html
     * https://github.com/faker-ruby/faker/tree/master/doc/default
     */
    protected static final Faker faker = new Faker();

    /**
     * EntityManager to use (created for each test case).
     */
    protected EntityManager em = null;

    /**
     * Transaction to use (created for each test case).
     */
    protected EntityTransaction tx = null;

    /**
     * Fake application
     */
    protected Application app;

    @Rule
    public TestWatcher testWatcher = new TestWatcher() {
        @Override
        protected void starting(final Description description) {
            System.out.printf(
                "[info] Running: %s.%s %n",
                description.getTestClass().getName(),
                description.getMethodName()
            );
        }

        @Override
        protected void failed(Throwable e, Description description) {
            super.failed(e, description);

            StackTraceElement knownLocation = Arrays
                .stream(e.getStackTrace())
                .filter(x ->
                    x.getClassName().equals(description.getTestClass().getName()) &&
                    x.getMethodName().equals(description.getMethodName())
                )
                .findFirst()
                .get();
            System.out.printf("[info] Failed: %s - %s", knownLocation.toString(), e.getMessage());

            if (e instanceof ComparisonFailure) {
                ComparisonFailure failure = (ComparisonFailure) e;
                String expected = failure.getExpected();
                String actual = failure.getActual();
                System.out.printf(" - expected: %s <> actual: %s", expected, actual);
            }

            System.out.println();
        }
    };

    @Before
    public void beforeEachTest() {
        startFakeApp();
        openTransaction();
    }

    @After
    public void afterEachTest() {
        closeTransaction();
        stopFakeApp();
    }

    protected void startFakeApp() {
        Config additionalConfig = ConfigFactory
            .parseFile(new File("conf/testing.application.conf"))
            .resolve();
        Map<String, Object> configuration = new Configuration(additionalConfig).asMap();

        // in memory db
        configuration.put("db.default.driver", "org.h2.Driver");
        configuration.put("db.default.url", "jdbc:h2:mem:play;MODE=MYSQL");
        configuration.put("db.default.username", "sa");
        configuration.put("db.default.password", "");
        configuration.put("jpa.default", "test");

        this.app =
            new GuiceApplicationBuilder()
                .configure(configuration)
                .overrides(this.bindingInstances())
                .build();

        Helpers.start(this.app);
    }

    /**
     * Creates a generic binding that overrides the default implementation of
     * any class.
     *
     * <p> This method is useful in tests where you need to inject a mock or a specific instance
     * for a class instead of using the default implementation.</p>
     *
     * <p><strong>Example Usage:</strong></p>
     * <pre>{@code
     *     @Override
     *     public Binding<?> bindingInstance() {
     *         this.mockService = PowerMock.createMock(GoogleReCaptchaService.class);
     *         return bind(GoogleReCaptchaService.class).toInstance(this.mockService);
     *     }
     * }</pre>
     */
    public Binding<?>[] bindingInstances() {
        return new Binding[] {};
    }

    protected void stopFakeApp() {
        if (this.app != null) {
            Helpers.stop(this.app);
        }
    }

    /*
     * ===== Database Operations =====
     */

    /**
     * Get a new EntityManager and open a transaction.
     */
    protected void openTransaction() {
        this.em = JPA.em("default");
        if (this.em == null) {
            Logger.error("Could not get JPA EntityManager");
        } else {
            Logger.debug("Found entity manager: {}", this.em);
        }

        JPA.bindForSync(this.em);
        this.tx = this.em.getTransaction();
        this.tx.begin();
        Logger.debug("Opened transaction");
    }

    /**
     * Commit or rollback transaction if active and close EntityManager.
     */
    protected void closeTransaction() {
        if (this.tx != null) {
            if (this.tx.isActive()) {
                if (this.tx.getRollbackOnly()) {
                    this.tx.rollback();
                } else {
                    this.tx.commit();
                }
            }
        }

        JPA.bindForSync(null);
        if (this.em != null) {
            this.em.close();
        }
        Logger.debug("Closed transaction");
    }

    /**
     * Reloads entity that could have been modified outside the current scope.
     * Usually applies to entities modified on requests and needs to be
     * reloaded to check if the changes were made.
     */
    protected <T extends Model<? super T>> T reload(T model) {
        this.em.refresh(model);

        return model;
    }

    /*
     * ===== Custom Assertions =====
     */

    /**
     * Assert collections have the same elements ignoring elements order
     */
    public static void assertCollection(final Collection expected, final Collection actual) {
        assertTrue(
            "Collections are not equals.",
            CollectionUtils.isEqualCollection(expected, actual)
        );
    }

    /**
     * Assert strings are equals considering an allowed length difference
     */
    public static void assertWithDeviation(
        final String expected,
        final String actual,
        int deviation
    ) {
        assertTrue(
            "Strings are different beyond the deviation.",
            Math.abs(expected.length() - actual.length()) <= deviation
        );

        int minLength = Math.min(expected.length(), actual.length());
        assertEquals(expected.substring(0, minLength), actual.substring(0, minLength));
    }

    /**
     * Assert long values considering an allowed difference
     */
    public static void assertEqualsWithinDeviation(
        final long expected,
        final long actual,
        long deviation
    ) {
        assertTrue(
            "Values are not within the acceptable delta",
            Math.abs(expected - actual) <= deviation
        );
    }
}
