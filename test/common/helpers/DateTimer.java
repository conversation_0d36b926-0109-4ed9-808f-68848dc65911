package common.helpers;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import org.joda.time.DateTime;

public abstract class DateTimer {

    /**
     * Return the current date time minus the amount of time indicated.
     * For example:
     *   if now is 2023-03-29 10:00
     *   DateTimer.ago(1, TimeUnit.HOURS)  ->  2023-03-29 09:00
     *   DateTimer.ago(1, TimeUnit.DAYS)   ->  2023-03-28 10:00
     */
    public static Date ago(int duration, TimeUnit timeUnit) {
        long milliseconds = TimeUnit.MILLISECONDS.convert(duration, timeUnit);
        return DateTime.now().minus(milliseconds).toDate();
    }

    /**
     * Return the current date time plus the amount of time indicated.
     * For example:
     *   if now is 2023-03-29 10:00
     *   DateTimer.advance(1, TimeUnit.HOURS)  ->  2023-03-29 11:00
     *   DateTimer.advance(1, TimeUnit.DAYS)   ->  2023-03-30 10:00
     */
    public static Date advance(int duration, TimeUnit timeUnit) {
        long milliseconds = TimeUnit.MILLISECONDS.convert(duration, timeUnit);
        return DateTime.now().plus(milliseconds).toDate();
    }

    /**
     * Create a date given the format yyyy-MM-dd
     */
    public static Date create(String dateString) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            return formatter.parse(dateString);
        } catch (ParseException e) {
            System.out.println("Error parsing date: " + e.getMessage());
            return null;
        }
    }
}
