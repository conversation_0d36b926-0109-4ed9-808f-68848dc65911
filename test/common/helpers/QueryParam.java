package common.helpers;

import org.apache.http.NameValuePair;

public class QueryParam implements NameValuePair {

    private final String name;
    private final String value;

    public QueryParam(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public QueryParam(String name, Object value) {
        this.name = name;
        this.value = value.toString();
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.name + '=' + this.value;
    }

    public static QueryParam[] concat(QueryParam item, QueryParam[] list) {
        int paramsLength = list != null ? list.length : 0;
        QueryParam[] params = new QueryParam[paramsLength + 1];
        int i = 0;
        for (; i < paramsLength; i++) {
            params[i] = list[i];
        }
        params[i] = item;

        return params;
    }
}
