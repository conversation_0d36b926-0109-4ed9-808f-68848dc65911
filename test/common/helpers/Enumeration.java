package common.helpers;

import java.util.Random;

public abstract class Enumeration {

    /**
     * Generates and returns a random enum value from the provided enum type.
     */
    public static <T extends Enum<T>> T sampleOf(Class<T> clazz) {
        Random random = new Random();
        T[] values = clazz.getEnumConstants();
        int randomIndex = random.nextInt(values.length);
        return values[randomIndex];
    }

    /**
     * Returns a random value from the provided array of values.
     */
    public static <T> T sampleOf(T... values) {
        Random random = new Random();
        int randomIndex = random.nextInt(values.length);
        return values[randomIndex];
    }
}
