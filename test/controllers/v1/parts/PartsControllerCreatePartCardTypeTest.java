package controllers.v1.parts;

import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import common.helpers.Formatter;
import factories.CardFactory;
import factories.FactoryException;
import factories.UserFactory;
import global.APIException;
import models.Card;
import models.Role;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class PartsControllerCreatePartCardTypeTest extends common.RequestBaseTest {

    private void createCard(String token, JsonNode card) {
        Call call = generateCall("POST", "/api/v1/cards");

        request(call, token, card);
    }

    @Test
    public void testCreationExistingCard() throws FactoryException {
        String uuid = faker.internet().uuid();
        Card existingCard = new CardFactory().prepaid().with("uuid", uuid).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Card card = new CardFactory().prepaid().with("uuid", uuid).build();

        createCard(token, jsonize(card));

        assertEquals(Http.Status.BAD_REQUEST, status());
        assertEquals(
            APIException.APIErrors.CARD_UUID_ALREADY_EXISTS.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.CARD_UUID_ALREADY_EXISTS.message(),
            json().get("result_message").asText()
        );
    }

    @Test
    public void testCreationPostpaidCard() throws FactoryException {
        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Card card = new CardFactory().postpaid().build();

        createCard(token, jsonize(card));

        assertEquals(Http.Status.CREATED, status());
        assertEquals(1, Card.count());

        Card createdCard = Card.findAll().get(0);
        assertEquals(card.getUuid(), createdCard.getUuid());
        assertEquals(
            Card.ContractType.POSTPAID.toString(),
            createdCard.getContractType().toString()
        );
    }

    @Test
    public void testCreationPrepaidCard() throws FactoryException {
        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Card card = new CardFactory().prepaid().build();

        createCard(token, jsonize(card));

        assertEquals(Http.Status.CREATED, status());
        assertEquals(1, Card.count());

        Card createdCard = Card.findAll().get(0);
        assertEquals(card.getUuid(), createdCard.getUuid());
        assertEquals(
            Card.ContractType.PREPAID.toString(),
            createdCard.getContractType().toString()
        );
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        String token = new UserFactory().withAuthentication().authenticate();

        createCard(token, null);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() {
        createCard(null, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(Card card) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("name", card.getName())
            .put("model", card.getModel())
            .put("description", card.getDescription())
            .put("uuid", card.getUuid())
            .put("serial_number", card.getSerialNumber())
            .put("master", card.isMaster())
            .put("contract_type", card.getContractType().toString())
            .put("discount", card.getDiscount())
            .put(
                "end_time_of_use",
                Formatter.format(card.getStartTimeOfUse(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            )
            .put(
                "start_time_of_use",
                Formatter.format(card.getEndTimeOfUse(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            );
    }
}
