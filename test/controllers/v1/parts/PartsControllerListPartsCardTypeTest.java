package controllers.v1.parts;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.fasterxml.jackson.databind.JsonNode;
import common.helpers.Formatter;
import common.helpers.QueryParam;
import factories.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import models.*;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class PartsControllerListPartsCardTypeTest extends common.RequestBaseTest {

    private void getCards(String token, QueryParam... extraParams) {
        Call call = generateCall("GET", "/api/v1/parts");

        QueryParam type = new QueryParam("type", "CARD");

        request(call, token, QueryParam.concat(type, extraParams));
    }

    @Test
    public void testSingleSearch() throws FactoryException {
        Building building = new BuildingFactory().withRate().withUnit().create();

        Card card = new CardFactory()
            .prepaid()
            .withEvent()
            .with("unit", building.getUnits().get(0))
            .create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getCards(token, new QueryParam("cardSearch", card.getUuid()));

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(1, jsonSize("cards"));

        JsonNode jsonCard = json().get("cards").get(0);
        assertEquals(card.getUuid(), jsonCard.get("uuid").asText());
        assertEquals(card.isMaster(), jsonCard.get("master").asBoolean());
        assertEquals(card.getContractType().toString(), jsonCard.get("contract_type").asText());
        assertEquals(card.getState().toString(), jsonCard.get("state").asText());
        assertEquals(card.getBalance(), jsonCard.get("balance").asDouble(), 0.001);
        assertEquals(
            Formatter.format(card.getStartTimeOfUse(), "yyyy-MM-dd HH:mm:ss"),
            jsonCard.get("start_time_of_use").asText()
        );
        assertEquals(
            Formatter.format(card.getEndTimeOfUse(), "yyyy-MM-dd HH:mm:ss"),
            jsonCard.get("end_time_of_use").asText()
        );
        assertEquals(card.getDiscount(), jsonCard.get("discount").asDouble(), 0.001);
        assertEquals(card.getSubState().toString(), jsonCard.get("substate").asText());
        assertEquals(card.getSubState().toString(), jsonCard.get("substate").asText());
        Unit unit = card.getUnit();
        assertEquals(unit.getId(), jsonCard.get("unit_id").asInt());
        assertEquals(unit.getNumber(), jsonCard.get("unit_number").asText());
        assertEquals(unit.getTower(), jsonCard.get("unit_tower").asText());
        assertEquals(card.getStartTimeOfUse().getTime(), jsonCard.get("start_time").asLong());
        assertEquals(card.getEndTimeOfUse().getTime(), jsonCard.get("end_time").asLong());
        CardEvent event = card.getCardEvents().get(0);
        JsonNode eventJson = jsonCard.get("card_events").get(0);
        assertEquals(
            Formatter.format(event.getTimestamp(), "yyyy-MM-dd HH:mm:ss"),
            eventJson.get("timestamp").asText()
        );
        assertEquals(event.getHeadline(), eventJson.get("headline").asText());
        assertEquals(event.getUid(), eventJson.get("uid").asText());
        assertEquals(event.getEventType().toString(), eventJson.get("event_type").asText());
        assertEquals(building.getName(), jsonCard.get("building").asText());
        Rate rate = reload(building.getRate());
        assertEquals(rate.getPriceCustomer(), jsonCard.get("priceCustomer").asDouble(), 0.001);
        assertEquals(rate.getPriceCompany(), jsonCard.get("priceCompany").asDouble(), 0.001);
        assertEquals(building.getId(), jsonCard.get("buildingId").asInt());
    }

    @Test
    public void testMultipleSearch() throws FactoryException {
        Building building = new BuildingFactory().withRate().withUnit().create();
        String commonUuid = "common-uuid";
        Unit unit = building.getUnits().get(0);
        List<Card> cardsFound = new CardFactory()
            .prepaid()
            .with("unit", unit)
            .with("uuid", () -> faker.internet().uuid() + commonUuid)
            .create(4);

        Building buildingExtra = new BuildingFactory().withRate().withUnit().create();
        List<Card> cardsExtra = new CardFactory()
            .prepaid()
            .with("unit", buildingExtra.getUnits().get(0))
            .create(7);

        Card master = new CardFactory().prepaid().master().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getCards(
            token,
            new QueryParam("cardSearch", commonUuid),
            new QueryParam("building", building.getId()),
            new QueryParam("unit", unit.getId()),
            new QueryParam("includeMaster", "0")
        );

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());
        assertEquals(4, jsonSize("cards"));

        assertEquals(4 + 7 + 1, Card.count());

        assertEquals(
            cardsFound.stream().map(Part::getId).collect(Collectors.toList()),
            jsonStream("cards").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testMultipleSearchIncludingMasterCards() throws FactoryException {
        Building building = new BuildingFactory().withRate().withUnit().create();
        String commonUuid = "common-uuid";
        Unit unit = building.getUnits().get(0);
        List<Card> cardsFound = new CardFactory()
            .prepaid()
            .with("unit", unit)
            .with("uuid", () -> faker.internet().uuid() + commonUuid)
            .create(4);

        Building buildingExtra = new BuildingFactory().withRate().withUnit().create();
        List<Card> cardsExtra = new CardFactory()
            .prepaid()
            .with("unit", buildingExtra.getUnits().get(0))
            .create(7);

        Card master = new CardFactory().prepaid().master().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getCards(
            token,
            new QueryParam("cardSearch", commonUuid),
            new QueryParam("building", building.getId()),
            new QueryParam("unit", unit.getId()),
            new QueryParam("includeMaster", "1")
        );

        assertEquals(Http.Status.OK, status());
        assertNotNull(json());
        assertEquals(5, jsonSize("cards"));

        assertEquals(4 + 7 + 1, Card.count());

        List<Integer> foundIds = cardsFound.stream().map(Part::getId).collect(Collectors.toList());
        foundIds.add(master.getId());
        assertEquals(
            foundIds,
            jsonStream("cards").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testSingleSearchUnassociated() throws FactoryException {
        List<Card> cards = new CardFactory()
            .prepaid()
            .with("uuid", () -> faker.internet().uuid())
            .create(4);
        Card card = cards.get(faker.number().numberBetween(0, cards.size()));

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getCards(token, new QueryParam("cardSearch", card.getUuid()));

        assertEquals(Http.Status.OK, status());

        assertEquals(4, Card.count());

        assertNotNull(json());
        assertEquals(1, jsonSize("cards"));

        List<Integer> expectedIds = new ArrayList<Integer>();
        expectedIds.add(card.getId());
        assertEquals(
            expectedIds,
            jsonStream("cards").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        String token = new UserFactory().withAuthentication().authenticate();

        getCards(token);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() {
        getCards(null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
