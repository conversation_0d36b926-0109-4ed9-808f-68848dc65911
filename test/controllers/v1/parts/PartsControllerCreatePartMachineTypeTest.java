package controllers.v1.parts;

import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import factories.FactoryException;
import factories.MachineFactory;
import factories.UserFactory;
import models.Machine;
import models.Role;
import org.json.JSONException;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class PartsControllerCreatePartMachineTypeTest extends common.RequestBaseTest {

    private void createMachine(String token, JsonNode machine) throws FactoryException {
        Call call = generateCall("POST", "/api/v1/machines");

        request(call, token, machine);
    }

    @Test
    public void testCreation() throws FactoryException, JSONException {
        Machine machine = new MachineFactory()
            .washer()
            .withBuilding()
            .withFirmware()
            .withMachineModel()
            .withRate()
            .build();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        createMachine(token, jsonize(machine));

        assertEquals(Http.Status.CREATED, status());
        assertEquals(1, Machine.count());
        assertEquals(Machine.findAll().get(0).getSerialNumber(), machine.getSerialNumber());
    }

    @Test
    public void testCreationWithoutData() throws FactoryException, JSONException {
        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        createMachine(token, null);

        assertEquals(Http.Status.BAD_REQUEST, status());
        assertEquals(0, Machine.count());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        String token = new UserFactory().withAuthentication().authenticate();

        createMachine(token, null);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        createMachine(null, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(Machine machine) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("name", machine.getName())
            .put("model", machine.getModel())
            .put("description", machine.getDescription())
            .put("serial_number", machine.getSerialNumber())
            .put("english_description", machine.getEnglishDescription())
            .put("unit_price", machine.getUnitPrice())
            .put("uy_price", machine.getUyPrice())
            .put("average_use_time", machine.getAverageUseTime())
            .put("machine_type", machine.getMachineType().toString())
            .put("reference", machine.getReference())
            .put("sortIndex", machine.getSortIndex())
            .put("capacity", machine.getCapacity())
            .put("machine_model_id", machine.getMachineModel().getId())
            .put("expected_uses", machine.getExpectedUses())
            .put("specialRateId", machine.getMachineRate().getId());
    }
}
