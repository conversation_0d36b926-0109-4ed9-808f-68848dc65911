package controllers.v1.parts;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.fasterxml.jackson.databind.JsonNode;
import factories.FactoryException;
import factories.MachineFactory;
import factories.UserFactory;
import java.util.List;
import java.util.stream.Collectors;
import models.*;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class PartsControllerListPartsMachineTypeTest extends common.RequestBaseTest {

    private void getMachines(String token, String machineSearch) throws FactoryException {
        Call call = generateCall(
            "GET",
            "/api/v1/parts?type=MACHINE&machineSearch=" + machineSearch
        );

        request(call, token);
    }

    @Test
    public void testSingleSearch() throws FactoryException {
        Machine machine = new MachineFactory()
            .washer()
            .withBuilding()
            .withFirmware()
            .withMachineModel()
            .withRate()
            .create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getMachines(token, machine.getSerialNumber());

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(1, jsonSize("machines"));

        JsonNode jsonMachine = json().get("machines").get(0);
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("machine_type").asText());
        assertEquals(machine.getCapacity(), jsonMachine.get("capacity").asInt());
        assertEquals(machine.getSortIndex(), jsonMachine.get("sort_index").asInt());
        assertEquals(machine.getReference(), jsonMachine.get("reference").asText());
        assertEquals(
            machine.getEnglishDescription(),
            jsonMachine.get("english_description").asText()
        );
        assertEquals(machine.getUnitPrice(), jsonMachine.get("unit_price").asDouble(), 0.001);
        assertEquals(machine.getUyPrice(), jsonMachine.get("uy_price").asDouble(), 0.001);
        assertEquals(machine.getAverageUseTime(), jsonMachine.get("average_use_time").asInt());
        assertEquals(machine.getExpectedUses(), jsonMachine.get("expected_uses").asInt());
        assertEquals(machine.getCurrentUses(), jsonMachine.get("current_uses").asInt());
        assertEquals(machine.getPrivateIp(), jsonMachine.get("private_ip").asText());
        assertEquals(machine.getPublicIp(), jsonMachine.get("public_ip").asText());
        assertEquals(machine.getPort(), jsonMachine.get("port").asText());
        assertEquals(machine.getLastAlive().getTime(), jsonMachine.get("last_keep_alive").asLong());
        assertEquals(machine.getLastAlive().getTime(), jsonMachine.get("lastKeepAlive").asLong());

        assertEquals(machine.getFirmwareVersion(), jsonMachine.get("firmware_version").asText());

        Rate rate = reload(machine.getMachineRate());
        assertNotNull(rate);
        assertEquals(rate.getId(), jsonMachine.get("rateId").asInt());
        assertEquals(
            rate.getPriceCompany(),
            (Double) jsonMachine.get("priceMachine").asDouble(),
            0.001
        );

        MachineModel model = machine.getMachineModel();
        assertEquals(model.getId(), jsonMachine.get("machine_model_id").asInt());
        assertEquals(model.getId(), jsonMachine.get("machine_model").get("id").asInt());
        assertEquals(model.getName(), jsonMachine.get("machine_model").get("name").asText());
        MaintenanceParameter parameters = model.getParameter();
        assertEquals(parameters.getMp100(), jsonMachine.get("machine_model").get("mp100").asInt());
        assertEquals(parameters.getMp500(), jsonMachine.get("machine_model").get("mp500").asInt());
        assertEquals(
            parameters.getMp1200(),
            jsonMachine.get("machine_model").get("mp1200").asInt()
        );

        Building building = machine.getBuilding();
        assertEquals(building.getId(), jsonMachine.get("building").get("id").asInt());
        assertEquals(building.getName(), jsonMachine.get("building").get("name").asText());
    }

    @Test
    public void testMultipleSearchAsMASTER() throws FactoryException {
        String commonSerial = "this-is-a-common-serial";
        List<Machine> foundMachines = new MachineFactory()
            .with("serialNumber", () -> commonSerial + faker.internet().uuid())
            .create(3);

        List<Machine> notFoundMachines = new MachineFactory().create(2);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        getMachines(token, commonSerial);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(3, jsonSize("machines"));
        assertEquals(5, Machine.count());
        assertEquals(
            foundMachines.stream().map(Part::getId).collect(Collectors.toList()),
            jsonStream("machines").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testMultipleSearchAsASSISTANT() throws FactoryException {
        String commonSerial = "this-is-a-common-serial";
        List<Machine> foundMachines = new MachineFactory()
            .with("serialNumber", () -> commonSerial + faker.internet().uuid())
            .create(3);

        List<Machine> notFoundMachines = new MachineFactory().create(2);

        String token = new UserFactory()
            .with("role", Role.ASSISTANT)
            .withAuthentication()
            .authenticate();

        getMachines(token, commonSerial);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(3, jsonSize("machines"));
        assertEquals(5, Machine.count());
        assertEquals(
            foundMachines.stream().map(Part::getId).collect(Collectors.toList()),
            jsonStream("machines").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        String token = new UserFactory().withAuthentication().authenticate();

        getMachines(token, "fake-text");

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        getMachines(null, "fake-text");

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
