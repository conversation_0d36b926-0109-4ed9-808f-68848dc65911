package controllers.v1.buildings.units.cards;

import static org.junit.Assert.assertEquals;

import factories.*;
import global.APIException;
import java.util.List;
import models.*;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;
import queries.cards.CardEventQuery;

public class BuildingControllerCardActionTest extends common.RequestBaseTest {

    enum Action {
        activate,
        deactivate,
        lost,
        damaged,
        fake,
    }

    private void cardAction(String token, Building building, Unit unit, Card card, Action action) {
        Call call = generateCall(
            // TODO: should be an PUT method
            "GET",
            "/api/v1/buildings/" +
            building.getId() +
            "/units/" +
            unit.getId() +
            "/cards/" +
            card.getUuid() +
            "/" +
            action.toString()
        );

        request(call, token);
    }

    @Test
    public void testDeactivatedAction() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        // TODO: should be OK
        assertEquals(Http.Status.CREATED, status());

        reload(card);
        assertEquals(Part.PartState.INACTIVE, card.getState());
        assertEquals(Part.PartState.SUSPENDED, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_BLOCKED_NO_PAYMENT);
    }

    @Test
    public void testLostAction() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.lost);

        // TODO: should be OK
        assertEquals(Http.Status.CREATED, status());

        reload(card);
        assertEquals(Part.PartState.INACTIVE, card.getState());
        assertEquals(Part.PartState.LOST, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_BLOCKED_LOST);
    }

    @Test
    public void testDamagedAction() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.damaged);

        // TODO: should be OK
        assertEquals(Http.Status.CREATED, status());

        reload(card);
        assertEquals(Part.PartState.INACTIVE, card.getState());
        assertEquals(Part.PartState.DAMAGED, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_BLOCKED_DAMAGED);
    }

    @Test
    public void testActivatedAction() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().deactivated().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.activate);

        // TODO: should be OK
        assertEquals(Http.Status.CREATED, status());

        reload(card);
        assertEquals(Part.PartState.ACTIVE, card.getState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_UNBLOCKED);
    }

    @Test
    public void testActionNonExistingBuilding() throws FactoryException {
        Building building = new BuildingFactory().withRate().build();
        Unit unit = new UnitFactory().create();
        Card card = new CardFactory().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        reload(card);
        assertEquals(Part.PartState.ACTIVE, card.getState());
        assertEquals(Part.PartState.ACTIVE, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testActionNonExistingUnit() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).build();
        Card card = new CardFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.UNIT_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.UNIT_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        reload(card);
        assertEquals(Part.PartState.ACTIVE, card.getState());
        assertEquals(Part.PartState.ACTIVE, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testActionNonExistingCard() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).build();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.CARD_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.CARD_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        assertEquals(0, Card.findAll().size());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testActionWhenUnitBelongsAnotherBuilding() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Building building2 = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building2).create();
        Card card = new CardFactory().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.UNIT_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.UNIT_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        reload(card);
        assertEquals(Part.PartState.ACTIVE, card.getState());
        assertEquals(Part.PartState.ACTIVE, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testUnsupportedAction() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().deactivated().with("unit", unit).create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        cardAction(token, building, unit, card, Action.fake);

        // TODO: should be OK
        assertEquals(Http.Status.CREATED, status());

        reload(card);
        assertEquals(Part.PartState.INACTIVE, card.getState());
        assertEquals(Part.PartState.SUSPENDED, card.getSubState());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().deactivated().with("unit", unit).create();
        String token = new UserFactory().withAuthentication().authenticate();

        cardAction(token, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().deactivated().with("unit", unit).create();

        cardAction(null, building, unit, card, Action.deactivate);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
