package controllers.v1.buildings.units.cards;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import common.helpers.QueryParam;
import factories.*;
import global.APIException;
import java.util.List;
import models.*;
import org.junit.Test;
import play.i18n.Messages;
import play.mvc.Call;
import play.mvc.Http;
import queries.cards.CardEventQuery;

public class BuildingControllerAssignCardToUnitTest extends common.RequestBaseTest {

    private void assignCard(
        String token,
        Building building,
        Unit unit,
        Card card,
        boolean billable
    ) {
        Call call = generateCall(
            "PUT",
            "/api/v1/buildings/" +
            building.getId() +
            "/units/" +
            unit.getId() +
            "/cards/" +
            card.getUuid()
        );

        QueryParam param = new QueryParam("billable", billable);

        request(call, token, param);
    }

    @Test
    public void testAssignPrepaidCardBillable() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().prepaid().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.OK, status());

        reload(unit);
        assertEquals(unit.getAssignedCards().get(0).getId(), card.getId());

        reload(card);
        assertEquals(card.getUnit().getId(), unit.getId());
        // QUESTION: weird, it does not take into account the previous balance
        assertEquals(
            card.getBalance(),
            reload(building.getRate()).getPriceCardReplacement() * -1,
            0.001
        );

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_ASSIGNED_NON_BILLABLE);
    }

    @Test
    public void testAssignPrepaidCardNonBillable() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().prepaid().create();
        double cardBalance = card.getBalance();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, false);

        assertEquals(Http.Status.OK, status());

        reload(unit);
        assertEquals(unit.getAssignedCards().get(0).getId(), card.getId());

        reload(card);
        assertEquals(card.getUnit().getId(), unit.getId());
        assertEquals(card.getBalance(), cardBalance, 0.001);

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_ASSIGNED_NON_BILLABLE);
    }

    @Test
    public void testAssignPostpaidCardBillable() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().create();
        double cardBalance = card.getBalance();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.OK, status());

        reload(unit);
        assertEquals(unit.getAssignedCards().get(0).getId(), card.getId());

        reload(card);
        assertEquals(card.getUnit().getId(), unit.getId());
        assertEquals(card.getBalance(), cardBalance, 0.001);

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_ASSIGNED_BILLABLE);
    }

    @Test
    public void testAssignPostpaidCardNonBillable() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().create();
        double cardBalance = card.getBalance();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, false);

        assertEquals(Http.Status.OK, status());

        reload(unit);
        assertEquals(unit.getAssignedCards().get(0).getId(), card.getId());

        reload(card);
        assertEquals(card.getUnit().getId(), unit.getId());
        assertEquals(card.getBalance(), cardBalance, 0.001);

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(1, events.size());
        CardEvent event = events.get(0);
        assertEquals(event.getEventType(), CardEventType.CARD_ASSIGNED_NON_BILLABLE);
    }

    @Test
    public void testAssignCardNonExistingBuilding() throws FactoryException {
        Building building = new BuildingFactory().withRate().build();
        Unit unit = new UnitFactory().create();
        Card card = new CardFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(Messages.get("BUILDING_NOT_FOUND"), json().get("result_message").asText());

        reload(card);
        assertNull(card.getUnit());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testAssignCardNonExistingUnit() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).build();
        Card card = new CardFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(Messages.get("UNIT_NOT_FOUND"), json().get("result_message").asText());

        reload(card);
        assertNull(card.getUnit());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testAssignCardNonExistingCard() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().build();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(Messages.get("CARD_NOT_FOUND"), json().get("result_message").asText());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testAssignCardAlreadyAssignedCard() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();
        Unit cardUnit = card.getUnit();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_ACCEPTABLE, status());
        assertEquals(
            APIException.APIErrors.NOT_ASSIGNABLE.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.NOT_ASSIGNABLE.message(),
            json().get("result_message").asText()
        );
        assertEquals("Esta tarjeta ya se encuentra asignada", json().get("result_detail").asText());

        reload(card);
        assertEquals(cardUnit, card.getUnit());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testAssignCardWithBuildingWithoutRate() throws FactoryException {
        Building building = new BuildingFactory().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(Messages.get("RATE_NOT_FOUND"), json().get("result_message").asText());

        reload(card);
        assertNull(card.getUnit());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testAssignCardUnitBelongsAnotherBuilding() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Building building2 = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building2).create();
        Card card = new CardFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignCard(token, building, unit, card, true);

        assertEquals(Http.Status.NOT_FOUND, status());

        assertEquals(Messages.get("UNIT_NOT_FOUND"), json().get("result_message").asText());

        reload(card);
        assertNull(card.getUnit());

        List<CardEvent> events = new CardEventQuery().all();
        assertEquals(0, events.size());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().create();
        String token = new UserFactory().withAuthentication().authenticate();

        assignCard(token, building, unit, card, false);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().postpaid().create();

        assignCard(null, building, unit, card, false);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
