package controllers.v1.buildings.units;

import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import factories.BuildingFactory;
import factories.FactoryException;
import factories.UnitFactory;
import factories.UserFactory;
import global.APIException;
import java.util.List;
import models.Building;
import models.Role;
import models.Unit;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;
import queries.untis.UnitQuery;

public class BuildingsControllerCreateUnitTest extends common.RequestBaseTest {

    private void createUnit(String token, Building building, JsonNode unit) {
        Call call = generateCall("POST", "/api/v1/buildings/" + building.getId() + "/units");

        request(call, token, unit);
    }

    @Test
    public void testCreation() throws FactoryException {
        Building building = new BuildingFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Unit unit = new UnitFactory().build();

        createUnit(token, building, jsonize(unit));

        assertEquals(Http.Status.CREATED, status());

        List<Unit> units = new UnitQuery().all();
        assertEquals(1, units.size());

        Unit createdUnit = units.get(0);
        assertEquals(unit.getNumber(), createdUnit.getNumber());
        assertEquals(unit.getTower(), createdUnit.getTower());
        assertEquals(unit.getContact(), createdUnit.getContact());
        assertEquals(building.getId(), createdUnit.getBuilding().getId());
    }

    @Test
    public void testCreationNonExistingBuilding() throws FactoryException {
        Building building = new BuildingFactory().build();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Unit unit = new UnitFactory().build();

        createUnit(token, building, jsonize(unit));

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        List<Unit> units = new UnitQuery().all();
        assertEquals(0, units.size());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().create();
        String token = new UserFactory().withAuthentication().authenticate();

        createUnit(token, building, null);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() {
        Building building = new BuildingFactory().create();

        createUnit(null, building, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    private JsonNode jsonize(Unit unit) {
        ObjectMapper mapper = new ObjectMapper();

        return mapper
            .createObjectNode()
            .put("number", unit.getNumber())
            .put("tower", unit.getTower())
            .put("contact", unit.getContact());
    }
}
