package controllers.v1.buildings.machines;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import factories.BuildingFactory;
import factories.FactoryException;
import factories.MachineFactory;
import factories.UserFactory;
import global.APIException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import models.Building;
import models.Machine;
import models.Maintenance;
import models.Role;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;
import queries.machines.MaintenanceQuery;

public class BuildingsControllerAssignMachineTest extends common.RequestBaseTest {

    private void assignMachine(String token, Building building, Machine machine)
        throws FactoryException {
        Call call = generateCall(
            "PUT",
            "/api/v1/buildings/" + building.getId() + "/machines/" + machine.getId()
        );

        request(call, token);
    }

    @Test
    public void testWasherAssignation() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().washer().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignMachine(token, building, machine);

        assertEquals(Http.Status.OK, status());
        assertEquals(1, Machine.count());

        reload(machine);
        assertNotNull(machine.getBuilding());
        assertEquals(building.getId(), machine.getBuilding().getId());

        List<Maintenance> maintenances = new MaintenanceQuery().all();
        assertEquals(2, maintenances.size());
        assertCollection(
            Arrays.asList(Maintenance.MaintenanceType.MP100, Maintenance.MaintenanceType.MP500),
            maintenances.stream().map(Maintenance::getMaintenanceType).collect(Collectors.toList())
        );
    }

    @Test
    public void testDryerAssignation() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().dryer().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignMachine(token, building, machine);

        assertEquals(Http.Status.OK, status());
        assertEquals(1, Machine.count());

        reload(machine);
        assertNotNull(machine.getBuilding());
        assertEquals(building.getId(), machine.getBuilding().getId());

        List<Maintenance> maintenances = new MaintenanceQuery().all();
        assertEquals(1, maintenances.size());
        assertCollection(
            Arrays.asList(Maintenance.MaintenanceType.MP1200),
            maintenances.stream().map(Maintenance::getMaintenanceType).collect(Collectors.toList())
        );
    }

    @Test
    public void testAssignationNonExistingBuilding() throws FactoryException {
        Building building = new BuildingFactory().build();
        Machine machine = new MachineFactory().create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignMachine(token, building, machine);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.BUILDING_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        assertEquals(1, Machine.count());
        List<Maintenance> maintenances = new MaintenanceQuery().all();
        assertEquals(0, maintenances.size());
    }

    @Test
    public void testAssignationNonExistingMachine() throws FactoryException {
        Building building = new BuildingFactory().create();
        Machine machine = new MachineFactory().build();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        assignMachine(token, building, machine);

        assertEquals(Http.Status.NOT_FOUND, status());
        assertEquals(
            APIException.APIErrors.MACHINE_NOT_FOUND.code(),
            json().get("result_code").asInt()
        );
        assertEquals(
            APIException.APIErrors.MACHINE_NOT_FOUND.message(),
            json().get("result_message").asText()
        );

        assertEquals(0, Machine.count());
        List<Maintenance> maintenances = new MaintenanceQuery().all();
        assertEquals(0, maintenances.size());
    }

    @Test
    public void testForbiddenAsUSER() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().washer().withFirmware().withMachineModel().create();
        String token = new UserFactory().withAuthentication().authenticate();

        assignMachine(token, building, machine);

        assertEquals(Http.Status.FORBIDDEN, status());
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        Building building = new BuildingFactory().withRate().create();
        Machine machine = new MachineFactory().washer().withFirmware().withMachineModel().create();

        assignMachine(null, building, machine);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }
}
