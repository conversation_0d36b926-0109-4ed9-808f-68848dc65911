package controllers.v1.reports.uses;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.fasterxml.jackson.databind.JsonNode;
import common.helpers.DateTimer;
import common.helpers.Formatter;
import common.helpers.QueryParam;
import factories.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import models.*;
import org.joda.time.DateTime;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;
import utils.DateHelper;
import utils.StringHelper;
import utils.TimeZoneUtils;

public class ReportsControllerFindUsesTest extends common.RequestBaseTest {

    private QueryParam[] generateQueryParam(
        Date from,
        Date to,
        Integer buildingId,
        String cardUuid,
        QueryParam... extraParams
    ) {
        Date fromDate = from == null ? DateTimer.ago(4, TimeUnit.DAYS) : from;
        Date toDate = to == null ? DateTime.now().toDate() : to;

        List<QueryParam> params = new ArrayList<>(
            Arrays.asList(
                new QueryParam("from", Formatter.format(fromDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")),
                new QueryParam("to", Formatter.format(toDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"))
            )
        );

        if (buildingId != null) {
            params.add(new QueryParam("building", buildingId));
        }

        if (!StringHelper.isBlank(cardUuid)) {
            params.add(new QueryParam("card", cardUuid));
        }

        if (extraParams != null) {
            params.addAll(Arrays.asList(extraParams));
        }

        return params.toArray(new QueryParam[0]);
    }

    private void findUses(String token, QueryParam... params) {
        Call call = generateCall("GET", "/api/v1/reports/uses");

        request(call, token, params == null ? new QueryParam[] {} : params);
    }

    @Test
    public void testFindUsesSingleSearch() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().withEvent().with("unit", unit).create();
        Machine machine = building.getMachines().get(0);
        MachineUse use = new MachineUsesFactory()
            .with("card", card)
            .with("machine", machine)
            .with("building", building)
            .create();

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params = this.generateQueryParam(null, null, building.getId(), card.getUuid());
        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(
            Formatter.format(
                TimeZoneUtils.getLocalDate(machine.getLastAlive().getTime(), true, false),
                "yyyy-MM-dd HH:mm:ss"
            ),
            json().get("oldestKeepAlive").asText()
        );
        assertEquals(machine.getSerialNumber(), json().get("serialNumber").asText());

        assertEquals(1, jsonSize("items"));
        JsonNode jsonUse = json().get("items").get(0);
        assertEquals(use.getId(), jsonUse.get("id").asInt());
        assertEquals(
            Formatter.format(use.getTimestamp(), "yyyy-MM-dd HH:mm:ss"),
            jsonUse.get("timestamp").asText()
        );
        assertEquals(use.getHeadline(), jsonUse.get("concept").asText());
        assertEquals(use.isAccredited(), jsonUse.get("accredited").asBoolean());
        assertEquals(use.getReason(), jsonUse.get("reason").asText());
        assertEquals(use.isAlert(), jsonUse.get("alert").asBoolean());
        assertEquals(TestHelpers.getResultName(use), jsonUse.get("result").asText());

        JsonNode jsonMachine = jsonUse.get("machine");
        assertEquals(machine.getMachineType().toString(), jsonMachine.get("machine_type").asText());
        assertEquals(machine.getCapacity(), jsonMachine.get("capacity").asInt());
        assertEquals(machine.getSortIndex(), jsonMachine.get("sort_index").asInt());
        assertEquals(machine.getReference(), jsonMachine.get("reference").asText());
        assertEquals(
            machine.getEnglishDescription(),
            jsonMachine.get("english_description").asText()
        );
        assertEquals(machine.getUnitPrice(), jsonMachine.get("unit_price").asDouble(), 0.01);
        assertEquals(machine.getUyPrice(), jsonMachine.get("uy_price").asDouble(), 0.01);
        assertEquals(machine.getAverageUseTime(), jsonMachine.get("average_use_time").asInt());
        assertEquals(machine.getExpectedUses(), jsonMachine.get("expected_uses").asInt());
        assertEquals(machine.getCurrentUses(), jsonMachine.get("current_uses").asInt());
        assertEquals(machine.getPrivateIp(), jsonMachine.get("private_ip").asText());
        assertEquals(machine.getPublicIp(), jsonMachine.get("public_ip").asText());
        assertEquals(machine.getPort(), jsonMachine.get("port").asText());
        assertEquals(machine.getLastAlive().getTime(), jsonMachine.get("last_keep_alive").asLong());
        assertEquals(machine.getLastAlive().getTime(), jsonMachine.get("lastKeepAlive").asLong());

        JsonNode jsonMachineBuilding = jsonMachine.get("building");
        assertEquals(building.getId(), jsonMachineBuilding.get("id").asInt());
        assertEquals(building.getName(), jsonMachineBuilding.get("name").asText());
        assertEquals(building.getRut(), jsonMachineBuilding.get("rut").asText());
        assertEquals(building.getAddress(), jsonMachineBuilding.get("address").asText());
        assertEquals(building.getCountry(), jsonMachineBuilding.get("country").asText());
        assertEquals(building.getCity(), jsonMachineBuilding.get("city").asText());
        assertEquals(building.getDepartamento(), jsonMachineBuilding.get("state").asText());
        assertEquals(building.getContact(), jsonMachineBuilding.get("contact").asText());
        assertEquals(
            building.getLatitude(),
            new BigDecimal(jsonMachineBuilding.get("latitude").asLong())
        );
        assertEquals(
            building.getLongitude(),
            new BigDecimal(jsonMachineBuilding.get("longitude").asLong())
        );
        assertEquals(
            building.getPrepaidRechargeableUses(),
            jsonMachineBuilding.get("prepaidRechargeableUses").asInt()
        );
        assertEquals(
            DateHelper.printDateAndTime(new DateTime(building.getLastBillingDate())),
            jsonMachineBuilding.get("lastBillingDate").asText()
        );
        assertEquals(
            building.getBuildingType().toString(),
            jsonMachineBuilding.get("buildingType").asText()
        );
        assertEquals(
            building.getMaxNumberOfUnits(),
            jsonMachineBuilding.get("maxNumberOfUnits").asInt()
        );

        JsonNode jsonMachineBuildingAdministration = jsonMachineBuilding.get("administration");
        Administration administration = building.getAdministration();
        assertEquals(administration.getId(), jsonMachineBuildingAdministration.get("id").asInt());
        assertEquals(
            administration.getName(),
            jsonMachineBuildingAdministration.get("name").asText()
        );
        assertEquals(
            administration.getContact(),
            jsonMachineBuildingAdministration.get("contact").asText()
        );
        assertEquals(
            administration.getAddress(),
            jsonMachineBuildingAdministration.get("address").asText()
        );
        assertEquals(
            administration.getClosureDay(),
            jsonMachineBuildingAdministration.get("closureDay").asInt()
        );
        assertWithDeviation(
            Formatter.format(administration.getMpCreationDate(), "yyyy-MM-dd HH:mm:ss.SSS"),
            jsonMachineBuildingAdministration.get("mpCreationDate").asText(),
            1
        );

        JsonNode jsonMachineBuildingRate = jsonMachineBuilding.get("rate");
        Rate rate = reload(building.getRate());
        assertEquals(rate.getName(), jsonMachineBuildingRate.get("name").asText());
        assertEquals(rate.getId(), jsonMachineBuildingRate.get("id").asInt());
        assertEquals(
            rate.getPriceCustomer(),
            jsonMachineBuildingRate.get("priceCustomer").asDouble(),
            0.01
        );

        JsonNode jsonUnit = jsonUse.get("unit");
        assertEquals(unit.getId(), jsonUnit.get("id").asInt());
        assertEquals(unit.getTower() + " - " + unit.getNumber(), jsonUnit.get("name").asText());
        assertEquals(unit.getNumber(), jsonUnit.get("number").asText());
        assertEquals(unit.getTower(), jsonUnit.get("tower").asText());
        assertEquals(unit.getContact(), jsonUnit.get("contact").asText());

        JsonNode jsonCard = jsonUse.get("card");
        assertEquals(card.getUuid(), jsonCard.get("uuid").asText());
        assertEquals(card.isMaster(), jsonCard.get("master").asBoolean());
        assertEquals(card.getContractType().toString(), jsonCard.get("contract_type").asText());
        assertEquals(card.getState().toString(), jsonCard.get("state").asText());
        assertEquals(card.getBalance(), jsonCard.get("balance").asDouble(), 0.001);
        assertEquals(
            Formatter.format(card.getStartTimeOfUse(), "yyyy-MM-dd HH:mm:ss"),
            jsonCard.get("start_time_of_use").asText()
        );
        assertEquals(
            Formatter.format(card.getEndTimeOfUse(), "yyyy-MM-dd HH:mm:ss"),
            jsonCard.get("end_time_of_use").asText()
        );
        assertEquals(card.getDiscount(), jsonCard.get("discount").asDouble(), 0.001);
        assertEquals(card.getSubState().toString(), jsonCard.get("substate").asText());
        assertEquals(card.getSubState().toString(), jsonCard.get("substate").asText());
        assertEquals(unit.getId(), jsonCard.get("unit_id").asInt());
        assertEquals(unit.getNumber(), jsonCard.get("unit_number").asText());
        assertEquals(unit.getTower(), jsonCard.get("unit_tower").asText());
        assertEquals(card.getStartTimeOfUse().getTime(), jsonCard.get("start_time").asLong());
        assertEquals(card.getEndTimeOfUse().getTime(), jsonCard.get("end_time").asLong());

        CardEvent cardEvent = card.getCardEvents().get(0);
        JsonNode jsonCardCardEvent = jsonCard.get("card_events").get(0);
        assertEquals(
            Formatter.format(cardEvent.getTimestamp(), "yyyy-MM-dd HH:mm:ss"),
            jsonCardCardEvent.get("timestamp").asText()
        );
        assertEquals(cardEvent.getHeadline(), jsonCardCardEvent.get("headline").asText());
        assertEquals(cardEvent.getUid(), jsonCardCardEvent.get("uid").asText());
        assertEquals(
            cardEvent.getEventType().toString(),
            jsonCardCardEvent.get("event_type").asText()
        );
    }

    @Test
    public void testFindUsesByPostpaidBuilding() throws FactoryException {
        Building building = new BuildingFactory().postpaid().withRate().withMachine().create();
        List<Unit> units = new UnitFactory()
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));
        List<Card> cards = new CardFactory()
            .with("unit", () -> units.get(faker.number().numberBetween(0, units.size())))
            .create(faker.number().numberBetween(1, 100));
        Machine machine = building.getMachines().get(0);
        List<MachineUse> uses = new MachineUsesFactory()
            .with("card", () -> cards.get(faker.number().numberBetween(0, cards.size())))
            .with("machine", machine)
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        Building extraBuilding = new BuildingFactory()
            .prepaid()
            .withRate()
            .withMachine()
            .withUnit()
            .create();
        Unit extraUnit = extraBuilding.getUnits().get(0);
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> extraUses = new MachineUsesFactory()
            .with("card", extraCard)
            .with("machine", extraBuilding.getMachines().get(0))
            .with("building", extraBuilding)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        Date from = faker.date().past(2, TimeUnit.DAYS);
        Date to = faker.date().past(1, TimeUnit.SECONDS);
        QueryParam[] params = this.generateQueryParam(null, null, building.getId(), null);

        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(
            Formatter.format(
                TimeZoneUtils.getLocalDate(machine.getLastAlive().getTime(), true, false),
                "yyyy-MM-dd HH:mm:ss"
            ),
            json().get("oldestKeepAlive").asText()
        );
        assertEquals(machine.getSerialNumber(), json().get("serialNumber").asText());

        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesByPrepaidBuilding() throws FactoryException {
        Building building = new BuildingFactory().prepaid().withRate().withMachine().create();
        List<Unit> units = new UnitFactory()
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));
        List<Card> cards = new CardFactory()
            .with("unit", () -> units.get(faker.number().numberBetween(0, units.size())))
            .create(faker.number().numberBetween(1, 100));
        Machine machine = building.getMachines().get(0);
        List<MachineUse> uses = new MachineUsesFactory()
            .with("card", () -> cards.get(faker.number().numberBetween(0, cards.size())))
            .with("machine", machine)
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        Building extraBuilding = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withUnit()
            .create();
        Unit extraUnit = extraBuilding.getUnits().get(0);
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> extraUses = new MachineUsesFactory()
            .with("card", extraCard)
            .with("machine", extraBuilding.getMachines().get(0))
            .with("building", extraBuilding)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params = this.generateQueryParam(null, null, building.getId(), null);

        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(
            Formatter.format(
                TimeZoneUtils.getLocalDate(machine.getLastAlive().getTime(), true, false),
                "yyyy-MM-dd HH:mm:ss"
            ),
            json().get("oldestKeepAlive").asText()
        );
        assertEquals(machine.getSerialNumber(), json().get("serialNumber").asText());

        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesWithoutUnitResultOrdered() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();

        List<MachineUse> uses = new MachineUsesFactory()
            .with("machine", building.getMachines().get(0))
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        findUses(token, this.generateQueryParam(null, null, building.getId(), null));

        assertEquals(Http.Status.OK, status());

        assertEquals(
            uses
                .stream()
                .sorted(Comparator.comparing(MachineUse::getTimestamp))
                .map(MachineUse::getId)
                .collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesWithUnitResultOrdered() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Machine machine = building.getMachines().get(0);
        Unit unit_01_001 = new UnitFactory()
            .with("building", building)
            .with("tower", "01")
            .with("number", "001")
            .create();
        Unit unit_01_101 = new UnitFactory()
            .with("building", building)
            .with("tower", "01")
            .with("number", "101")
            .create();
        Unit unit_02_001 = new UnitFactory()
            .with("building", building)
            .with("tower", "02")
            .with("number", "001")
            .create();
        Card card_01_001 = new CardFactory().with("unit", unit_01_001).create();
        Card card_01_101 = new CardFactory().with("unit", unit_01_101).create();
        Card card_02_001 = new CardFactory().with("unit", unit_02_001).create();

        Date _10daysAgo = DateTimer.ago(10, TimeUnit.DAYS);
        Date _9daysAgo = DateTimer.ago(9, TimeUnit.DAYS);
        Date _8daysAgo = DateTimer.ago(8, TimeUnit.DAYS);
        Date _7daysAgo = DateTimer.ago(7, TimeUnit.DAYS);
        Date _6daysAgo = DateTimer.ago(6, TimeUnit.DAYS);
        Date _5daysAgo = DateTimer.ago(5, TimeUnit.DAYS);
        MachineUse use3rd = new MachineUsesFactory()
            .with("card", card_01_001)
            .with("timestamp", _5daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use8th = new MachineUsesFactory()
            .with("card", card_02_001)
            .with("timestamp", _8daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use1st = new MachineUsesFactory()
            .with("card", card_01_001)
            .with("timestamp", _8daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use7th = new MachineUsesFactory()
            .with("card", card_02_001)
            .with("timestamp", _9daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use2nd = new MachineUsesFactory()
            .with("card", card_01_001)
            .with("timestamp", _7daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use10th = new MachineUsesFactory()
            .with("card", card_02_001)
            .with("timestamp", _6daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use5th = new MachineUsesFactory()
            .with("card", card_01_101)
            .with("timestamp", _7daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use6th = new MachineUsesFactory()
            .with("card", card_02_001)
            .with("timestamp", _10daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use9th = new MachineUsesFactory()
            .with("card", card_02_001)
            .with("timestamp", _7daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse use4th = new MachineUsesFactory()
            .with("card", card_01_101)
            .with("timestamp", _8daysAgo)
            .with("machine", machine)
            .with("building", building)
            .create();
        MachineUse[] uses = new MachineUse[] {
            use1st,
            use2nd,
            use3rd,
            use4th,
            use5th,
            use6th,
            use7th,
            use8th,
            use9th,
            use10th,
        };

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        findUses(token, this.generateQueryParam(_10daysAgo, null, building.getId(), null));

        assertEquals(Http.Status.OK, status());

        assertEquals(
            Arrays.stream(uses).map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByFromTo() throws FactoryException {
        Card card = new CardFactory().create();

        List<MachineUse> uses = new MachineUsesFactory()
            .with("timestamp", DateTimer.ago(2, TimeUnit.DAYS))
            .with("card", card)
            .create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params =
            this.generateQueryParam(
                    DateTimer.ago(4, TimeUnit.DAYS),
                    DateTimer.ago(1, TimeUnit.DAYS),
                    null,
                    card.getUuid()
                );

        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByCard() throws FactoryException {
        List<MachineUse> usesWithoutCard = new MachineUsesFactory().create(5);
        Card extraCard = new CardFactory().create();
        List<MachineUse> usesExtraCard = new MachineUsesFactory().with("card", extraCard).create(5);
        Card card = new CardFactory().create();
        List<MachineUse> uses = new MachineUsesFactory().with("card", card).create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params = this.generateQueryParam(null, null, null, card.getUuid());

        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByUnit() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit extraUnit = new UnitFactory().with("building", building).create();
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> usesExtraUnit = new MachineUsesFactory()
            .with("timestamp", DateTimer.ago(1, TimeUnit.DAYS))
            .with("machine", building.getMachines().get(0))
            .with("card", extraCard)
            .with("building", building)
            .create(5);
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();
        List<MachineUse> uses = new MachineUsesFactory()
            .with("timestamp", DateTimer.ago(1, TimeUnit.DAYS))
            .with("machine", building.getMachines().get(0))
            .with("card", card)
            .with("building", building)
            .create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params =
            this.generateQueryParam(
                    null,
                    null,
                    building.getId(),
                    null,
                    new QueryParam("unit", unit.getId())
                );
        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByBuilding() throws FactoryException {
        Building extraBuilding = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit extraUnit = new UnitFactory().with("building", extraBuilding).create();
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> usesExtraBuilding = new MachineUsesFactory()
            .withMachine()
            .with("card", extraCard)
            .with("building", extraBuilding)
            .create(5);
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();
        Machine machine = building.getMachines().get(0);
        List<MachineUse> uses = new MachineUsesFactory()
            .with("card", card)
            .with("machine", machine)
            .with("building", building)
            .create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params = this.generateQueryParam(null, null, building.getId(), null);

        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByMachine() throws FactoryException {
        Building extraBuilding = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit extraUnit = new UnitFactory().with("building", extraBuilding).create();
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> usesExtraMachine = new MachineUsesFactory()
            .with("machine", extraBuilding.getMachines().get(0))
            .with("card", extraCard)
            .with("building", extraBuilding)
            .create(5);
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit unit = new UnitFactory().with("building", building).create();
        Card card = new CardFactory().with("unit", unit).create();
        Machine machine = building.getMachines().get(0);
        List<MachineUse> uses = new MachineUsesFactory()
            .with("card", card)
            .with("machine", machine)
            .with("building", building)
            .create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params =
            this.generateQueryParam(
                    null,
                    null,
                    building.getId(),
                    null,
                    new QueryParam("machine", machine.getId())
                );
        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testFindUsesResultByAccount() throws FactoryException {
        User extraUser = new UserFactory().withAccount().create();
        Building extraBuilding = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        Unit extraUnit = new UnitFactory()
            .with("building", extraBuilding)
            .with("owner", extraUser)
            .create();
        Card extraCard = new CardFactory().with("unit", extraUnit).create();
        List<MachineUse> usesExtraOwner = new MachineUsesFactory()
            .with("machine", extraBuilding.getMachines().get(0))
            .with("card", extraCard)
            .with("building", extraBuilding)
            .create(5);
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();
        User user = new UserFactory().withAccount().create();
        Unit unit = new UnitFactory().with("building", building).with("owner", user).create();
        Card card = new CardFactory().with("unit", unit).create();
        Machine machine = building.getMachines().get(0);
        List<MachineUse> uses = new MachineUsesFactory()
            .with("card", card)
            .with("machine", machine)
            .with("building", building)
            .create(5);

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        QueryParam[] params =
            this.generateQueryParam(
                    null,
                    null,
                    building.getId(),
                    null,
                    new QueryParam("account", user.getMasterAccount().getId())
                );
        findUses(token, params);

        assertEquals(Http.Status.OK, status());

        assertNotNull(json());
        assertEquals(uses.size(), jsonSize("items"));
        assertCollection(
            uses.stream().map(MachineUse::getId).collect(Collectors.toList()),
            jsonStream("items").map(j -> j.get("id").asInt()).collect(Collectors.toList())
        );
    }

    @Test
    public void testSearchAsMASTER() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();

        List<MachineUse> uses = new MachineUsesFactory()
            .with("machine", building.getMachines().get(0))
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.MASTER)
            .withAuthentication()
            .authenticate();

        findUses(token, this.generateQueryParam(null, null, building.getId(), null));

        assertEquals(Http.Status.OK, status());

        assertEquals(uses.size(), jsonSize("items"));
    }

    @Test
    public void testSearchAsASSISTANT() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();

        List<MachineUse> uses = new MachineUsesFactory()
            .with("machine", building.getMachines().get(0))
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.ASSISTANT)
            .withAuthentication()
            .authenticate();

        findUses(token, this.generateQueryParam(null, null, building.getId(), null));

        assertEquals(Http.Status.OK, status());

        assertEquals(uses.size(), jsonSize("items"));
    }

    @Test
    public void testSearchAsBUILDING_ADM() throws FactoryException {
        Building building = new BuildingFactory()
            .postpaid()
            .withRate()
            .withMachine()
            .withAdministration()
            .create();

        List<MachineUse> uses = new MachineUsesFactory()
            .with("machine", building.getMachines().get(0))
            .with("building", building)
            .create(faker.number().numberBetween(1, 100));

        String token = new UserFactory()
            .with("role", Role.BUILDING_ADM)
            .withAuthentication()
            .authenticate();

        findUses(token, this.generateQueryParam(null, null, building.getId(), null));

        assertEquals(Http.Status.OK, status());

        assertEquals(uses.size(), jsonSize("items"));
    }

    @Test
    public void testUnauthorizedWithoutCredentials() throws FactoryException {
        findUses(null, null);

        assertEquals(Http.Status.UNAUTHORIZED, status());
    }

    protected static class TestHelpers {

        public static String getResultName(MachineUse machineUse) {
            if (machineUse.getResult() == null) return MachineUseResult.NON_APPLIED.getName();

            if (machineUse.getResult().equals("None")) return "None";

            return MachineUseResult.getEnum(machineUse.getResult()).getName();
        }
    }
}
