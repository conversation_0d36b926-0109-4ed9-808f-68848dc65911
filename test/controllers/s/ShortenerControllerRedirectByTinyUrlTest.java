package controllers.s;

import static org.junit.Assert.assertEquals;

import factories.TinyUrlFactory;
import models.TinyUrl;
import org.junit.Test;
import play.mvc.Call;
import play.mvc.Http;

public class ShortenerControllerRedirectByTinyUrlTest extends common.RequestBaseTest {

    private void getRedirection(String token) {
        Call call = generateCall("GET", "/s/" + token);

        request(call);
    }

    @Test
    public void testRedirection() {
        TinyUrl tinyUrl = new TinyUrlFactory().active().create();

        getRedirection(tinyUrl.getToken());

        assertEquals(Http.Status.SEE_OTHER, status());
        assertEquals(tinyUrl.getDestinationUrl(), this.response.redirectLocation());
    }

    @Test
    public void testRedirectionNonActivatedUrl() {
        TinyUrl tinyUrl = new TinyUrlFactory().create();

        getRedirection(tinyUrl.getToken());

        assertEquals(Http.Status.NOT_FOUND, status());
    }

    @Test
    public void testRedirectionNonExistingUrl() {
        TinyUrl tinyUrl = new TinyUrlFactory().build();

        getRedirection(tinyUrl.getToken());

        assertEquals(Http.Status.NOT_FOUND, status());
    }
}
