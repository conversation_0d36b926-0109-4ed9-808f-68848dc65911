package models;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import factories.UserFactory;
import org.junit.Test;

public class UserTest extends common.BaseTest {

    @Test
    public void testFindByEmailAddress() {
        User storedUser = new UserFactory().create();

        User user = User.findByEmailAddress(storedUser.getEmailAddress());

        assertNotNull(user);
        assertEquals(storedUser.getEmailAddress(), user.getEmailAddress());
        assertEquals(storedUser.getFirstName(), user.getFirstName());
        assertEquals(storedUser.getLastName(), user.getLastName());
        assertEquals(storedUser.getRole(), user.getRole());
    }
}
