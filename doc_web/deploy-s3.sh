#!/bin/bash

# Deploy static documentation to S3
# Usage: ./deploy-s3.sh <bucket-name>

set -e

BUCKET_NAME=$1
STATIC_DIR="./static-dist"
AWS_PROFILE=${AWS_PROFILE:-lavomat}

if [ -z "$BUCKET_NAME" ]; then
    echo "❌ Error: Bucket name is required"
    echo "Usage: ./deploy-s3.sh <bucket-name>"
    echo "Example: ./deploy-s3.sh my-docs-bucket"
    exit 1
fi

if [ ! -d "$STATIC_DIR" ]; then
    echo "❌ Error: Static files not found. Run 'yarn build' first."
    exit 1
fi

echo "🚀 Deploying to S3 bucket: $BUCKET_NAME"
echo "📁 Source directory: $STATIC_DIR"
echo "🔑 Using AWS profile: $AWS_PROFILE"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI is not installed"
    echo "Please install AWS CLI: https://aws.amazon.com/cli/"
    exit 1
fi

# Check if bucket exists
if ! aws --profile "$AWS_PROFILE" s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
    echo "❌ Error: Bucket '$BUCKET_NAME' does not exist or you don't have access"
    echo "Please create the bucket first or check your AWS credentials"
    exit 1
fi

# Sync files to S3
echo "📤 Uploading files..."
aws --profile "$AWS_PROFILE" s3 sync "$STATIC_DIR" "s3://$BUCKET_NAME" \
    --delete \
    --cache-control "max-age=3600" \
    --metadata-directive REPLACE

# Set content type for HTML files
echo "🔧 Setting content types..."
aws --profile "$AWS_PROFILE" s3 cp "s3://$BUCKET_NAME" "s3://$BUCKET_NAME" \
    --recursive \
    --exclude "*" \
    --include "*.html" \
    --content-type "text/html" \
    --metadata-directive REPLACE

echo "✅ Deployment complete!"
echo "🌐 Your documentation is now available at:"
echo "   http://$BUCKET_NAME.s3-website-us-west-2.amazonaws.com"
echo ""
echo "💡 To enable static website hosting, run:"
echo "   aws --profile $AWS_PROFILE s3 website s3://$BUCKET_NAME --index-document index.html --error-document index.html"
