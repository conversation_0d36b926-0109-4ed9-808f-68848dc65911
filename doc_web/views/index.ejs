<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>lm-app documentation</title>
  <style>
    @import url('https://fonts.googleapis.com/css?family=Roboto:400,700|Inconsolata|Raleway:200');

    body {
      color: black;
      background: white;
      font: 400 14px / 1.42 'Roboto', Helvetica, sans-serif;
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
      color: black;
      margin: 12px 0;
    }

    h1 {
      font: 200 36px 'Raleway', Helvetica, sans-serif;
      font-size: 36px;
    }

    .container {
      margin-left: 194px;
      width: 526px;
    }
  </style>
</head>
<body>
  <%- include('header.ejs') %>

  <div class="container">
    <h1>LAVOMAT - API documentation</h1>

    <ul>
      <% docs.forEach((doc)=> { %>
        <li>
          <h3><a href="<%= doc.url %>">
              <%= doc.name %>
            </a></h3>
        </li>
      <% })%>
      <% if (!docs.length) { %>
        <h3>No docs found</h3>
      <% } %>
    </ul>
  </div>
</body>
</html>
