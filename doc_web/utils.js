const fs = require('fs'),
  path = require('path'),
  aglio = require('aglio')

const CONTAINER_FOLDER = 'dist'
const DOCUMENT_EXTENSION = '.apib'

const getDocs = () => {
  if (!fs.existsSync(CONTAINER_FOLDER)) {
    console.error(`Container folder ${CONTAINER_FOLDER} does not exist`)
    return []
  }

  const docs = []

  const scanDirectory = (dirPath, relativePath = '') => {
    const items = fs.readdirSync(dirPath)

    for (const item of items) {
      const fullPath = path.join(dirPath, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        // Recursively scan subdirectories
        const newRelativePath = relativePath ? `${relativePath}/${item}` : item
        scanDirectory(fullPath, newRelativePath)
      } else if (path.extname(item) === DOCUMENT_EXTENSION) {
        // Found an .apib file
        const name = path.parse(item).name
        const displayName = relativePath
          ? `${relativePath}/${name}`.replaceAll('_', '/').replaceAll('/', ' / ')
          : name.replaceAll('_', '/')

        const urlName = relativePath ? `${relativePath}_${name}` : name

        docs.push({
          name: displayName,
          url: `/${urlName}`,
          absolutePath: fullPath,
          relativePath: relativePath || '',
        })
      }
    }
  }

  scanDirectory(path.join(__dirname, CONTAINER_FOLDER))

  // Sort docs by name for consistent ordering
  return docs.sort((a, b) => a.name.localeCompare(b.name))
}

const DOCS = getDocs()

const getByUrl = (url) => DOCS.find((item) => item.url === url)

const readFile = (...args) => {
  return new Promise((resolve, reject) => {
    fs.readFile(...args, (err, data) => {
      if (err) return reject(err)
      resolve(data)
    })
  })
}

const aglioRender = (...args) => {
  return new Promise((resolve, reject) => {
    aglio.render(...args, (err, html, warnings) => {
      if (err) {
        console.log(err)
        return reject(err)
      }
      if (warnings) console.log(warnings)

      resolve(html)
    })
  })
}

const getHtmlByDoc = async ({ name, absolutePath }) => {
  const blueprint = await readFile(absolutePath, 'utf8')

  const html = await aglioRender(blueprint, { themeVariables: 'default' })

  return html
}

module.exports = {
  DOCS,
  CONTAINER_FOLDER,
  getByUrl,
  getHtmlByDoc,
}
