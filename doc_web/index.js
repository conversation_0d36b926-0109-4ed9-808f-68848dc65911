const path = require('path'),
  express = require('express')
;(utils = require('./utils')), (dotenv = require('dotenv'))

dotenv.config()
const app = express()

app.set('view engine', 'ejs')

app.get('/', (req, res) =>
  res.render('index', {
    docs: utils.DOCS,
  })
)

app.get('/*', async (req, res) => {
  const doc = utils.getByUrl(req.url)
  if (doc) {
    const html = await utils.getHtmlByDoc(doc)
    res.render('doc', { html })
  } else {
    res.status(400).send('Documentation not found')
  }
})

app.listen(process.env.PORT, () => {
  console.log(`server started on port ${process.env.PORT}`)
})
