{"name": "lm-doc", "version": "1.0.0", "description": "Viewer for API documentation", "main": "index.js", "private": true, "scripts": {"dev": "mkdir -p ./dist && cp -a ../doc/*.apib ./dist && nodemon -e js,ejs", "start": "node index.js", "build": "node build-static.js", "build:clean": "rm -rf ./static-dist && yarn build", "deploy": "node build-static.js && ./deploy-s3.sh doc.app-sandbox.lavomat.com.uy"}, "engines": {"node": "16.13"}, "dependencies": {"aglio": "^2.3.0", "dotenv": "^14.3.2", "ejs": "^3.1.6", "express": "^4.17.2"}, "devDependencies": {"nodemon": "^2.0.15"}}