{"name": "lm-doc", "version": "1.0.0", "description": "Viewer for API documentation", "main": "index.js", "private": true, "scripts": {"dev": "mkdir -p ./dist && cp -a ../doc/*.apib ./dist && nodemon -e js,ejs", "start": "node index.js", "deploy:sandbox": "aws s3 sync build/ s3://doc.app-sandbox.lavomat.com.uy --profile lavomat"}, "engines": {"node": "16.13"}, "dependencies": {"aglio": "^2.3.0", "dotenv": "^14.3.2", "ejs": "^3.1.6", "express": "^4.17.2"}, "devDependencies": {"nodemon": "^2.0.15"}}