{"name": "lm-doc", "version": "1.0.0", "description": "Static site generator for API documentation", "main": "index.js", "private": true, "scripts": {"build": "node build-static.js", "build:clean": "rm -rf ./static-dist && yarn build", "deploy": "yarn build && ./deploy-s3.sh doc.app-sandbox.lavomat.com.uy", "dev": "rm -rf ./dist && cp -r ../doc ./dist && nodemon", "start": "node index.js"}, "engines": {"node": "16.13"}, "dependencies": {"aglio": "^2.3.0", "ejs": "^3.1.6"}, "devDependencies": {"express": "^4.17.2", "nodemon": "^2.0.15"}}