# Static Documentation Site for S3 Hosting

This directory contains the tools to convert the dynamic doc_web application into static HTML files suitable for hosting on Amazon S3.

## Overview

The original doc_web application was a dynamic Node.js/Express application that processed API Blueprint (.apib) files on-demand. This static version pre-generates all HTML files during build time, making it perfect for S3 static website hosting.

## Benefits of Static Hosting

- **Cost-effective**: S3 static hosting is much cheaper than running servers
- **Performance**: Static files load faster than dynamic generation
- **Scalability**: S3 can handle high traffic without server scaling concerns
- **Simplicity**: No server maintenance or monitoring required
- **Reliability**: Higher uptime compared to server-based solutions

## Files

- `build-static.js` - Main build script that generates static HTML files
- `deploy-s3.sh` - Deployment script for uploading to S3
- `static-dist/` - Output directory containing generated static files
- `README-STATIC.md` - This documentation file

## Prerequisites

1. **Node.js 16.13.1** (use `nvm use` to switch to the correct version)
2. **Yarn** package manager
3. **AWS CLI** (for deployment)

## Quick Start

### 1. Install Dependencies

```bash
# Switch to correct Node.js version
nvm use

# Install dependencies
yarn install
```

### 2. Build Static Files

```bash
# Generate static HTML files
yarn build

# Or clean build (removes previous build)
yarn build:clean
```

This will:
- Process all `.apib` files from the `../doc` folder
- Generate individual HTML pages for each API documentation
- Create an `index.html` with links to all documentation
- Output everything to `./static-dist/`

### 3. Deploy to S3

```bash
# Deploy to your S3 bucket
./deploy-s3.sh your-bucket-name
```

## S3 Setup Instructions

### 1. Create S3 Bucket

```bash
# Create bucket (replace with your bucket name)
aws s3 mb s3://your-docs-bucket-name

# Enable static website hosting
aws s3 website s3://your-docs-bucket-name \
    --index-document index.html \
    --error-document index.html
```

### 2. Configure Bucket Policy

Create a bucket policy to make files publicly readable:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-docs-bucket-name/*"
        }
    ]
}
```

Apply the policy:

```bash
aws s3api put-bucket-policy \
    --bucket your-docs-bucket-name \
    --policy file://bucket-policy.json
```

### 3. Access Your Documentation

Your documentation will be available at:
- **S3 Website URL**: `http://your-docs-bucket-name.s3-website-us-east-1.amazonaws.com`
- **CloudFront** (optional): Set up CloudFront distribution for HTTPS and better performance

## Build Process Details

The `build-static.js` script:

1. **Scans** the `../doc` folder for `.apib` files
2. **Processes** each file using Aglio (same as the dynamic version)
3. **Generates** individual HTML pages with proper navigation
4. **Creates** an index page with links to all documentation
5. **Maintains** the same styling and structure as the original

## Customization

### Styling

The generated HTML includes the same CSS as the original application. To customize:

1. Edit the styles in `build-static.js` (around line 100)
2. Or modify the EJS templates in `./views/`
3. Rebuild with `yarn build`

### Adding New Documentation

1. Add new `.apib` files to the `../doc` folder
2. Run `yarn build` to regenerate static files
3. Deploy with `./deploy-s3.sh your-bucket-name`

## Troubleshooting

### Build Errors

- **Module not found**: Run `yarn install` to install dependencies
- **Node version**: Use `nvm use` to switch to Node.js 16.13.1
- **API Blueprint errors**: Check the console output for specific .apib file issues

### Deployment Issues

- **AWS CLI not found**: Install AWS CLI and configure credentials
- **Bucket access denied**: Check your AWS credentials and bucket permissions
- **Files not updating**: The deployment script uses `--delete` flag to remove old files

## Performance Optimization

For better performance, consider:

1. **CloudFront CDN**: Set up CloudFront distribution for global caching
2. **Gzip Compression**: Enable gzip compression in S3 or CloudFront
3. **Custom Domain**: Use Route 53 to set up a custom domain

## Maintenance

The static site needs to be rebuilt and redeployed when:
- API documentation (.apib files) are updated
- Styling changes are made
- New documentation is added

Set up a CI/CD pipeline to automate this process when documentation changes.
