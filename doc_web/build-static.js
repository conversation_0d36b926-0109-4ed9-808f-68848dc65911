const fs = require('fs')
const path = require('path')
const aglio = require('aglio')
const ejs = require('ejs')

// Configuration
const SOURCE_FOLDER = '../doc'
const OUTPUT_FOLDER = './static-dist'
const DOCUMENT_EXTENSION = '.apib'

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_FOLDER)) {
  fs.mkdirSync(OUTPUT_FOLDER, { recursive: true })
}

// Helper functions from utils.js
const readFile = (...args) => {
  return new Promise((resolve, reject) => {
    fs.readFile(...args, (err, data) => {
      if (err) return reject(err)
      resolve(data)
    })
  })
}

const aglioRender = (...args) => {
  return new Promise((resolve, reject) => {
    aglio.render(...args, (err, html, warnings) => {
      if (err) {
        console.log(err)
        return reject(err)
      }
      if (warnings) console.log(warnings)
      resolve(html)
    })
  })
}

// Recursively get all .apib files from the doc folder and subdirectories
const getDocs = () => {
  if (!fs.existsSync(SOURCE_FOLDER)) {
    console.error(`Source folder ${SOURCE_FOLDER} does not exist`)
    return []
  }

  const docs = []

  const scanDirectory = (dirPath, relativePath = '') => {
    const items = fs.readdirSync(dirPath)

    for (const item of items) {
      const fullPath = path.join(dirPath, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        // Recursively scan subdirectories
        const newRelativePath = relativePath ? `${relativePath}/${item}` : item
        scanDirectory(fullPath, newRelativePath)
      } else if (path.extname(item) === DOCUMENT_EXTENSION) {
        // Found an .apib file
        const name = path.parse(item).name
        const displayName = relativePath
          ? `${relativePath}/${name}`.replaceAll('_', '/').replaceAll('/', ' / ')
          : name.replaceAll('_', '/')

        docs.push({
          name: displayName,
          url: `/${relativePath ? relativePath + '_' : ''}${name}.html`,
          fileName: `${relativePath ? relativePath + '_' : ''}${name}.html`,
          absolutePath: fullPath,
          relativePath: relativePath || '',
        })
      }
    }
  }

  scanDirectory(path.join(__dirname, SOURCE_FOLDER))

  // Sort docs by name for consistent ordering
  return docs.sort((a, b) => a.name.localeCompare(b.name))
}

// Read and compile EJS templates
const readTemplate = async (templatePath) => {
  const templateContent = await readFile(templatePath, 'utf8')
  return ejs.compile(templateContent)
}

// Generate HTML for a single document
const generateDocHtml = async (doc, headerTemplate) => {
  try {
    console.log(`Processing ${doc.name}...`)
    
    // Read the API Blueprint file
    const blueprint = await readFile(doc.absolutePath, 'utf8')
    
    // Convert to HTML using Aglio
    const aglioHtml = await aglioRender(blueprint, { themeVariables: 'default' })
    
    // Render the doc template with header
    const headerHtml = headerTemplate()
    
    const fullHtml = `${headerHtml}

<style>
  @media print {
    .back-to-top {
      display: none !important;
    }
  }
</style>

${aglioHtml}`
    
    return fullHtml
  } catch (error) {
    console.error(`Error processing ${doc.name}:`, error)
    return null
  }
}

// Generate the index page
const generateIndexHtml = async (docs, headerTemplate, indexTemplate) => {
  const headerHtml = headerTemplate()
  const indexHtml = indexTemplate({ docs })
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>lm-app documentation</title>
  <style>
    @import url('https://fonts.googleapis.com/css?family=Roboto:400,700|Inconsolata|Raleway:200');

    body {
      color: black;
      background: white;
      font: 400 14px / 1.42 'Roboto', Helvetica, sans-serif;
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
      color: black;
      margin: 12px 0;
    }

    h1 {
      font: 200 36px 'Raleway', Helvetica, sans-serif;
      font-size: 36px;
    }

    .container {
      margin-left: 194px;
      width: 526px;
    }
  </style>
</head>
<body>
  ${headerHtml}

  <div class="container">
    <h1>LAVOMAT - API documentation</h1>

    <ul>
      ${docs.map(doc => `
        <li>
          <h3><a href="${doc.url}">
              ${doc.name}
            </a></h3>
        </li>
      `).join('')}
      ${docs.length === 0 ? '<h3>No docs found</h3>' : ''}
    </ul>
  </div>
</body>
</html>`
}

// Main build function
const buildStatic = async () => {
  try {
    console.log('Starting static site generation...')
    
    // Get all documentation files
    const docs = getDocs()
    console.log(`Found ${docs.length} documentation files`)
    
    if (docs.length === 0) {
      console.log('No documentation files found. Make sure .apib files exist in the doc folder.')
      return
    }
    
    // Load templates
    const headerTemplate = await readTemplate('./views/header.ejs')
    const indexBodyTemplate = ejs.compile(`
      <div class="container">
        <h1>LAVOMAT - API documentation</h1>
        <ul>
          <% docs.forEach((doc)=> { %>
            <li>
              <h3><a href="<%= doc.url %>">
                  <%= doc.name %>
                </a></h3>
            </li>
          <% })%>
          <% if (!docs.length) { %>
            <h3>No docs found</h3>
          <% } %>
        </ul>
      </div>
    `)
    
    // Generate index.html
    console.log('Generating index.html...')
    const indexHtml = await generateIndexHtml(docs, headerTemplate, indexBodyTemplate)
    fs.writeFileSync(path.join(OUTPUT_FOLDER, 'index.html'), indexHtml)
    
    // Generate individual documentation pages
    let successCount = 0
    for (const doc of docs) {
      const docHtml = await generateDocHtml(doc, headerTemplate)
      if (docHtml) {
        fs.writeFileSync(path.join(OUTPUT_FOLDER, doc.fileName), docHtml)
        successCount++
      }
    }
    
    console.log(`\n✅ Static site generation complete!`)
    console.log(`📁 Output directory: ${OUTPUT_FOLDER}`)
    console.log(`📄 Generated ${successCount} documentation pages + 1 index page`)
    console.log(`🚀 Ready for S3 deployment!`)
    
  } catch (error) {
    console.error('❌ Build failed:', error)
    process.exit(1)
  }
}

// Run the build
buildStatic()
