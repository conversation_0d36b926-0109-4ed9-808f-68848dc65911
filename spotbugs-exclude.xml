<?xml version="1.0" encoding="UTF-8"?>
<FindBugsFilter>
    <!-- Exclude generated files -->
    <Match>
        <Package name="~app\.com\..*"/>
    </Match>
    <Match>
        <Package name="~app\.org\..*"/>
    </Match>
    <Match>
        <Package name="~org\.tempuri\..*"/>
    </Match>

    <!-- Exclude test files from certain checks -->
    <Match>
        <Package name="~.*\.test\..*"/>
        <Bug pattern="DM_EXIT"/>
    </Match>

    <!-- Play Framework specific exclusions -->
    <Match>
        <Class name="~.*Controller"/>
        <Bug pattern="UWF_UNWRITTEN_FIELD"/>
    </Match>

    <!-- JPA Entity specific exclusions -->
    <Match>
        <Package name="~models\..*"/>
        <Bug pattern="EI_EXPOSE_REP,EI_EXPOSE_REP2,UWF_UNWRITTEN_FIELD"/>
    </Match>

    <!-- Serializer exclusions -->
    <Match>
        <Package name="~.*serializers\..*"/>
        <Bug pattern="UWF_UNWRITTEN_FIELD,EI_EXPOSE_REP"/>
    </Match>

    <!-- DTO exclusions -->
    <Match>
        <Package name="~.*dto\..*"/>
        <Bug pattern="UWF_UNWRITTEN_FIELD,EI_EXPOSE_REP,EI_EXPOSE_REP2"/>
    </Match>
</FindBugsFilter>
