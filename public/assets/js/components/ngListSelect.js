;(function (window, angular, undefined) {
  'use strict'
  angular.module('ngListSelect', []).directive('ngListSelect', [
    '$timeout',
    function () {
      return {
        restrict: 'E',
        scope: {
          key: '@key',
          sourceList: '=source',
          sourceLabel: '@sourceLabel',
          targetLists: '=targets',
          onSave: '=onSave',
          height: '@height',
          width: '@width',
        },
        templateUrl: 'public/assets/js/components/ngListSelect.html',
        link: function (scope, elem, attrs) {
          scope.$watchCollection(
            'targetLists',
            function (newCollection, oldCollection) {
              if (newCollection) {
                scope.targetLists = newCollection
                scope.buildTarget(newCollection)
              }
            }
          )

          elem.on('$destroy', function () {
            scope.targetLists = []
          })
        },
        controller: function ($scope) {
          $scope.height = angular.isUndefined($scope.height)
            ? '144px'
            : $scope.height
          $scope.width = angular.isUndefined($scope.width)
            ? '640px'
            : $scope.width
          $scope.dropdownStyle = {
            height: $scope.height,
          }
          $scope.containerStyle = {
            width: $scope.width,
          }
          $scope.targetListSelectedItems = []
          $scope.targetListOptions = []

          $scope.sourceListOption = `item as item.label for item in sourceList | orderBy: item.id`

          $scope.buildTarget = function (targetLists) {
            for (let i = 0; i < targetLists.length; i++) {
              const target = targetLists[i]
              $scope[`targetList_${target.key}`] = target.list
              $scope.targetListSelectedItems[target.key] = []
              $scope.targetListOptions[
                target.key
              ] = `item as item.label for item in targetList_${target.key} | orderBy: item.id`
            }
          }

          $scope.addItemsToTarget = function (targetKey) {
            for (let i = 0; i < $scope.sourceListSelectedItem.length; i++) {
              const sourceSelectedItem = $scope.sourceListSelectedItem[i]
              const foundIndex = $scope.sourceList.findIndex(
                (item) => item.id === sourceSelectedItem.id
              )
              $scope[`targetList_${targetKey}`].push(sourceSelectedItem)
              $scope.sourceList.splice(foundIndex, 1)
            }

            $scope.sourceListSelectedItem = []
          }

          $scope.addItemsToSource = function (targetKey) {
            for (
              let i = 0;
              i < $scope.targetListSelectedItems[targetKey].length;
              i++
            ) {
              const targetSelectedItem =
                $scope.targetListSelectedItems[targetKey][i]
              const foundIndex = $scope[`targetList_${targetKey}`].findIndex(
                (item) => item.id === targetSelectedItem.id
              )
              $scope.sourceList.push(targetSelectedItem)
              $scope[`targetList_${targetKey}`].splice(foundIndex, 1)
            }
            $scope.targetListSelectedItems[targetKey] = []
          }

          $scope.save = function () {
            const allTargetLists = []
            for (let i = 0; i < $scope.targetLists.length; i++) {
              const target = $scope.targetLists[i]
              allTargetLists.push({
                key: target.key,
                list: $scope[`targetList_${target.key}`],
              })
            }
            $scope.onSave($scope.sourceList, allTargetLists)
          }
        },
      }
    },
  ])
})(window, window.angular)
