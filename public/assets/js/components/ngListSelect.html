<div class="ngListSelect container" ng-style="containerStyle">
  <button
    class="btn btn-success"
    ng-click="save()"
    style="
      width: 60px;
      height: 34px;
      line-height: 17px;
      vertical-align: middle;
      outline: none !important;
      margin-bottom: 0px;
    "
  >
    Guardar
  </button>
  <div
    style="display: flex; justify-content: space-between; align-items: stretch"
  >
    <div class="panel panel-primary" style="width: 250px">
      <div class="panel-heading panel-primary">
        <b><span ng-bind="sourceLabel"></span></b>
      </div>
      <select
        size="9"
        style="width: 100%; vertical-align: top; color: black"
        ng-model="sourceListSelectedItem"
        ng-options="{{sourceListOption}}"
        multiple
      >
        <option value="" selected hidden></option>
      </select>
    </div>
    <div style="display: flex; flex-direction: column">
      <div
        ng-repeat="target in targetLists"
        style="display: flex; justify-content: center; text-align: center"
      >
        <div
          style="display: inline-block; width: 20%; text-align: center"
          class="p-r-10"
        >
          <br /><br />
          <div class="btn-group-vertical">
            <button
              class="btn btn-primary"
              ng-click="addItemsToTarget(target.key)"
              style="
                width: 60px;
                height: 34px;
                line-height: 17px;
                vertical-align: middle;
                outline: none !important;
                margin-bottom: 12px;
              "
            >
              <i class="glyphicon glyphicon-triangle-right"></i>
            </button>
            <button
              class="btn btn-primary"
              ng-click="addItemsToSource(target.key)"
              style="
                width: 60px;
                height: 34px;
                line-height: 17px;
                vertical-align: middle;
                outline: none !important;
                margin-bottom: 0px;
              "
            >
              <i class="glyphicon glyphicon-triangle-left"></i>
            </button>
          </div>
        </div>
        <div style="width: 250px">
          <div class="panel panel-primary">
            <div class="panel-heading panel-primary">
              <b><span ng-bind="target.label"></span></b>
            </div>
            <select
              size="9"
              ng-style="dropdownStyle"
              style="width: 100%; vertical-align: top; color: black"
              id="{{target.key}}"
              ng-model="targetListSelectedItems[target.key]"
              ng-options="{{targetListOptions[target.key]}}"
              multiple
            >
              <option value="" selected hidden></option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
