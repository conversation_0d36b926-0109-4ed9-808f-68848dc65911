// routes
app
  .config([
    '$routeProvider',
    function ($routeProvider) {
      var originalWhen = $routeProvider.when

      $routeProvider.when = function (path, route) {
        route.resolve = angular.extend(route.resolve ? route.resolve : {}, {
          init: [
            'AccountService',
            'AuthService',
            function (AccountService, auth) {
              return auth.isAuthorized() ? AccountService.getAccount() : null
            },
          ],
        })

        return originalWhen.call($routeProvider, path, route)
      }

      $routeProvider
        .when('/', {
          templateUrl: 'assets/tpl/dashboard.html',
        })
        .when('/access/:tpl', {
          templateUrl: function (attr) {
            return 'access/' + attr.tpl + '.html'
          },
        })
        .when('/buildings/:bid', {
          templateUrl: function (attr) {
            return 'assets/tpl/building-detail.html'
          },
          resolve: {
            building: [
              '$route',
              'BuildingService',
              function ($route, bs) {
                var bid = $route.current.params.bid
                return bs.getBuildingById(bid)
              },
            ],
          },
          controller: 'BuildingDetailController',
        })
        .when('/:folder/:tpl', {
          templateUrl: function (attr) {
            return 'assets/tpl/' + attr.folder + '/' + attr.tpl + '.html'
          },
        })
        .when('/:tpl', {
          templateUrl: function (attr) {
            return 'assets/tpl/' + attr.tpl + '.html'
          },
        })
        .when('/maintenance/preventive/:bid', {
          templateUrl: function (attr) {
            return 'assets/tpl/maintenance/preventive-details.html'
          },
        })
        .otherwise({ redirectTo: '/' })
    },
  ])

  // google maps
  .config([
    'uiGmapGoogleMapApiProvider',
    function (uiGmapGoogleMapApiProvider) {
      uiGmapGoogleMapApiProvider.configure({
        //    key: 'your api key',
        v: '3.17',
        libraries: 'weather,geometry,visualization',
      })
    },
  ])

  // loading bar settings
  .config([
    'cfpLoadingBarProvider',
    function (cfpLoadingBarProvider) {
      cfpLoadingBarProvider.includeSpinner = false
      cfpLoadingBarProvider.latencyThreshold = 300
    },
  ])

  // defaults for date picker
  .config([
    '$datepickerProvider',
    function ($datepickerProvider) {
      angular.extend($datepickerProvider.defaults, {
        dateFormat: 'dd/MM/yyyy',
        iconLeft: 'md md-chevron-left',
        iconRight: 'md md-chevron-right',
        autoclose: true,
      })
    },
  ])

  // defaults for date picker
  .config([
    '$timepickerProvider',
    function ($timepickerProvider) {
      angular.extend($timepickerProvider.defaults, {
        timeFormat: 'HH:mm',
        iconUp: 'md md-expand-less',
        iconDown: 'md md-expand-more',
        hourStep: 1,
        minuteStep: 1,
        arrowBehavior: 'picker',
        modelTimeFormat: 'HH:mm',
      })
    },
  ])

  // disable nganimate with adding class
  .config([
    '$animateProvider',
    function ($animateProvider) {
      $animateProvider.classNameFilter(/^(?:(?!ng-animate-disabled).)*$/)
    },
  ])

  // set constants
  .run([
    '$rootScope',
    '$location',
    'APP',
    'AuthService',
    function ($rootScope, $location, APP, auth) {
      $rootScope.APP = APP
      //register listener to watch route changes
      $rootScope.$on('$routeChangeStart', function (event, next, current) {
        if (!auth.isAuthorized()) {
          // no logged user, we should be going to #login
          if (
            next.pathParams.tpl == 'login' ||
            next.pathParams.tpl == 'signup' ||
            next.pathParams.tpl == 'forgot'
          ) {
            // already going to #login, no redirect needed
          } else {
            $location.path('/access/login')
          }
        }
      })
    },
  ])
