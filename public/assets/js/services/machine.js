app.service('MachineService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.machines = []
    this.cards = []

    $rootScope.$on('loggedUser:changed', function () {
      self.cards = []
    })

    this.loadMachines = function () {
      return api.getParts('MACHINE').then(function (data) {
        self.machines = data.data.machines.sortBy('serial_number')
      })
    }

    this.findMachinesWithDifferentRatesByBuildingId = function (buildingId) {
      return api
        .findMachinesWithDifferentRatesByBuildingId(buildingId)
        .then(function (data) {
          self.machines = data.data.machines
        })
    }

    this.findMachines = function (serialNumber) {
      var promise = api.getMachines(serialNumber)

      promise.then(function (data) {
        self.machines = data.data.machines
      })

      return promise
    }

    this.loadCards = function () {
      return api.getParts('CARD').then(function (data) {
        self.cards = data.data.cards.sortBy('uuid')
      })
    }

    this.getMachines = function () {
      return self.machines
    }

    this.getCards = function () {
      return self.cards
    }

    this.getMachineHistory = function (machineId) {
      return api.get(`/machine/${machineId}/history`)
    }
  },
])
