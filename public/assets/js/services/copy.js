app.service('CopyService', function () {
  this.copy = (value) => {
    const fallbackCopyTextToClipboard = (text) => {
      const dummy = document.createElement('input')
      document.body.appendChild(dummy)
      dummy.value = text

      // Avoid scrolling to top
      dummy.style.top = '0'
      dummy.style.left = '0'
      dummy.style.position = 'fixed'
      dummy.focus()
      dummy.select()

      document.execCommand('copy')
      document.body.removeChild(dummy)
    }

    if (navigator?.clipboard) {
      return navigator.clipboard.writeText(value)
    }

    return new Promise((resolve, reject) => {
      try {
        fallbackCopyTextToClipboard(value)
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }
})
