app.service('BillingService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this
    this.billResults = []

    $rootScope.$on('loggedUser:changed', function () {
      self.billResults = []
    })

    this.findBills = function (filters) {
      return api.findBills(filters).then(function (data) {
        self.billResults = data.data.bills
      })
    }

    this.findBillsByCard = function (filters) {
      return api.findBillsByCard(filters).then(function (data) {
        self.billResults = data.data.bills
      })
    }

    this.getBillResults = function () {
      return self.billResults
    }

    this.billBuilding = function (filters) {
      return api.billBuilding(
        filters.building.id,
        filters.from,
        filters.to,
        filters.onlyCardEvents
      )
    }

    this.getBillDetails = function (bill) {
      return api.billDetail(bill.id)
    }

    this.getBillItemMeasures = function () {
      return api.getBillItemMeasures()
    }

    this.getBillPDF = function (bill) {
      return api.getBillPDF(bill.id)
    }

    this.cancelBill = function (bill, type, reason, discreditedItems) {
      return api.cancelBill(bill.id, type, reason, discreditedItems)
    }

    this.updateBillCollectionStatus = function (bill, status, reference) {
      return api.updateBillCollectionStatus(bill, status, reference)
    }

    this.promoteBillCollectionStatus = function (bid) {
      return api.promoteBillCollectionStatus(bid)
    }

    this.demoteBillCollectionStatus = function (bid) {
      return api.demoteBillCollectionStatus(bid)
    }

    this.assignBillDebtCollector = function (bid, user, date) {
      return api.assignBillDebtCollector(bid, user, date)
    }

    this.unassignBillDebtCollector = function (bid) {
      return api.unassignBillDebtCollector(bid)
    }

    this.getBillShortDetails = function (bill) {
      return api.billShortDetail(bill.id)
    }

    this.bill = function (bill) {
      return api.bill(bill)
    }

    this.loadTypes = function () {
      return api.getBillTypes().then(function (data) {
        self.billTypes = data.data.sortBy()
      })
    }

    this.getTypes = function () {
      return self.billTypes
    }

    this.getModes = function () {
      return self.modes
    }

    this.loadCollectors = function () {
      return api.loadCollectors().then(function (data) {
        self.collectors = data.data.users
      })
    }

    this.getCollectors = function () {
      return self.collectors
    }

    this.exportToExcel = function (filters) {
      return api.findBills(filters, true)
    }
  },
])
