app.service('TinyUrlService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    this.getTinyUrls = (level) => api.getTinyUrls(level)

    this.createTinyUrl = (tinyUrl) => api.createTinyUrl(tinyUrl)

    this.createBulkTinyUrls = (count) => api.createBulkTinyUrls(count)

    this.updateTinyUrl = (id, tinyUrl) => api.updateTinyUrl(id, tinyUrl)

    this.assignTinyUrlToMachine = (token, machine) =>
      api.assignTinyUrlToMachine(token, machine)
    this.unassignTinyUrlFromMachine = (token, machine) =>
      api.unassignTinyUrlFromMachine(token, machine)
  },
])
