app.service('AccountService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this
    const oneHour = 60 * 60000

    this.ROLES = {
      MASTER: 'MASTER',
      ASSISTANT: 'ASSISTANT',
      TECHNICIAN: 'TECHNICIAN',
      SUPERVISOR: 'SUPERVISOR',
    }

    this.account = undefined

    this.accountSettings = {}
    this.dashboardData = {}
    this.loadingDashboardData = undefined
    this.dashboardDataUpdatedAt = new Date().getTime() - oneHour - 10

    $rootScope.$on('loggedUser:changed', function () {
      self.account = undefined
      self.accountSettings = {}
      self.dashboardData = {}
    })

    $rootScope.$on('sessionUser:loaded', function (event, args) {
      if (
        self.account &&
        (!self.dashboardData ||
          jQuery.isEmptyObject(self.dashboardData) ||
          self.dashboardDataUpdatedAt + oneHour < new Date().getTime())
      ) {
        self.getDashboardData()
      }
    })

    this.getAccount = function () {
      var deferred = $q.defer()
      if (self.account !== undefined) {
        deferred.resolve(self.account)
        $rootScope.$broadcast('sessionUser:loaded', null)
      } else {
        self.loadAccount().then(function (data) {
          deferred.resolve(self.account)
        })
      }

      return deferred.promise
    }

    const anyRole = (...roles) => {
      if (!this.account || !roles) return false

      return roles.includes(this.account.user.role)
    }

    this.loadAccount = function () {
      return api
        .getAccount()
        .success(function (data) {
          self.account = data.account
          self.account.anyRole = anyRole

          $rootScope.$broadcast('sessionUser:loaded', null)
        })
        .error(function () {
          auth.logout('Su sesión ha expirado')
        })
    }

    this.getDashboardData = function () {
      if (!self.loadingDashboardData) {
        self.loadingDashboardData = api
          .getDashboardData(self.account.id)
          .then(function (data) {
            self.dashboardData = data.data
            self.dashboardDataUpdatedAt = new Date().getTime()
          })
      }

      return self.loadingDashboardData
    }
  },
])
