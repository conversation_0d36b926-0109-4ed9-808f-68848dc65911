app.service('AdministrationService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.administrations = []

    $rootScope.$on('loggedUser:changed', function () {
      self.administrations = []
    })

    this.createAdministration = function (data) {
      return api.createAdministration(data)
    }

    this.updateAdministration = function (aid, data) {
      return api.updateAdministration(aid, data)
    }

    this.deleteAdministration = function (aid) {
      return api.deleteAdministration(aid)
    }

    this.loadAdministrations = function (level) {
      return api.getAdministrations(level).then(function (data) {
        self.administrations = data.data.administrations.sortBy('name')
      })
    }

    this.getAdministrations = function () {
      return self.administrations
    }

    this.assignBillDebtCollector = function (bid, user, date) {
      return api.assignAdminDebtCollector(bid, user, date)
    }

    this.unassignBillDebtCollector = function (bid) {
      return api.unassignAdminDebtCollector(bid)
    }

    this.sendRefreshTokenReminder = function (aid) {
      return api.sendRefreshTokenReminder(aid)
    }

    this.runClosure = function (administrationId) {
      return api.runAdministrationClosure(administrationId)
    }
  },
])
