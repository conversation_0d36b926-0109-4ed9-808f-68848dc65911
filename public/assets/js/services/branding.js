app.service('BrandingService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    this.loadBrandingItems = (level) => api.getBrandingItems(level)

    this.loadRequestedItems = function () {
      return api.getBrandingItemRequests()
    }

    this.deliverRequestedItem = function (id) {
      return api.deliverBrandingItemRequest(id)
    }

    this.createItem = (item) => api.createBrandingItem(item)

    this.updateItem = (id, item) => api.updateBrandingItem(id, item)

    this.deleteItem = (id) => api.deleteBrandingItem(id)
  },
])
