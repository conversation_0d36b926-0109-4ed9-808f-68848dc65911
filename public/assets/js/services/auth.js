function AuthService($rootScope, $q, $http, $location) {
  var STORAGE_ID = 'lm-app-201510'

  var self = this

  this.authorized = false

  self.isAuthorized = function () {
    var auth_data = self.getAuthData()
    return auth_data !== undefined && auth_data !== null && auth_data !== ''
  }

  self.authorize = function (user, pass) {
    var deferred = $q.defer()
    $http
      .post('/api/v1/signin', { emailAddress: user, password: pass })
      .success(function (data) {
        self.saveAuthData(data)
        $rootScope.authData = data
        $rootScope.menuItems = null
        $rootScope.$broadcast('loggedUser:changed', data)
        deferred.resolve(data)
      })
      .error(function (reason) {
        deferred.reject(reason)
      })
    return deferred.promise
  }

  self.forgot = function (email) {
    var deferred = $q.defer()
    $http
      .post('/api/v1/resetPassword', { email })
      .success(function () {
        deferred.resolve()
      })
      .error(function (reason) {
        deferred.reject(reason)
      })
    return deferred.promise
  }

  this.getAuthData = function () {
    var data = localStorage.getItem(STORAGE_ID) || ''
    if (data !== '') $rootScope.authData = JSON.parse(data)
    return $rootScope.authData
  }

  this.saveAuthData = function (data) {
    localStorage.setItem(STORAGE_ID, JSON.stringify(data))
    if (data !== '') $rootScope.authData = data
    $rootScope.$broadcast('loggedUser:changed', null)
  }

  this.logout = function (message) {
    var deferred = $q.defer()

    $http
      .post(
        '/api/v1/signout',
        {},
        {
          headers: {
            'X-LM-AUTH-TOKEN': $rootScope.authData
              ? $rootScope.authData.token
              : '',
          },
        }
      )
      .success(function () {
        localStorage.removeItem(STORAGE_ID)
        $rootScope.authData = null
        $rootScope.$broadcast('loggedUser:changed', null)
        $location.path('/access/login')
        deferred.resolve()
      })
      .error(function (reason) {
        deferred.reject(reason)
      })

    return deferred.promise
  }
}

app.service('AuthService', [
  '$rootScope',
  '$q',
  '$http',
  '$location',
  AuthService,
])
