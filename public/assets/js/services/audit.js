app.service('AuditService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this
    this.auditResults = []

    $rootScope.$on('loggedUser:changed', function () {
      self.auditResults = []
    })

    this.findAudits = function (filters) {
      return api.findAudits(filters).then(function (data) {
        self.auditResults = data.data.audits
      })
    }

    this.getAuditResults = function () {
      return self.auditResults
    }
  },
])
