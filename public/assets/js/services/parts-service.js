app.service('PartsService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.parts = []
    this.soapDispensers = []

    $rootScope.$on('loggedUser:changed', function () {
      self.users = []
    })

    this.updateCardBalance = function (transaction) {
      return api.updateCardBalance(transaction)
    }

    this.createPart = function (data, partType) {
      return api.createPart(data, partType)
    }

    this.updatePart = function (bid, data) {
      return api.updatePart(bid, data)
    }

    this.deletePart = function (bid) {
      return api.deletePart(bid)
    }

    this.loadParts = function (type, filters) {
      var promise = api.getParts(type, filters)

      promise.then(function (data) {
        self.parts = data.data.parts
      })

      return promise
    }

    this.getParts = function () {
      return self.parts
    }

    //SOAP DISPENSER

    this.createSoapDispenser = function (data) {
      return api.createSoapDispenser(data)
    }

    this.updateSoapDispenser = function (bid, data) {
      return api.updateSoapDispenser(bid, data)
    }

    this.replenishSoapDispenserUses = function (dispenserId, data) {
      return api.replenishSoapDispenserUses(dispenserId, data)
    }

    this.deleteSoapDispenser = function (bid) {
      return api.deleteSoapDispenser(bid)
    }

    this.loadSoapDispensers = function (type, filters) {
      var promise = api.getSoapDispensers(type, filters)

      promise.then(function (data) {
        self.soapDispensers = data.data.dispensers
      })

      return promise
    }

    this.getSoapDispensers = function () {
      return self.soapDispensers
    }

    // RPI PARENT
    this.assignRPIChild = function (machineId, rpiChildMachineId) {
      return api.assignRPIChild(machineId, rpiChildMachineId)
    }

    this.unassignRPIChild = function (machineId) {
      return api.unassignRPIChild(machineId)
    }

    // MACHINES
    this.zeroingPendingUses = function (machineId) {
      return api.zeroingPendingUses(machineId)
    }
  },
])
