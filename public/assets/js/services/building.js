app.service('BuildingService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.buildings = []
    this.rates = []
    this.maintenances = []
    this.units = []
    this.machines = []

    $rootScope.$on('loggedUser:changed', function () {
      self.users = []
    })

    this.createBuilding = function (data) {
      return api.createBuilding(data)
    }

    this.updateBuilding = function (bid, building) {
      // reducing payload
      const { machines, units, ...data } = building

      return api.updateBuilding(bid, data)
    }

    this.deleteBuilding = function (bid) {
      return api.deleteBuilding(bid)
    }

    this.loadBuildings = function (level) {
      return api.getBuildings(level).then(function (data) {
        var buildings = data.data.buildings.sortBy('name')
        for (let index = 0; index < buildings.length; index++) {
          if (buildings[index].units)
            buildings[index].units = buildings[index].units.sortBy(
              'tower',
              'number',
              true
            )
        }

        self.buildings = buildings
      })
    }

    this.createUnit = function (bid, unit) {
      return api.createUnit(bid, unit)
    }

    this.updateUnit = function (bid, uid, unit) {
      return api.updateUnit(bid, uid, unit)
    }

    this.deleteUnit = function (bid, uid) {
      return api.deleteUnit(bid, uid)
    }

    this.getBuildings = function () {
      return self.buildings
    }

    this.getBuildingById = function (bid) {
      return api.getBuildingById(bid)
    }

    this.assignMachine = function (bid, mid) {
      return api.assignMachine(bid, mid)
    }

    this.unassignMachine = function (bid, mid) {
      return api.unassignMachine(bid, mid)
    }

    this.assignCard = function (bid, uid, cid, billable) {
      return api.assignCard(bid, uid, cid, billable)
    }

    this.unassignCard = function (bid, uid, cid) {
      return api.unassignCard(bid, uid, cid)
    }

    this.cardAction = function (bid, uid, cid, action) {
      return api.cardAction(bid, uid, cid, action)
    }

    this.rechargePrepaidUses = function (bid) {
      return api.rechargePrepaidUses(bid)
    }

    this.uploadingPayment = function (file) {
      return api.uploadingPayment(file)
    }

    this.uploadingUses = function (file, buildingId, userName) {
      return api.uploadingUses(file, buildingId, userName)
    }

    this.uploadBuildingFile = function (file, buildingId, userName) {
      return api.uploadBuildingFile(file, buildingId, userName)
    }

    this.enableAdministrationClosurePermission = function (buildingId) {
      return api.enableAdministrationClosurePermission(buildingId)
    }

    this.disableAdministrationClosurePermission = function (buildingId) {
      return api.disableAdministrationClosurePermission(buildingId)
    }

    this.enableForMaintenance = function (buildingId) {
      return api.enableBuildingForMaintenance(buildingId)
    }

    this.disableForMaintenance = function (buildingId) {
      return api.disableBuildingForMaintenance(buildingId)
    }

    this.editBuildingClosureType = function (buildingId, data) {
      return api.editBuildingClosureType(buildingId, data)
    }

    this.enablePagosWebWithSplit = function (buildingId) {
      return api.enableBuildingPagosWebWithSplit(buildingId)
    }

    this.disablePagosWebWithSplit = function (buildingId) {
      return api.disableBuildingPagosWebWithSplit(buildingId)
    }

    this.enableRedPagosWithSplit = function (buildingId) {
      return api.enableBuildingRedPagosWithSplit(buildingId)
    }

    this.disableRedPagosWithSplit = function (buildingId) {
      return api.disableBuildingRedPagosWithSplit(buildingId)
    }

    /*
     * Rates
     */
    this.loadRates = function (level) {
      return api.getRates(level).then(function (data) {
        var rates = data.data.rates.sortBy('name')
        self.rates = rates
      })
    }

    this.getRates = function () {
      return self.rates
    }

    this.createRate = function (data) {
      return api.createRate(data)
    }

    this.updateRate = function (uid, data) {
      return api.updateRate(uid, data)
    }

    this.createRateEvent = function (data) {
      return api.createRateEvent(data)
    }

    this.editRateEvent = function (data) {
      return api.editRateEvent(data)
    }

    this.getRateHistory = function (id) {
      return api.getRateHistory(id)
    }

    this.deleteRateHistory = function (rateId, id) {
      return api.deleteRateHistory(rateId, id)
    }

    /**
     * Maintenance
     */
    this.loadMaintenances = function (id) {
      return api.getBuildingMaintenances(id).then(function (data) {
        self.maintenances = data.data.maintenances
      })
    }

    this.getMaintenances = function () {
      return self.maintenances
    }

    /**
     * Units
     */
    this.loadUnits = function (id) {
      return api.getBuildingUnits(id).then(function (data) {
        self.units = data.data.units
      })
    }

    this.getUnits = function () {
      return self.units
    }

    /**
     * Machines
     */
    this.loadMachines = function (id) {
      return api.getBuildingMachines(id).then(function (data) {
        self.machines = data.data.machines
      })
    }

    this.getMachines = function () {
      return self.machines
    }

    this.loadBuildingFirstUse = function (buildingId) {
      return api.get('/buildings/' + buildingId + '/first-use')
    }

    this.loadBuildingOldestKeepAlive = function (buildingId) {
      return api.get('/buildings/' + buildingId + '/oldest-keep-alive')
    }
  },
])
