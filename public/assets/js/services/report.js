app.service('ReportService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.useResults = []

    $rootScope.$on('loggedUser:changed', function () {
      self.useResults = []
      self.oldestKeepAlive = null
      self.serialNumber = null
    })

    this.findUses = function (filters) {
      return api.findUses(filters).then(function (data) {
        self.useResults = data.data.items.map((item) => {
          return {
            ...item,
            unit: item.card
              ? item.card.unit_tower + '-' + item.card.unit_number
              : '',
            typeDesc: item.machine
              ? item.machine.machine_type == 'WASHER'
                ? 'Lavado'
                : 'Secado'
              : '',
          }
        })
        self.oldestKeepAlive = data.data.oldestKeepAlive
        self.serialNumber = data.data.serialNumber
      })
    }

    this.getUseResults = function () {
      return this.useResults
    }

    this.getOldestKeepAlive = function () {
      return this.oldestKeepAlive
    }

    this.getSerialNumber = function () {
      return this.serialNumber
    }

    this.exportToExcel = function (filters) {
      return api.findUses(filters, true)
    }

    this.accreditUse = function (useId) {
      return api.accreditUse(useId)
    }

    this.discreditUse = function (useId, reason) {
      return api.discreditUse(useId, reason)
    }

    this.getDiscreditedUses = function (bill) {
      return api.getDiscreditedUses(bill.id)
    }
  },
])
