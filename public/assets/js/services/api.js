function LMAPI($rootScope, $http, Upload) {
  var self = this
  var API_VERSION = 'v1'
  const baseURL = `api/${API_VERSION}`

  this.headers = function () {
    return {
      'X-LM-AUTH-TOKEN': $rootScope.authData ? $rootScope.authData.token : '',
    }
  }

  this.apiCall = function (endpoint, method, data) {
    var url = '/' + baseURL + endpoint

    var config = {
      headers: this.headers(),
      timeout: 240000,
    }

    if (method == 'GET') return $http.get(url, config)
    if (method == 'DELETE') return $http['delete'](url, config)
    if (method == 'PUT') return $http.put(url, data, config)
    if (method == 'PATCH') return $http.patch(url, data, config)
    if (method == 'POST') return $http.post(url, data, config)
  }

  this.get = function (endpoint, data) {
    return this.apiCall(endpoint, 'GET', data)
  }

  this.delete = function (endpoint, data) {
    return this.apiCall(endpoint, 'DELETE', data)
  }

  this.post = function (endpoint, data) {
    return this.apiCall(endpoint, 'POST', data)
  }

  this.put = function (endpoint, data) {
    return this.apiCall(endpoint, 'PUT', data)
  }

  this.patch = function (endpoint, data) {
    return this.apiCall(endpoint, 'PATCH', data)
  }

  this.apiCallBinary = function (endpoint, method, data) {
    var url = '/' + baseURL + endpoint

    var config = {
      headers: this.headers(),
      timeout: 240000,
      responseType: 'arraybuffer',
    }

    if (method == 'GET') return $http.get(url, config)
    if (method == 'DELETE') return $http['delete'](url, config)
    if (method == 'PUT') return $http.put(url, data, config)
    if (method == 'PATCH') return $http.patch(url, data, config)
    if (method == 'POST') return $http.post(url, data, config)
  }

  this.queryLevel = (level, { prefix = '' } = {}) =>
    typeof level === 'number' ? `${prefix}level=${level}` : ''

  this.getAccount = function () {
    return self.get('/account')
  }

  /**
   * Reports
   */
  this.findBills = function (filters, xls) {
    var query = ''

    if (filters.building) {
      query += '&building=' + filters.building.id
    }

    if (filters.administration) {
      query += '&administration=' + filters.administration.id
    }

    if (filters.from) {
      query += '&from=' + filters.from.getTime() / 1000
    }

    if (filters.to) {
      query += '&to=' + filters.to.getTime() / 1000
    }

    if (filters.type) {
      query += '&type=' + filters.type
    }

    if (filters.mode) {
      query += '&mode=' + filters.mode
    }

    if (filters.dgiNumber) {
      query += '&dgiNumber=' + filters.dgiNumber
    }

    if (filters.collector) {
      query += '&collector=' + filters.collector
    }

    if (filters.collectionStatus) {
      query += '&collectionStatus=' + filters.collectionStatus
    }

    if (filters.state) {
      query += '&state=' + filters.state
    }

    if (filters.billOrigin) {
      query += '&billOrigin=' + filters.billOrigin
    }

    if (filters.order != null) {
      query += '&order=' + filters.order
    }

    if (filters.direction != null) {
      query += '&direction=' + filters.direction
    }

    if (filters.page && filters.perPage) {
      query += '&perPage=' + filters.perPage + '&page=' + filters.page
    } else {
      query += '&perPage=500&page=1'
    }

    if (query.length > 0) {
      query = query.substr(1)
    }

    if (xls) {
      return self.apiCallBinary(`/reports/bills/xls?${query}`, 'GET')
    } else {
      return self.get(`/bills?${query}`)
    }
  }

  this.findBillsByCard = function (filters) {
    let query = ''

    if (filters.limit) {
      query += '&limit=' + filters.limit
    }

    return self.get(`/cards/${filters.cardId}/bills?${query}`)
  }

  this.findUses = function (filters, xls) {
    var query = ''

    if (filters.from) {
      const userTimezoneOffset = filters.from.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(filters.from.getTime() - userTimezoneOffset)
      query += '&from=' + dateUTCFixed.toISOString()
    }

    if (filters.to) {
      const userTimezoneOffset = filters.to.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(filters.to.getTime() - userTimezoneOffset)
      query += '&to=' + dateUTCFixed.toISOString()
    }

    if (filters.daysOfWeek?.length > 0) {
      query += filters.daysOfWeek.map((dayOfWeek) => '&daysOfWeek=' + dayOfWeek)
    }

    if (filters.building?.id) {
      query += '&building=' + filters.building.id
    }

    if (filters.machine) {
      query += '&machine=' + filters.machine.id
    } else if (filters.machines?.length > 0) {
      query += filters.machines
        .map((machine) => '&machines=' + machine.id)
        .join('')
    }

    if (filters.card) {
      query += '&card=' + filters.card
    }

    if (filters.unit) {
      query += '&unit=' + filters.unit.id
    } else if (filters.units?.length > 0) {
      query += filters.units.map((unit) => '&unit=' + unit.id).join('')
    }

    if (filters.account) {
      query += '&account=' + filters.account.id
    }

    if (filters.onlyCardEvents) {
      query += '&onlyCardEvents=true'
    }

    if (filters.page && filters.perPage) {
      query += '&perPage=' + filters.perPage + '&page=' + filters.page
    } else {
      query += '&perPage=50&page=1'
    }

    if (query.length > 0) {
      query = query.substr(1)
    }

    if (xls) {
      return self.apiCallBinary(
        '/reports/uses/' + filters.building.id + '/xls?' + query,
        'GET'
      )
    }

    return self.get('/reports/uses?' + query)
  }

  this.accreditUse = function (useId) {
    return self.get('/reports/uses/' + useId + '/accredit')
  }

  this.discreditUse = function (useId, reason) {
    return self.get('/reports/uses/' + useId + '/discredit?reason=' + reason)
  }

  this.getDiscreditedUses = function (billId) {
    return self.get('/reports/uses/' + billId + '/get_discredit')
  }

  this.findMachinesWithDifferentRatesByBuildingId = function (buildingId) {
    return self.get(`/buildings/${buildingId}/machines/different-rates`)
  }

  /**
   * Transaction
   */
  this.findTransactions = function (filters, level) {
    var query = ''

    if (filters.from) {
      const userTimezoneOffset = filters.from.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(filters.from.getTime() - userTimezoneOffset)
      query += '&from=' + Math.floor(dateUTCFixed.getTime() / 1000)
    }

    if (filters.to) {
      const userTimezoneOffset = filters.to.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(filters.to.getTime() - userTimezoneOffset)
      query += '&to=' + Math.floor(dateUTCFixed.getTime() / 1000)
    }

    if (filters.uid) {
      query += '&uid=' + filters.uid
    }

    if (filters.building != null) {
      query += '&buildingId=' + filters.building.id
    }

    if (filters.onlySent != null) {
      query += '&onlySent=' + filters.onlySent
    }

    if (filters.isCSV != null) {
      query += '&isCSV=' + filters.isCSV
    }

    if (filters.order != null) {
      query += '&order=' + filters.order
    }

    if (filters.direction != null) {
      query += '&direction=' + filters.direction
    }

    if (filters.page && filters.perPage) {
      query += '&perPage=' + filters.perPage + '&page=' + filters.page
    } else {
      query += '&perPage=50&page=1'
    }

    if (query.length > 0) {
      query = query.substr(1)
    }

    return self.get(`/transactions?${this.queryLevel(level)}&${query}`)
  }

  this.updateCardBalance = function (transaction) {
    return self.post('/transaction/back-office', transaction)
  }

  /**
   * Audits
   */
  this.findAudits = function (filters) {
    var query = ''

    if (filters.from) {
      query += '&from=' + filters.from.getTime() / 1000
    }

    if (filters.to) {
      query += '&to=' + filters.to.getTime() / 1000
    }

    if (filters.sender) {
      query += '&uid=' + filters.sender
    }

    if (filters.order != null) {
      query += '&order=' + filters.order
    }

    if (filters.direction != null) {
      query += '&direction=' + filters.direction
    }

    if (filters.page && filters.perPage) {
      query += '&perPage=' + filters.perPage + '&page=' + filters.page
    } else {
      query += '&perPage=50&page=1'
    }

    if (query.length > 0) {
      query = query.substr(1)
    }

    return self.get('/audits?' + query)
  }

  /**
   * Dashboard
   */
  this.getDashboardData = function (aid) {
    return self.get('/dashboard')
  }

  /**
   * Administrations
   */
  this.getAdministrations = function (level) {
    return self.get(
      `/administrations${this.queryLevel(level, { prefix: '?' })}`
    )
  }

  this.createAdministration = function (data) {
    return self.post('/administrations', data)
  }

  this.updateAdministration = function (aid, data) {
    return self.put('/administrations/' + aid, data)
  }

  this.deleteAdministration = function (aid) {
    return self.delete('/administrations/' + aid)
  }

  this.assignAdminDebtCollector = function (aid, user, date) {
    return self.put(`/administrations/${aid}/user/assign/${user}/date/${date}`)
  }

  this.unassignAdminDebtCollector = function (aid) {
    return self.put(`/administrations/${aid}/user/unassign`)
  }

  this.sendRefreshTokenReminder = function (aid) {
    return self.post(`/administrations/${aid}/sendRefreshTokenReminder`)
  }

  this.runAdministrationClosure = function (administrationId) {
    return self.get(`/administrations/${administrationId}/close`)
  }

  /**
   * Users
   */
  this.getUsers = function (filters) {
    const query = new URLSearchParams(filters)

    return self.get('/users?' + query.toString())
  }

  this.getTechnicians = function () {
    return self.get('/users/technicians')
  }

  this.createUser = function (data) {
    return self.post('/users', data)
  }

  this.updateUser = function (aid, data) {
    return self.put('/users/' + aid, data)
  }

  this.deleteUser = function (aid) {
    return self.delete('/users/' + aid)
  }

  this.resendAccountValidationEmail = function (accountId) {
    return self.post(`/resendValidationEmail/${accountId}`)
  }

  /**
   * Buildings
   */
  this.getBuildings = function (level) {
    return self.get(`/buildings${this.queryLevel(level, { prefix: '?' })}`)
  }

  this.getBuildingById = function (bid) {
    return self.get('/buildings/' + bid)
  }

  this.createBuilding = function (data) {
    return self.post('/buildings', data)
  }

  this.updateBuilding = function (bid, data) {
    return self.put('/buildings/' + bid, data)
  }

  this.deleteBuilding = function (bid) {
    return self.delete('/buildings/' + bid)
  }

  this.createUnit = function (bid, unit) {
    return self.post(`/buildings/${bid}/units`, unit)
  }

  this.updateUnit = function (bid, uid, unit) {
    return self.put(`/buildings/${bid}/units/${uid}`, unit)
  }

  this.deleteUnit = function (bid, uid) {
    return self.delete(`/buildings/${bid}/units/${uid}`)
  }

  this.assignMachine = function (bid, mid) {
    return self.put(`/buildings/${bid}/machines/${mid}`)
  }

  this.unassignMachine = function (bid, mid) {
    return self.delete(`/buildings/${bid}/machines/${mid}`)
  }

  this.assignCard = function (bid, uid, cid, billable) {
    return self.put(
      `/buildings/${bid}/units/${uid}/cards/${cid}${
        billable ? '?billable=true' : ''
      }`
    )
  }

  this.unassignCard = function (bid, uid, cid) {
    return self.delete(`/buildings/${bid}/units/${uid}/cards/${cid}`)
  }

  this.cardAction = function (bid, uid, cid, action) {
    return self.get(`/buildings/${bid}/units/${uid}/cards/${cid}/${action}`)
  }

  this.rechargePrepaidUses = function (bid) {
    return self.put(`/buildings/${bid}/rechargePrepaidUnitsBalance`)
  }

  this.uploadingPayment = function (file) {
    file.upload = Upload.upload({
      url: baseURL + '/buildings/upload_payment',
      data: { file },
      headers: this.headers(),
    })

    return file.upload
  }

  this.uploadingUses = function (file, buildingId, userName) {
    file.upload = Upload.upload({
      url: `${baseURL}/buildings/${buildingId}/upload_uses`,
      data: { file, username: userName },
      headers: this.headers(),
    })

    return file.upload
  }

  this.uploadBuildingFile = function (file, buildingId, userName) {
    file.upload = Upload.upload({
      url: `${baseURL}/buildings/${buildingId}/upload-building`,
      data: { file, username: userName },
      headers: this.headers(),
    })

    return file.upload
  }

  this.enableAdministrationClosurePermission = function (buildingId) {
    return self.put(`/buildings/${buildingId}/enable-closure`)
  }

  this.disableAdministrationClosurePermission = function (buildingId) {
    return self.put(`/buildings/${buildingId}/disable-closure`)
  }

  this.enableBuildingForMaintenance = function (buildingId) {
    return self.put(`/buildings/${buildingId}/enable-maintenance`)
  }

  this.disableBuildingForMaintenance = function (buildingId) {
    return self.put(`/buildings/${buildingId}/disable-maintenance`)
  }

  this.editBuildingClosureType = function (buildingId, data) {
    return self.patch(`/buildings/${buildingId}/edit-closure-type`, data)
  }

  this.enableBuildingPagosWebWithSplit = function (buildingId) {
    return self.put(
      `/buildings/${buildingId}/settings/payment-methods/pagos-web-split/enable`
    )
  }

  this.disableBuildingPagosWebWithSplit = function (buildingId) {
    return self.put(
      `/buildings/${buildingId}/settings/payment-methods/pagos-web-split/disable`
    )
  }

  this.enableBuildingRedPagosWithSplit = function (buildingId) {
    return self.put(
      `/buildings/${buildingId}/settings/payment-methods/red-pagos-split/enable`
    )
  }

  this.disableBuildingRedPagosWithSplit = function (buildingId) {
    return self.put(
      `/buildings/${buildingId}/settings/payment-methods/red-pagos-split/disable`
    )
  }

  this.getBuildingUnits = function (buildingId) {
    return self.get(`/buildings/${buildingId}/units`)
  }

  this.getBuildingMachines = function (buildingId) {
    return self.get(`/buildings/${buildingId}/machines`)
  }

  /*
   * Rate
   */

  this.getRates = function (level) {
    return self.get(`/rates${this.queryLevel(level, { prefix: '?' })}`)
  }

  this.createRate = function (data) {
    return self.post('/rates', data)
  }

  this.updateRate = function (id, data) {
    return self.put('/rates/' + id, data)
  }

  this.deleteRate = function (id) {
    return self.delete('/rates/' + id)
  }

  this.createRateEvent = function (data) {
    return self.post(`/rates/${data?.rate?.id || 0}/rateEvents`, data)
  }

  this.editRateEvent = function (data) {
    return self.put(`/rates/${data?.rate?.id || 0}/rateEvents/${data.id}`, data)
  }

  this.getRateHistory = function (id) {
    return self.get(`/rates/${id}/rateEvents`)
  }

  this.deleteRateHistory = function (rateId, id) {
    return self.delete(`/rates/${rateId || 0}/rateEvents/${id}`)
  }

  this.notifyNewRate = function (rateId) {
    return self.post(`/rates/${rateId}/rateEvents/notifyNewRate`)
  }

  /**
   * Parts
   */
  this.getParts = function (partType, filters) {
    var query = ''
    if (filters) {
      if (filters.cardSearch) {
        query += '&cardSearch=' + filters.cardSearch
      }

      if (filters.building) {
        query += '&building=' + filters.building.id
      }

      if (filters.unit) {
        query += '&unit=' + filters.unit.id
      }

      if (filters.machineQuickSearch) {
        query += '&machineSearch=' + filters.machineQuickSearch
      }
    }

    return self.get('/parts?type=' + partType + query)
  }

  const PART_TYPES = {
    TOOL: 'TOOL',
    CARD: 'CARD',
    MACHINE: 'MACHINE',
    REPLACEMENT: 'REPLACEMENT',
  }

  this.createPart = function (data, partType) {
    if (partType === PART_TYPES.TOOL) {
      return self.post(`/tools`, data)
    }

    if (partType === PART_TYPES.REPLACEMENT) {
      return self.post(`/replacements`, data)
    }

    if (partType === PART_TYPES.CARD) {
      return self.post(`/cards`, data)
    }

    if (partType === PART_TYPES.MACHINE) {
      return self.post(`/machines`, data)
    }
  }

  this.updatePart = function (pid, data) {
    return self.put('/parts/' + pid, data)
  }

  this.deletePart = function (pid) {
    return self.delete('/parts/' + pid)
  }

  /**
   * Soap Dispenser
   */

  this.getSoapDispensers = function (partType, filters) {
    return self.get('/dispensers')
  }

  this.createSoapDispenser = function (data) {
    return self.post('/dispensers', data)
  }

  this.updateSoapDispenser = function (dispenserId, data) {
    return self.put('/dispensers/' + dispenserId, data)
  }

  this.deleteSoapDispenser = function (pid) {
    return self.delete('/dispensers/' + pid)
  }

  this.replenishSoapDispenserUses = function (dispenserId, data) {
    return self.put(`/dispensers/${dispenserId}/restartUses`, data)
  }

  /**
   * Card
   */
  this.getCardEvents = function (cardId) {
    return self.get(`/cards/${cardId}/events`)
  }

  /**
   * Billing
   */
  this.billBuilding = function (bid, from, to, onlyCardEvents) {
    var query = ''

    if (from) {
      const userTimezoneOffset = from.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(from.getTime() - userTimezoneOffset)
      query += '&from=' + dateUTCFixed.toISOString()
    }

    if (to) {
      const userTimezoneOffset = to.getTimezoneOffset() * 60000
      const dateUTCFixed = new Date(to.getTime() - userTimezoneOffset)
      query += '&to=' + dateUTCFixed.toISOString()
    }

    if (onlyCardEvents) {
      query += '&onlyCardEvents=true'
    }

    return self.get(`/buildings/${bid}/bill?${query}`)
  }

  this.getBillPDF = function (bid) {
    return self.apiCallBinary(`/bills/${bid}/pdf`, 'GET')
  }

  this.cancelBill = function (bid, type, reason, discreditedItems) {
    var data = {}

    if (type) data.type = type
    if (reason) data.reason = reason
    if (reason) data.discreditedItems = discreditedItems

    return self.post(`/bills/${bid}/cancel`, data)
  }

  this.billShortDetail = function (bid) {
    return self.get(`/bills/${bid}/shortDetail`)
  }

  this.billDetail = function (bid) {
    return self.get(`/bills/${bid}`)
  }

  this.getBillItemMeasures = function () {
    return self.get('/bills/item/measures')
  }

  this.bill = function (bill) {
    return self.post('/bills', bill)
  }

  this.getBillTypes = function () {
    return self.get('/billtypes')
  }

  this.loadCollectors = function () {
    return self.get('/users/collectors')
  }

  this.findPreventiveMaintenance = function () {
    return self.get('/maintenance/preventive')
  }

  this.invalidateCache = function () {
    return self.post(`/maintenance/preventive/invalidate`)
  }

  this.setMachineIntoMaintenance = function (machineId) {
    return self.post(`/maintenance/machine/${machineId}/start`)
  }

  this.interruptMachineMaintenance = function (machineId) {
    return self.post(`/maintenance/machine/${machineId}/interrupt`)
  }

  this.findPreventiveMaintenanceByBuilding = function (buildingId) {
    return self.get('/maintenance/preventive/building/' + buildingId)
  }

  this.getBuildingMaintenances = function (bid) {
    return self.get('/maintenance/buildings/' + bid)
  }

  this.updateBillCollectionStatus = function (bid, status, reference) {
    return self.put(`/bill/${bid}/status/${status}`, {
      reference,
    })
  }

  this.promoteBillCollectionStatus = function (bid) {
    return self.put(`/bill/${bid}/status/promote`)
  }

  this.demoteBillCollectionStatus = function (bid) {
    return self.put(`/bill/${bid}/status/demote`)
  }

  this.assignBillDebtCollector = function (bid, user, date) {
    return self.put(`/bill/${bid}/user/assign/${user}/date/${date}`)
  }

  this.unassignBillDebtCollector = function (bid) {
    return self.put(`/bill/${bid}/user/unassign`)
  }

  /**
   * Branding
   */

  this.getBrandingItems = function (level) {
    return self.apiCall(
      `/branding/items${this.queryLevel(level, { prefix: '?' })}`,
      'GET'
    )
  }

  this.getBrandingItemRequests = function () {
    return self.get('/branding/requests')
  }

  this.deliverBrandingItemRequest = function (requestId) {
    return self.put(`/branding/requests/${requestId}/delivered`)
  }

  this.createBrandingItem = (item) => self.post('/branding/items', item)

  this.updateBrandingItem = (id, item) =>
    self.put(`/branding/items/${id}`, item)

  this.deleteBrandingItem = (id) => self.delete(`/branding/items/${id}`)

  /**
   * Machines
   */

  this.getMachines = function (filters) {
    const query = new URLSearchParams(filters)

    return self.get(`/machines?${query}`)
  }

  this.assignRPIChild = function (machineId, rpiChildMachineId) {
    return self.put(
      `/machines/${machineId}/assign-rpi-child/${rpiChildMachineId}`
    )
  }

  this.unassignRPIChild = function (machineId) {
    return self.put(`/machines/${machineId}/unassign-rpi-child`)
  }

  this.zeroingPendingUses = function (machineId) {
    return self.put(`/machines/${machineId}/zero-pending-uses`)
  }

  /**
   * Machine Models
   */
  this.getMachineModels = function () {
    return self.get('/machine/model')
  }

  this.createMachineModel = function (data) {
    return self.post('/machine/model', data)
  }

  this.updateMachineModel = function (mmid, data) {
    return self.put('/machine/model/' + mmid, data)
  }

  this.deleteMachineModel = function (mmid) {
    return self.delete('/machine/model/' + mmid)
  }

  /**
   * Maintenance
   */
  this.createMaintenance = function (buildingId, data) {
    return self.post('/maintenance/buildings/' + buildingId, data)
  }

  this.updateMaintenance = function (buildingId, maintenanceId, data) {
    return self.put(
      `/maintenance/${maintenanceId}/buildings/${buildingId}`,
      data
    )
  }

  this.deleteMaintenance = function (buildingId, maintenanceId) {
    return self.delete(`/maintenance/${maintenanceId}/buildings/${buildingId}`)
  }

  /**
   * Tiny Urls
   */

  this.getTinyUrls = function (level) {
    return self.get(`/tinyUrls${this.queryLevel(level, { prefix: '?' })}`)
  }

  this.createTinyUrl = (tinyUrl) => self.post('/tinyUrls', tinyUrl)

  this.createBulkTinyUrls = (count) => self.post(`/tinyUrls/${count}`)

  this.updateTinyUrl = (id, tinyUrl) => self.put(`/tinyUrls/${id}`, tinyUrl)

  this.assignTinyUrlToMachine = (token, machine) =>
    self.post(`/tinyUrls/${token}/machine/${machine?.id}`)

  this.unassignTinyUrlFromMachine = (token, machine) =>
    self.delete(`/tinyUrls/${token}/machine/${machine?.id}`)
}

app.service('LMAPI', ['$rootScope', '$http', 'Upload', LMAPI])

/**
 * Global Definitions
 */
Array.prototype.sortBy = function (property, property2, areNumbers) {
  return this.sort(function (a, b) {
    var valueA = areNumbers ? 0 : ''
    var valueB = areNumbers ? 0 : ''
    if (property) {
      valueA = areNumbers
        ? a[property] && !isNaN(a[property])
          ? +a[property]
          : Number.MIN_SAFE_INTEGER
        : (a[property] ? a[property] + '' : '').toUpperCase()

      valueB = areNumbers
        ? b[property] && !isNaN(b[property])
          ? +b[property]
          : Number.MIN_SAFE_INTEGER
        : (b[property] ? b[property] + '' : '').toUpperCase()

      if (property2) {
        var valueA2 = areNumbers
          ? a[property2] && !isNaN(a[property2])
            ? +a[property2]
            : Number.MIN_SAFE_INTEGER
          : (a[property2] ? a[property2] + '' : '').toUpperCase()

        var valueB2 = areNumbers
          ? b[property2] && !isNaN(b[property2])
            ? +b[property2]
            : Number.MIN_SAFE_INTEGER
          : (b[property2] ? b[property2] + '' : '').toUpperCase()

        if (valueA == valueB)
          return valueA2 < valueB2 ? -1 : valueA2 > valueB2 ? 1 : 0
        else return valueA < valueB ? -1 : valueA > valueB ? 1 : 0
      } else {
        return valueA < valueB ? -1 : valueA > valueB ? 1 : 0
      }
    } else {
      valueA = areNumbers
        ? a
          ? +a
          : Number.MIN_SAFE_INTEGER
        : (a ? a + '' : '').toUpperCase()
      valueB = areNumbers
        ? b
          ? +b
          : Number.MIN_SAFE_INTEGER
        : (b ? b + '' : '').toUpperCase()
    }

    return valueA < valueB ? -1 : valueA > valueB ? 1 : 0
  })
}
