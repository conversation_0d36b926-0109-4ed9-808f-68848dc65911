app.service('UserService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.users = []

    $rootScope.$on('loggedUser:changed', function () {
      self.users = []
      self.technicians = []
    })

    this.createUser = function (data) {
      return api.createUser(data)
    }

    this.updateUser = function (uid, data) {
      return api.updateUser(uid, data)
    }

    this.deleteUser = function (uid) {
      return api.deleteUser(uid)
    }

    this.loadUsers = function (filters) {
      return api.getUsers(filters).then(function (data) {
        self.users = data.data.users
      })
    }

    this.getUsers = function () {
      return self.users
    }

    this.loadTechnicians = function () {
      return api.getTechnicians().then(function (data) {
        self.technicians = data.data.users
      })
    }

    this.getTechnicians = function () {
      return self.technicians
    }

    this.resendAccountValidationEmail = function (accountId) {
      return api.resendAccountValidationEmail(accountId)
    }
  },
])
