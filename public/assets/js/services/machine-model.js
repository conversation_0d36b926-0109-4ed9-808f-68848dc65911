app.service('MachineModelService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.machineModels = []

    $rootScope.$on('loggedUser:changed', function () {
      self.machineModels = []
    })

    this.createMachineModel = function (data) {
      return api.createMachineModel(data)
    }

    this.updateMachineModel = function (mmid, data) {
      return api.updateMachineModel(mmid, data)
    }

    this.deleteMachineModel = function (mmid) {
      return api.deleteMachineModel(mmid)
    }

    this.loadMachineModels = function () {
      return api.getMachineModels().then(function (data) {
        self.machineModels = data.data.machineModels.sortBy('name')
      })
    }

    this.getMachineModels = function () {
      return self.machineModels
    }
  },
])
