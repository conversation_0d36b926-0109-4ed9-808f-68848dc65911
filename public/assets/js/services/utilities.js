app.service('UtilitiesService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this

    this.sendMqttMessage = function ({ buildingId, serialNumber, json }) {
      return api
        .post('/utilities/mqtt-message', {
          buildingId,
          serialNumber,
          json,
        })
        .then(function ({ data }) {
          return {
            result: data?.message,
          }
        })
        .catch(function ({ status = '', statusText = '', data = {} }) {
          return {
            error: `${status} . ${statusText} . ${data?.result_message} - ${data?.result_detail}`,
          }
        })
    }

    this.runTask = function ({ key }) {
      return api
        .put('/utilities/run-task', { key })
        .then(function ({ data }) {
          return {
            result: data?.message,
          }
        })
        .catch(function ({ status = '', statusText = '', data = {} }) {
          return {
            error: `${status} . ${statusText} . ${data?.result_message} - ${data?.result_detail}`,
          }
        })
    }

    this.getExchangeRates = function () {
      return api
        .get('/utilities/exchange-rates')
        .then(function ({ data }) {
          return {
            result: data?.message,
          }
        })
        .catch(function ({ status = '', statusText = '', data = {} }) {
          return {
            error: `${status} . ${statusText} . ${data?.result_message} - ${data?.result_detail}`,
          }
        })
    }
  },
])
