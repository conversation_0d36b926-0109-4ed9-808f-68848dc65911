app.service('TransactionService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this
    this.transactionResults = []

    $rootScope.$on('loggedUser:changed', function () {
      self.transactionResults = []
    })

    this.findTransactions = function (filters, level) {
      return api.findTransactions(filters, level).then(function (data) {
        self.transactionResults = data.data.transactions
      })
    }

    this.getTransactionResults = function () {
      return self.transactionResults
    }
  },
])
