app.service('MaintenanceDetailService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    var self = this
    this.preventiveMaintenances = []
    this.machineAverageUses = []

    $rootScope.$on('loggedUser:changed', function () {
      self.preventiveMaintenances = []
      self.machineAverageUses = []
    })

    this.findPreventiveMaintenanceByBuilding = function (buildingId) {
      return api
        .findPreventiveMaintenanceByBuilding(buildingId)
        .then(function (res) {
          self.preventiveMaintenances = res.data.maintenances
          self.machineAverageUses = res.data.machineAverageUses
        })
    }

    this.getPreventiveMaintenanceResults = function () {
      return self.preventiveMaintenances.sort()
    }

    this.getMachineAverageUsesResults = function () {
      return self.machineAverageUses.sort()
    }
  },
])
