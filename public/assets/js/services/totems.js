app.service('TotemService', [
  '$rootScope',
  'LMAPI',
  function ($rootScope, api) {
    this.loadBuildingsWithMappings = function () {
      return api.get('/totem/mappings')
    }

    this.loadTotemUsers = function () {
      return api.get('/users/totem')
    }

    this.createTotemPosTerminalMappings = function (mappings) {
      return api.post('/totem/mappings/posTerminal', { mappings })
    }

    this.createTotemPosMachineMappings = function (mappings) {
      return api.post('/totem/mappings/machine', { mappings })
    }
  },
])
