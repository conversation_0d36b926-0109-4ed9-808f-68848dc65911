/*jslint strict: true */
var app = angular.module('materialism', [
  'app.constants',

  'ngRoute',
  'ngAnimate',
  'ngSanitize',
  'ngPlaceholders',
  'ngTable',

  'angular-loading-bar',

  'angulartics',
  'angulartics.google.analytics',

  'uiGmapgoogle-maps',
  'ui.select',

  'gridshore.c3js.chart',
  'monospaced.elastic', // resizable textarea
  'mgcrea.ngStrap',
  'jcs-autoValidate',
  'ngFileUpload',
  //'textAngular',
  'fsm', // sticky header
  'smoothScroll',
  'LocalStorageModule',
  'cgBusy',

  'chroma.angularChroma',
  'angularTrix',
  'ngListSelect',
  'angularjs-dropdown-multiselect',
])
