app.controller('UsersController', [
  '$scope',
  '$window',
  '$aside',
  'UserService',
  'BuildingService',
  'AdministrationService',
  function (
    $scope,
    $window,
    $aside,
    userService,
    buildingService,
    administrationService
  ) {
    // settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: 'Add',
    }

    // adding demo data
    $scope.users = []
    $scope.buildings = []
    $scope.administrations = []

    $scope.roleKeys = {
      user: 'USER',
      totem: 'TOTEM',
      master: 'MASTER',
      administration: 'BUILDING_ADM',
      technician: 'TECHNICIAN',
      supervisor: 'SUPERVISOR',
      collector: 'DEBT_COLLECTOR',
      assistant: 'ASSISTANT',
      developer: 'DEVELOPER',
      taskRunner: 'TASK_RUNNER',
    }

    $scope.roles = {
      [$scope.roleKeys.user]: 'Usuario',
      [$scope.roleKeys.totem]: 'Totem',
      [$scope.roleKeys.master]: 'Master',
      [$scope.roleKeys.administration]: 'Administración',
      [$scope.roleKeys.technician]: 'Técnico',
      [$scope.roleKeys.supervisor]: 'Supervisor',
      [$scope.roleKeys.collector]: 'Cobrador',
      [$scope.roleKeys.assistant]: 'Asistente',
      [$scope.roleKeys.developer]: 'Developer',
      [$scope.roleKeys.taskRunner]: 'Task Runner',
    }

    $scope.clearFilter = function (filter) {
      $scope.filters[filter] = ''
    }

    $scope.clearError = function () {
      $scope.errorMessage = null
    }

    $scope.refreshUsers = function () {
      const level = 1
      $scope.loadingUsers = userService
        .loadUsers({ ...$scope.filters, level })
        .then(
          function (data) {
            $scope.users = userService.getUsers()
          },
          function (response) {
            $scope.errorMessage = `No se ha podido encontrar usuarios. ERROR: ${response.data.result_message}`
          }
        )
    }

    // defining template
    var formTpl = $aside({
      scope: $scope,
      template: '/user-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    // methods
    $scope.checkAll = function () {
      angular.forEach($scope.data, function (item) {
        item.selected = !item.selected
      })
    }

    $scope.editUser = function (item) {
      if (item) {
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        $scope.errorMessage = null
        showForm()
      }
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        showForm()
      }
    }

    $scope.createUser = function () {
      var item = {
        editing: true,
      }
      $scope.item = item
      $scope.settings.cmd = 'New'
      $scope.errorMessage = null
      showForm()
    }

    $scope.loadDependenciesByRole = function (role) {
      if (role === $scope.roleKeys.totem) {
        $scope.loadingBuildings = buildingService
          .loadBuildings(0)
          .then(function () {
            $scope.buildings = buildingService.getBuildings()
          })
      } else if (role === $scope.roleKeys.administration) {
        $scope.loadingAdministrations = administrationService
          .loadAdministrations(0)
          .then(function (data) {
            $scope.administrations = administrationService.getAdministrations()
          })
      }
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd == 'New') {
        $scope.errorMessage = null

        $scope.savingUser = userService
          .createUser($scope.item)
          .success(function () {
            hideForm()
            $scope.refreshUsers()
          })
          .error(function (reason) {
            $scope.errorMessage = `No se ha podido crear el usuario. ERROR: ${reason.result_detail}`
          })
      } else if ($scope.settings.cmd == 'Edit') {
        $scope.errorMessage = null
        $scope.savingUser = userService
          .updateUser(item.id, item)
          .success(function () {
            hideForm()
          })
          .error(function (reason) {
            $scope.errorMessage = `No se ha podido modificar el usuario. ERROR: ${reason.result_detail}`
          })
      }
    }

    $scope.deleteUser = function (item) {
      if (confirm('Estás seguro de querer borrar el usuario?')) {
        userService.deleteUser(item.id).success(function () {
          $scope.refreshUsers()
        })
      }
    }

    $scope.resendAccountValidationEmail = function (user) {
      if (
        confirm(
          'Estás seguro de querer volver a mandar el mail de confirmación de cuenta?'
        )
      ) {
        userService
          .resendAccountValidationEmail(user.accountId)
          .success(function () {
            $scope.refreshUsers()
            alert('Email enviado')
          })
      }
    }

    showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
      $scope.loadDependenciesByRole($scope.item.role)
    }

    hideForm = function () {
      formTpl.hide()
    }

    $scope.$on('$destroy', function () {
      hideForm()
    })
  },
])
