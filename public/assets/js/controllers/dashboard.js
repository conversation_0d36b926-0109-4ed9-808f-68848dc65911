app.controller('DashboardController', [
  '$window',
  '$scope',
  '$rootScope',
  '$interval',
  'colorService',
  'AccountService',
  function (
    $window,
    $scope,
    $rootScope,
    $interval,
    colorService,
    accountService
  ) {
    $rootScope.pageTitle = 'Dashboard'

    $scope.chart_color_pattern = [
      colorService.brand_success(),
      colorService.brand_danger(),
    ].join()

    $rootScope.page = {
      hideSidebar: false,
      hideTopbar: false,
    }

    accountService.getAccount().then(function (account) {
      $scope.account = account
    })

    $scope.roles = accountService.ROLES

    pattern = []
    pattern.push(colorService.theme())

    $scope.color_pattern = pattern.join()

    random_load_value = function (min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min
    }

    var values = []
    for (i = 0; i < 30; ++i) {
      values.push(random_load_value(40, 80))
    }

    randomData = function (size, min, max) {
      data = []
      for (i = 0; i < size; ++i) {
        if (data.length) {
          factor = 3
          minOrganic = data[data.length - 1] - factor
          maxOrganic = data[data.length - 1] + factor
          data.push(
            random_load_value(
              minOrganic < min ? min : minOrganic,
              maxOrganic > max ? max : maxOrganic
            )
          )
        } else {
          data.push(random_load_value(min, max))
        }
      }
      return data
    }

    $scope.randomDate = function () {
      start = new Date(2012, 0, 1)
      end = new Date()

      date = new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime())
      )
      dateString = date.toLocaleString()
      return dateString
    }

    // $scope.chartData1 = randomData(20, 40, 60);
    // $scope.chartData2 = randomData(20, 40, 60);
    // $scope.chartData3 = randomData(20, 40, 60);
    // $scope.chartData4 = randomData(100, 10, 30);

    // $scope.tabs = ["Log", "Timeline", "Messages"];

    $scope.data = accountService.dashboardDatal

    $scope.refresh = function () {
      $scope.loadingDashboard = accountService
        .getDashboardData()
        .then(function (data) {
          $scope.data = accountService.dashboardData
          $scope.showGraph()
        })
    }

    $scope.getChartColumns = function () {
      return [
        { id: 'Lavados', type: 'bar' },
        { id: 'Secados', type: 'bar' },
      ]
    }

    $scope.showGraph = function () {
      var config = {}
      config.bindto = '#bar-chart'
      config.data = {}
      config.data.json = {}
      config.data.json.Lavados = $scope.data.uses
        ? $scope.data.uses.washes.split(',')
        : []
      config.data.json.Secados = $scope.data.uses
        ? $scope.data.uses.dries.split(',')
        : []
      config.axis = {
        x: { label: { text: 'Dia', position: 'outer-middle' } },
        y: { label: { text: 'Número de usos', position: 'outer-middle' } },
      }
      config.data.types = { Lavados: 'bar', Secados: 'bar' }
      $scope.chart = c3.generate(config)
    }

    $scope.refresh()
  },
])
