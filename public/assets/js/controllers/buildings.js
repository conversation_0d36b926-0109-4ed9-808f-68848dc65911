app.controller('BuildingsController', [
  '$scope',
  '$window',
  '$aside',
  '$location',
  'BuildingService',
  'AdministrationService',
  function (
    $scope,
    $window,
    $aside,
    $location,
    buildingService,
    administrationService
  ) {
    $scope.data = {
      file: null,
    }

    // settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: 'Add',
    }

    $scope.filters = {
      quickSearch: '',
      orderBy: 'name',
    }

    $scope.buildingTypes = {
      BUILDING: 'EDIFICIO',
      HOTEL: 'HOTEL',
      LAUNDROMAT: 'LAUNDROMAT',
      COLIVING: 'COLIVING',
      THIRD_PARTY_LAUNDROMAT: 'LAUNDROMAT DE 3ROS',
    }

    $scope.buildingContractTypes = {
      PREPAID: 'PREPAGO',
      MIXED: 'MIXTO',
      POSTPAID: 'POSTPAGO',
    }

    $scope.buildingClosureTypes = {
      POSTPAID: 'POSTPAGO',
      POSTPAID_WITH_MINIMUM: 'POSTPAGO-CON-MÍNIMO',
      POSTPAID_WITH_MINIMUM_PER_UNIT: 'POSTPAGO-CON-MINIMO-POR-UNIDAD',
      POSTPAID_RESOURCE_REIMBURSEMENT: 'POSTPAGO-CON-RR',
      POSTPAID_WITH_MINIMUM_RESOURCE_REIMBURSEMENT: 'POSTPAGO-CON-MÍNIMO-Y-RR',
      POSTPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT:
        'POSTPAGO-CON-MÍNIMO-POR-UNIDAD-Y-RR',
      PREPAID: 'PREPAGO',
      PREPAID_WITH_MINIMUM_PER_UNIT: 'PREPAGO-CON-MÍNIMO-POR-UNIDAD',
      PREPAID_AUTO_RECHARGEABLE_USES: 'PREPAGO-USOS-AUTO-RECARGADOS',
      PREPAID_RESOURCE_REIMBURSEMENT: 'PREPAGO-CON-RR',
      PREPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT:
        'PREPAGO-CON-MÍNIMO-POR-UNIDAD-CON-RR',
      MIXED: 'MIXTO',
      MIXED_RESOURCE_REIMBURSEMENT: 'MIXTO-CON-RR',
      MIXED_AUTO_RECHARGEABLE_USES: 'MIXTO-CON-USOS-AUTO-RECARGADOS',
    }

    $scope.prepaidBuildingClosureType = {
      PREPAID: 'PREPAGO',
      PREPAID_WITH_MINIMUM_PER_UNIT: 'PREPAGO-CON-MÍNIMO-POR-UNIDAD',
      PREPAID_AUTO_RECHARGEABLE_USES: 'PREPAGO-USOS-AUTO-RECARGADOS',
      PREPAID_RESOURCE_REIMBURSEMENT: 'PREPAGO-CON-RR',
      PREPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT:
        'PREPAGO-CON-MÍNIMO-POR-UNIDAD-CON-RR',
    }

    $scope.colivingBuildingClosureType = {
      COLIVING: 'COLIVING',
    }

    $scope.updateClosureTypes = function (buildingType) {
      switch (buildingType) {
        case 'COLIVING':
          $scope.filteredClosureTypes = $scope.colivingBuildingClosureType
          break
        case 'LAUNDROMAT':
        case 'THIRD_PARTY_LAUNDROMAT':
          $scope.filteredClosureTypes = $scope.prepaidBuildingClosureType
          break
        default:
          $scope.filteredClosureTypes = $scope.buildingClosureTypes
          break
      }
    }

    $scope.data = {
      administrations: [],
      rates: [],
    }

    $scope.buildings = []

    var paymentTpl = $aside({
      scope: $scope,
      template: '/payment-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    $scope.orderColumn = function (order) {
      $scope.filters.reverse =
        order != $scope.filters.orderBy ? false : !$scope.filters.reverse
      $scope.filters.orderBy = order
    }

    $scope.showLoadPayment = function () {
      $scope.showPaymentForm()
    }

    $scope.showPaymentForm = function () {
      angular.element('.tooltip').remove()
      paymentTpl.show()
    }

    $scope.hidePaymentForm = function () {
      paymentTpl.hide()
    }

    $scope.uploadPaymentFile = function (file) {
      $scope.uploadingPayment = buildingService.uploadingPayment(file).then(
        function (response) {
          alert('Carga finalizada.')

          if (
            response.data.processed_error_index ||
            response.data.processed_error_payments
          ) {
            $scope.errorMessage =
              (response.data.processed_error_index.length ? 'Filas: ' : '') +
              response.data.processed_error_index.join(', ') +
              (response.data.processed_error_payments.length
                ? '<br>Datos:<br>'
                : '') +
              response.data.processed_error_payments.join('<br>')
          } else {
            $scope.errorMessage = ''
            $scope.hidePaymentForm()
          }
        },
        function (response) {
          if (response.status > 0)
            $scope.errorMessage =
              'No se ha podido cargar el archivo. ' + reason.result_detail
                ? reason.result_detail
                : ''
        }
      )
    }

    $scope.refreshBuildings = function () {
      $scope.loadingBuildings = buildingService
        .loadBuildings(1)
        .then(function () {
          $scope.buildings = buildingService.getBuildings()
        })
    }

    $scope.refreshFormData = function () {
      $scope.loadingRates = buildingService.loadRates().then(function (data) {
        $scope.data.rates = buildingService.getRates()
      })

      $scope.loadingAdministrations = administrationService
        .loadAdministrations(1)
        .then(function (data) {
          $scope.data.administrations =
            administrationService.getAdministrations()
        })
    }

    // defining template
    var formTpl = $aside({
      scope: $scope,
      template: '/building-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    // methods
    $scope.checkAll = function () {
      angular.forEach($scope.data, function (item) {
        item.selected = !item.selected
      })
    }

    $scope.editBuilding = function (item) {
      $scope.refreshFormData()
      if (item) {
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        $scope.showForm()

        $scope.syncBillingNameWithName =
          $scope.item.name === $scope.item.billingName
      }
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        $scope.showForm()
      }
    }

    $scope.createBuilding = function () {
      let item = {
        editing: false,
        name: '',
        billingName: '',
      }
      $scope.item = item
      $scope.settings.cmd = 'New'

      $scope.syncBillingNameWithName = false
      $scope.billingNameSync()

      $scope.refreshFormData()
      $scope.showForm()
    }

    $scope.billingNameSync = function () {
      $scope.copyNameToBillingName()
      $scope.syncBillingNameWithName = !$scope.syncBillingNameWithName
    }

    $scope.$watch('item.name', function (buildingName) {
      if ($scope.syncBillingNameWithName) {
        $scope.item.billingName = buildingName
      }
    })

    $scope.copyNameToBillingName = function () {
      if ($scope.item && $scope.item.name) {
        $scope.item.billingName = $scope.item.name
      }
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd == 'New') {
        $scope.errorMessage = null

        $scope.savingBuilding = buildingService
          .createBuilding($scope.item)
          .success(function () {
            $scope.hideForm()
            $scope.refreshBuildings()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear el edificio. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      } else if ($scope.settings.cmd == 'Edit') {
        $scope.errorMessage = null

        $scope.savingBuildings = buildingService
          .updateBuilding(item.id, item)
          .success(function () {
            $scope.hideForm()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido modificar el edificio. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      }
    }

    $scope.deleteBuilding = function (item) {
      if (confirm('Are you sure?')) {
        buildingService.deleteBuilding(item.id).success(function () {
          $scope.refreshBuildings()
        })
      }
    }

    $scope.showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = function () {
      formTpl.hide()
    }

    $scope.$on('$destroy', function () {
      $scope.hideForm()
    })

    $scope.showBuilding = function (b) {
      $location.path('/buildings/' + b.id)
    }

    $scope.updateClosureTypes()
    $scope.refreshBuildings()
  },
])
