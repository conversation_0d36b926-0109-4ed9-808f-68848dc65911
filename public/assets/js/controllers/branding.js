app.controller('BrandingController', [
  '$scope',
  '$window',
  '$aside',
  'BrandingService',
  'BuildingService',
  function ($scope, $window, $aside, brandingService, buildingService) {
    const BRANDING_ITEMS_QUERY_LEVEL = 1

    $scope.data = {
      items: [],
      requests: [],
      rates: [],
    }

    $scope.filters = {
      quickSearch: '',
    }

    $scope.settings = {
      cmd: '',
    }

    $scope.refreshItems = function () {
      $scope.loadingItems = brandingService
        .loadBrandingItems(BRANDING_ITEMS_QUERY_LEVEL)
        .then(function (data) {
          $scope.data.items = data.data.items
        })
    }

    $scope.refreshRequests = function () {
      $scope.loadingRequests = brandingService
        .loadRequestedItems()
        .then(function (data) {
          $scope.data.requests = data.data.requests
        })
    }

    $scope.refreshAll = function () {
      $scope.refreshItems()
      $scope.refreshRequests()
    }

    $scope.deliverRequest = function (request) {
      if (request && confirm('Are you sure?')) {
        brandingService.deliverRequestedItem(request.id).success(function () {
          $scope.refreshRequests()
        })
      }
    }

    $scope.refreshAll()

    /* ----------- branding item form ----------- */

    const FORM_CMD = {
      edit: 'Edit',
      new: 'New',
    }

    const formTpl = $aside({
      scope: $scope,
      template: '/branding-item-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    $scope.showForm = () => {
      $scope.refreshFormData()

      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = () => {
      formTpl.hide()
    }

    $scope.$on('$destroy', () => {
      $scope.hideForm()
    })

    $scope.refreshFormData = () => {
      if (!$scope.data.rates?.length) {
        $scope.loadingRates = buildingService.loadRates().then((data) => {
          $scope.data.rates = buildingService.getRates()
        })
      }
    }

    $scope.submitItem = (item) => {
      if ($scope.settings.cmd == FORM_CMD.new) {
        $scope.saveItem(item)
      } else if ($scope.settings.cmd == FORM_CMD.edit) {
        $scope.updateItem(item)
      }
    }

    $scope.clearError = () => {
      $scope.errorMessage = ''
    }

    $scope.hideTrixToolbarItems = () => {
      angular.element('trix-toolbar .link').remove()
      angular.element('trix-toolbar .quote').remove()
      angular.element('trix-toolbar .code').remove()
    }

    /* ----------- create branding item ----------- */

    $scope.createItem = () => {
      $scope.item = {
        editing: true,
      }

      $scope.settings.cmd = FORM_CMD.new

      $scope.showForm()
    }

    $scope.saveItem = (item) => {
      $scope.errorMessage = null

      $scope.submittingItem = brandingService
        .createItem($scope.item)
        .success(() => {
          $scope.hideForm()

          $scope.refreshItems()
        })
        .error((reason) => {
          $scope.errorMessage = `No se ha podido crear el accesorio. ${
            reason?.result_detail ?? ''
          }`
        })
    }

    /* ----------- edit branding item ----------- */

    $scope.editItem = (item) => {
      if (!item) return

      $scope.item = item
      ;($scope.item.editing = true), ($scope.settings.cmd = FORM_CMD.edit)

      $scope.showForm()
    }

    $scope.updateItem = (item) => {
      $scope.errorMessage = null

      $scope.submittingItem = brandingService
        .updateItem(item.id, item)
        .success(() => {
          $scope.hideForm()
        })
        .error((reason) => {
          $scope.errorMessage = `No se ha podido actualizar el accesorio. ${
            reason?.result_detail ?? ''
          }`
        })
    }

    /* ----------- delete branding item ----------- */

    $scope.deleteItem = (item) => {
      $scope.errorMessage = null

      $scope.submittingItem = brandingService
        .deleteItem(item.id)
        .success(() => {
          $scope.hideForm()
          $scope.refreshItems()
        })
        .error((reason) => {
          $scope.errorMessage = `No se ha podido eliminar el accesorio. ${
            reason?.result_detail ?? ''
          }`
        })
    }
  },
])
