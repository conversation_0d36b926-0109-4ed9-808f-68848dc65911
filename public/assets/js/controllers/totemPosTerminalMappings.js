app.controller('TotemPosTerminalMappingsController', [
  '$scope',
  'TotemService',
  function ($scope, totemService) {
    $scope.totemUsers = []

    $scope.loadTotemUsers = function () {
      totemService.loadTotemUsers().success(function (data) {
        $scope.totemUsers = data.users
      })
    }

    $scope.loadTotemUsers()

    $scope.savePosTerminalCodeMapping = function (user) {
      const mappings = [
        {
          totemUserId: user.id,
          posTerminalCode: user.posTerminalCode,
        },
      ]

      totemService.createTotemPosTerminalMappings(mappings)
      user.editPosTerminalCode = false
    }
  },
])
