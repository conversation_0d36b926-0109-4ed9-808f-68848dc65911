app.controller('UtilitiesController', [
  '$scope',
  '$window',
  '$aside',
  '$modal',
  'UtilitiesService',
  function ($scope, $window, $aside, $modal, utilitiesService) {
    /**
     * MQTT Messages
     */

    $scope.message = {
      collapsed: true,
    }

    $scope.openMqttMessage = function () {
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/utilities/mqtt-message.html',
        controller: 'UtilitiesController',
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    $scope.sendMessage = function () {
      $scope.message.sending = utilitiesService
        .sendMqttMessage($scope.message)
        .then(function (data) {
          $scope.message = {
            ...$scope.message,
            error: null,
            result: null,
            ...data,
          }
        })
    }

    $scope.cleanMessage = function () {
      $scope.message = {}
    }

    $scope.toggleMessage = function () {
      $scope.message.collapsed = !$scope.message.collapsed
    }

    /**
     * Run Task
     */

    $scope.task = {
      collapsed: true,
    }

    $scope.openTask = function () {
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/utilities/run-task.html',
        controller: 'UtilitiesController',
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    $scope.runTask = function () {
      $scope.task.sending = utilitiesService
        .runTask($scope.task)
        .then(function (data) {
          $scope.task = {
            ...$scope.task,
            error: null,
            result: null,
            ...data,
          }
        })
    }

    $scope.cleanTask = function () {
      $scope.task = {}
    }

    $scope.toggleTask = function () {
      $scope.task.collapsed = !$scope.task.collapsed
    }

    /**
     * Resources
     */
    $scope.openResource = function (name) {
      var modal = $modal({
        scope: $scope,
        template: `assets/tpl/ui-elements/${name}.html`,
        controller: 'UtilitiesController',
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    /* see results modal */

    const ACTIONS = {
      EXCHANGE_RATES: 'Exchange Rates',
    }

    $scope.getResults = function () {
      const method = {
        [ACTIONS.EXCHANGE_RATES]: utilitiesService.getExchangeRates,
      }[$scope.results.action]

      if (!method) {
        $scope.results = {
          ...$scope.results,
          error: 'unsupported action',
          result: null,
        }
        return
      }

      $scope.results.sending = method?.().then(function (data) {
        $scope.results = {
          ...$scope.results,
          error: null,
          result: null,
          ...data,
        }
      })
    }

    $scope.cleanResults = function () {
      $scope.results = {
        ...$scope.results,
        error: null,
        result: null,
      }
    }

    $scope.toggleResults = function () {
      $scope.results.collapsed = !$scope.results.collapsed
    }

    /**
     * Exchange Rates
     */

    $scope.openExchangeRates = function () {
      $scope.results = {
        collapsed: false,
        action: ACTIONS.EXCHANGE_RATES,
      }

      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/utilities/get-result.html',
        controller: 'UtilitiesController',
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }
  },
])
