app.controller('AdministrationsController', [
  '$scope',
  '$window',
  '$aside',
  '$modal',
  'AdministrationService',
  'BillingService',
  function (
    $scope,
    $window,
    $aside,
    $modal,
    administrationService,
    billingService
  ) {
    // settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: 'Add',
    }

    $scope.toast = {
      message: '',
      type: '',
    }

    $scope.filters = {
      quickSearch: '',
    }

    $scope.administrations = []

    $scope.refreshAdministrations = function () {
      $scope.loadingAdministrations = administrationService
        .loadAdministrations(1)
        .then(function (data) {
          const dbAdministrations = administrationService.getAdministrations()

          for (let i = 0; i < dbAdministrations.length; i++) {
            $scope.administrations.push({
              ...dbAdministrations[i],
              mpTokenAboutToExpire: $scope.isMpTopenAboutToExpire(
                dbAdministrations[i]
              ),
            })
          }
        })

      billingService.loadCollectors().then(function (data) {
        $scope.collectors = billingService.getCollectors()
      })
    }

    // defining template
    var formTpl = $aside({
      scope: $scope,
      template: '/administration-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    // methods
    $scope.checkAll = function () {
      angular.forEach($scope.data, function (item) {
        item.selected = !item.selected
      })
    }

    $scope.editAdministration = function (item) {
      if (item) {
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        $scope.showForm()
      }
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        $scope.showForm()
      }
    }

    $scope.createAdministration = function () {
      var item = {
        editing: true,
      }
      $scope.item = item
      $scope.settings.cmd = 'New'
      $scope.showForm()
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd == 'New') {
        $scope.errorMessage = null

        $scope.savingAdministration = administrationService
          .createAdministration($scope.item)
          .success(function () {
            $scope.hideForm()
            $scope.refreshAdministrations()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear la administración. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      } else if ($scope.settings.cmd == 'Edit') {
        $scope.errorMessage = null

        $scope.savingAdministration = administrationService
          .updateAdministration(item.id, item)
          .success(function () {
            $scope.hideForm()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido modificar la administración. ' +
              reason.result_detail
                ? reason.result_detail
                : ''
          })
      }
    }

    $scope.deleteAdministration = function (item) {
      if (confirm('Are you sure?')) {
        administrationService
          .deleteAdministration(item.id)
          .success(function () {
            $scope.refreshAdministrations()
          })
      }
    }

    $scope.sendRefreshTokenReminder = function (item) {
      if (confirm('Estas Seguro?')) {
        administrationService
          .sendRefreshTokenReminder(item.id)
          .success(function () {})
      }
    }

    $scope.showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = function () {
      formTpl.hide()
    }

    $scope.$on('$destroy', function () {
      $scope.hideForm()
    })

    $scope.refreshAdministrations()

    $scope.editBillsCollector = function (a) {
      $scope.selectedAdministration = a

      $scope.collector = ''
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/administration-update-collector.html',
        controller: EditBillsCollectorController,
        show: false,
      })
      modal.$promise.then(modal.show)
    }

    $scope.runClosure = function (admin) {
      if (
        confirm(
          'Estas seguro de ejecutar el cierre mensual de esta administración?'
        )
      ) {
        administrationService.runClosure(admin.id).error((error) => {
          $scope.toast.text = `No se realizó el cierre de la administración: ${
            error.result_detail ? error.result_detail : ''
          }`
          $scope.toast.type = 'Error'
        })
      }
    }

    $scope.isMpTopenAboutToExpire = function (admin) {
      let mpCreationDate = admin.mpCreationDate

      if (mpCreationDate) {
        mpCreationDate = new Date(mpCreationDate)
        const today = new Date()
        // In milliseconds
        const diffTime = mpCreationDate - today
        // In days
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        return diffDays <= 20
      }

      return false
    }

    var EditBillsCollectorController = [
      '$scope',
      '$modal',
      '$filter',
      function ($scope, modal, $filter) {
        $scope.collector = ''
        $scope.collectionDate = ''
        $scope.cancelled = false

        if ($scope.selectedAdministration.collector) {
          const selectedCollector = $scope.collectors.find(
            (c) =>
              c.name + ' ' + c.lastname ===
              $scope.selectedAdministration.collector
          )
          $scope.collector = $scope.selectedAdministration
            ? selectedCollector
            : ''
        }

        if ($scope.selectedAdministration.collectionDate) {
          const selectedDate =
            $scope.selectedAdministration.collectionDate.split('-')
          $scope.collectionDate = new Date(
            selectedDate[2],
            selectedDate[1],
            selectedDate[0]
          )
        }

        $scope.submit = function (collector, collectionDate) {
          $scope.collector =
            !collector && !collectionDate ? 'unassign' : collector?.id || 0

          if (collectionDate) {
            $scope.collectionDate =
              $filter('date')(collectionDate, 'dd-MM-yyyy', '-0300') + ''
          } else {
            $scope.collectionDate = 'null'
          }

          if ($scope.collector === 'unassign') {
            $scope.loadingResults = administrationService
              .unassignBillDebtCollector($scope.selectedAdministration.id)
              .success(function (data) {
                $scope.refreshAdministrations()
              })
          } else {
            $scope.loadingResults = administrationService
              .assignBillDebtCollector(
                $scope.selectedAdministration.id,
                $scope.collector,
                $scope.collectionDate
              )
              .success(function (data) {
                $scope.refreshAdministrations()
              })
          }

          $scope.close()
        }

        $scope.close = function () {
          $scope.collector = ''
          $scope.collectionDate = ''
          $scope.cancelled = true
          $scope.$hide()
        }
      },
    ]
  },
])
