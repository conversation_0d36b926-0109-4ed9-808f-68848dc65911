app.controller('BillingController', [
  '$scope',
  '$modal',
  '$window',
  '$aside',
  'ReportService',
  'MachineService',
  'BuildingService',
  'AccountService',
  'BillingService',
  'AdministrationService',
  function (
    $scope,
    $modal,
    $window,
    $aside,
    reportService,
    machineService,
    buildingService,
    accountService,
    billingService,
    administrationService
  ) {
    $scope.results = []

    $scope.states = [
      {
        key: ['SENT', 'CANCELLED'],
        text: 'SENT & CANCELLED',
        value: ['SENT', 'CANCELLED'],
      },
      { key: ['NEW'], text: 'NEW', value: ['NEW'] },
      { key: ['ERROR_DGI'], text: 'ERROR_DGI', value: ['ERROR_DGI'] },
    ]

    $scope.filters = {
      state: $scope.states[0].key,
    }

    $scope.data = {
      machines: [],
    }

    $scope.billOrigins = [
      { key: ['MercadoPagos'], text: 'MercadoPagos', value: ['MercadoPagos'] },
      {
        key: ['REDPAGOS-BambooPayment'],
        text: 'RedPagos',
        value: ['REDPAGOS-BambooPayment'],
      },
      { key: ['TransAct'], text: 'TransAct', value: ['TransAct'] },
      { key: ['PagosWeb'], text: 'Pagos Web', value: ['PagosWeb'] },
      {
        key: ['QR Payment - MercadoPagos'],
        text: 'QR Payment',
        value: ['QR Payment - MercadoPagos'],
      },
      // Deprecated origins which are not generated anymore. Keeping them for historical researches.
      { key: ['Bancard'], text: 'Bancard', value: ['Bancard'] },
      {
        key: ['Totem Payment - LAUNDROMAT - 1069'],
        text: 'Totem Efectivo (Micenas - discontinuado)',
        value: ['Totem Payment - LAUNDROMAT - 1069'],
      },
      {
        key: ['RedPagos'],
        text: 'RedPagos (Qualit - discontinuado)',
        value: ['RedPagos'],
      },
    ]

    $scope.collectionStatus = {
      PENDING_COLLECTION: 'Pendiente de Cobro',
      HOLDED: 'Valores en Cartera',
      AVAILABLE: 'Disponible',
      RECONCILED: 'Conciliada',
    }

    $scope.collectionStatusKeys = Object.keys($scope.collectionStatus)

    const initPagination = {
      page: 1,
      perPage: 50,
    }

    $scope.pagination = {
      ...initPagination,
      pageSizes: [30, 50, 100],
    }

    $scope.orderColumn = function (order) {
      $scope.filters.orderBy = order

      if (order == 'timestamp') {
        order = 'creationDate'
      }

      if (order == 'billOrigin') {
        order = 'transactions.origin'
      }

      $scope.filters.reverse =
        order != $scope.filters.order ? true : !$scope.filters.reverse
      $scope.filters.order = order
      $scope.filters.direction = $scope.filters.reverse ? 'desc' : 'asc'

      $scope.refresh()
    }

    $scope.setPageSize = function (size) {
      $scope.pagination.perPage = size || 50
      $scope.refresh($scope.pagination)
    }

    $scope.setPage = function (page) {
      $scope.pagination.page = page || 1
      $scope.refresh($scope.pagination)
    }

    $scope.loadWithUser = function () {
      accountService.getAccount().then(function (account) {
        $scope.filters.account = account
      })
    }

    $scope.refresh = function (pagination = initPagination) {
      const filters = {
        ...$scope.filters,
        ...pagination,
        order: $scope.filters.order,
        direction: $scope.filters.direction,
      }

      $scope.loadingResults = billingService
        .findBills(filters)
        .then(function () {
          $scope.results = billingService.getBillResults()
          $scope.pagination = { ...$scope.pagination, ...pagination }
        })
    }

    $scope.refreshFilters = function () {
      $scope.loadingFilters = buildingService
        .loadBuildings(1)
        .then(function () {
          $scope.data.buildings = buildingService.getBuildings()
        })
      $scope.loadingAdministrations = administrationService
        .loadAdministrations(1)
        .then(function (data) {
          $scope.data.administrations =
            administrationService.getAdministrations()
        })

      billingService.loadTypes().then(function (data) {
        $scope.data.types = billingService.getTypes()
      })

      $scope.data.modes = ['BUILDING', 'UNIT', 'MISC']

      billingService.loadCollectors().then(function (data) {
        $scope.data.collectors = billingService.getCollectors()
      })
    }

    $scope.clearFilter = function (filter) {
      $scope.filters[filter] = null
    }

    $scope.viewBillPDF = function (b) {
      $scope.loadingResults = billingService
        .getBillPDF(b)
        .success(function (data) {
          var blob = new Blob([data], { type: 'application/pdf' })
          var objectUrl = URL.createObjectURL(blob)
          window.open(objectUrl)
        })
    }

    var BillDetailController = [
      '$scope',
      'BillingService',
      function ($scope, BillingService) {
        $scope.lines = []
        $scope.loadingDetails = BillingService.getBillDetails(
          $scope.selectedBill
        ).success(function (data) {
          $scope.lines = data
        })

        $scope.close = function () {
          $scope.$hide()
        }
      },
    ]

    $scope.viewBillDetails = function (b) {
      $scope.selectedBill = b

      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/bill-detail.html',
        controller: BillDetailController,
        show: false,
      })

      modal.$promise.then(modal.show)
    }

    $scope.exportToExcel = function () {
      $scope.loadingResults = billingService
        .exportToExcel($scope.filters)
        .then(function (data) {
          var blob = new Blob([data.data], { type: 'application/vnd.ms-excel' })
          var objectUrl = URL.createObjectURL(blob)
          window.open(objectUrl)
        })
    }

    var NewBillController = [
      '$scope',
      '$modal',
      function ($scope, modal, BillingService) {
        $scope.billToType = {
          UNIT: 'UNIT',
          BUILDING: 'BUILDING',
          MISC: 'MISC',
          FINAL: 'FINAL',
        }

        $scope.billToTypes = [
          { key: 'MISC', text: 'MISC', value: 'MISC' },
          { key: 'BUILDING', text: 'BUILDING', value: 'BUILDING' },
          { key: 'UNIT', text: 'UNIT', value: 'UNIT' },
          { key: 'FINAL', text: 'FINAL', value: 'FINAL' },
        ]

        $scope.currencies = [
          { key: 'UYU', text: 'UYU', value: 'UYU' },
          { key: 'USD', text: 'USD', value: 'USD' },
        ]

        $scope.paymentMethods = [
          { key: 2, text: 'Credito', value: 2 },
          { key: 1, text: 'Contado', value: 1 },
        ]

        $scope.docTypes = [
          { key: 2, text: 'RUT', value: 2 },
          { key: 3, text: 'CI', value: 3 },
          { key: 4, text: 'OTROS', value: 4 },
        ]

        $scope.newBill = {
          items: [],
          billToType: $scope.billToTypes[0].key,
          currency: $scope.currencies[0].key,
          paymentMethod: $scope.paymentMethods[0].key,
        }

        $scope.newItem = {}

        $scope.clearFilter = function (data) {
          $scope.data[data] = null
          $scope.newBill.unit = null
        }

        $scope.handleBuildingChange = function () {
          $scope.resetBuildingRelatedFilters()
          $scope.getBuilding()
        }

        $scope.resetBuildingRelatedFilters = function () {
          $scope.data.unit = ''
        }

        $scope.getBuilding = function () {
          $scope.loadingBuilding = buildingService
            .getBuildingById($scope.data.building.id)
            .then(function (data) {
              $scope.data.building = data.data
            })
        }

        $scope.getBillItemMeasures = function () {
          $scope.loadingBillItemMeasures = billingService
            .getBillItemMeasures()
            .then(function (data) {
              $scope.billItemMeasures = data.data
            })
        }

        $scope.getBillItemMeasures()

        $scope.getSubtotal = function () {
          var subtotal = 0
          angular.forEach($scope.newBill.items, function (i) {
            subtotal += i.amount * i.unitPrice
          })
          return subtotal / 1.22
        }

        $scope.getIva = function () {
          var subtotal = 0
          angular.forEach($scope.newBill.items, function (i) {
            subtotal += i.amount * i.unitPrice
          })
          return subtotal * 0.22
        }

        $scope.getTotal = function () {
          var subtotal = 0
          angular.forEach($scope.newBill.items, function (i) {
            subtotal += i.amount * i.unitPrice
          })
          return subtotal
        }

        $scope.addNewItem = function () {
          $scope.newBill.items.push(angular.copy($scope.newItem))
          $scope.newItem = null
        }

        $scope.removeItem = function (i) {
          $scope.newBill.items.splice($scope.newBill.items.indexOf(i), 1)
        }

        $scope.$parent.$on('modal.hide.before', function (modal) {
          modal.targetScope.newBill = $scope.newBill
        })

        $scope.save = function () {
          $scope.$hide()
        }

        $scope.handleBillToTypeChange = function () {
          $scope.building = null
          $scope.data.building = null
          $scope.data.unit = null
          if ($scope.newBill.recipient != null) {
            $scope.newBill.recipient.name = ''
            $scope.newBill.recipient.docType = ''
            $scope.newBill.recipient.doc = ''
            $scope.newBill.recipient.address = ''
            $scope.newBill.recipient.city = ''
            $scope.newBill.recipient.department = ''
            $scope.newBill.recipient.country = ''
            $scope.newBill.recipient.countryCode = ''
          }
        }

        $scope.createBill = function () {
          $scope.errorMessage = null

          if ($scope.newBill.billToType !== $scope.billToType.MISC) {
            $scope.newBill.recipient = null
          }

          if ($scope.newBill.billToType === $scope.billToType.BUILDING) {
            if ($scope.building == null) {
              $scope.errorMessage = `No se ha podido crear la factura. ERROR: No existe edificio`

              return
            } else {
              $scope.newBill.buildingId = $scope.building.id
            }
          } else if ($scope.newBill.billToType === $scope.billToType.UNIT) {
            if ($scope.unit == null) {
              $scope.errorMessage = `No se ha podido crear la factura. ERROR: No existe unidad`

              return
            } else {
              $scope.newBill.unitId = $scope.unit.id
            }
          }

          if (!$scope.newBill.periodStart && !$scope.newBill.periodEnd) {
            $scope.newBill.periodStart = ''
            $scope.newBill.periodEnd = ''
          }

          $scope.loadingNewBill = billingService.bill($scope.newBill).then(
            function (response) {
              $scope.$hide()
            },
            function (response) {
              $scope.errorMessage = `No se ha podido crear la factura. ERROR: ${response.data.result_message}`
            }
          )
        }

        $scope.close = function () {
          $scope.newBill = null
          $scope.$hide()
        }
      },
    ]

    $scope.showCreateNewBill = function () {
      const modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/new-bill.html',
        controller: NewBillController,
        show: false,
      })

      modal.$promise.then(modal.show)
    }

    $scope.cancelBill = function (b) {
      $scope.selectedBill = b
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/bill-choose-cancel-type.html',
        controller: CancelBillController,
        show: false,
      })
      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        if (
          modal.targetScope.cancelType &&
          modal.targetScope.cancelType != ''
        ) {
          $scope.loadingResults = billingService
            .cancelBill(
              b,
              modal.targetScope.cancelType,
              modal.targetScope.reason,
              modal.targetScope.discreditedItems
            )
            .success(function (data) {
              $scope.refresh()
              unsubscribe()
            })
        } else {
          unsubscribe()
        }
      })

      modal.$promise.then(modal.show)
    }

    var CancelBillController = [
      '$scope',
      '$modal',
      'BillingService',
      'ReportService',
      function ($scope, modal, BillingService, ReportService) {
        $scope.cancelType = ''
        $scope.reason = ''

        $scope.shortDetails = BillingService.getBillShortDetails(
          $scope.selectedBill
        ).success(function (data) {
          $scope.lines = data
          $scope.calculateDiscreditedAmount()
        })

        $scope.shortResults = ReportService.getDiscreditedUses(
          $scope.selectedBill
        ).success(function (data) {
          $scope.discrediteResults = data.uses
        })

        $scope.submit = function (type) {
          $scope.cancelType = type
          $scope.$hide()
        }

        $scope.$parent.$on('modal.show.before', function (modal) {
          $scope.reason =
            'Anulación de factura ' +
            $scope.selectedBill.serie +
            '-' +
            $scope.selectedBill.number
          $scope.selectedBill.discreditedAmount = 0
        })

        $scope.$parent.$on('modal.hide.before', function (modal) {
          modal.targetScope.cancelType = $scope.cancelType
          modal.targetScope.reason = $scope.reason
          modal.targetScope.discreditedItems = $scope.lines
        })

        $scope.close = function () {
          $scope.cancelType = ''
          $scope.reason = ''
          $scope.$hide()
        }

        $scope.calculateDiscreditedAmount = function () {
          $scope.selectedBill.discreditedAmount = 0
          angular.forEach($scope.lines, function (value) {
            $scope.selectedBill.discreditedAmount +=
              value.discreditedAmount * value.unitPrice
          })
          $scope.selectedBill.discreditedAmount *= 1.22
          $scope.isNumberDiscreditedAmount = !isNaN(
            $scope.selectedBill.discreditedAmount
          )
        }
      },
    ]

    $scope.editBillCollectionStatus = function (b) {
      $scope.selectedBill = b

      $scope.previousSelectedStatus = b.billCollectionStatus
        ? $scope.collectionStatus[b.billCollectionStatus]
        : ''
      $scope.selectedStatus = ''
      $scope.paymentReference = b.paymentReference ? b.paymentReference : ''
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/bill-update-collection-status.html',
        controller: EditBillingCollectionStatusController,
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        if (modal.targetScope.selectedStatus) {
          $scope.loadingResults = billingService
            .updateBillCollectionStatus(
              b.id,
              modal.targetScope.selectedStatus,
              modal.targetScope.paymentReference
            )
            .success(function (data) {
              $scope.refresh()
              unsubscribe()
            })
        } else {
          unsubscribe()
        }
      })
      modal.$promise.then(modal.show)
    }

    var EditBillingCollectionStatusController = [
      '$scope',
      '$modal',
      function ($scope, modal) {
        $scope.submit = function (selectedStatus, paymentReference) {
          $scope.selectedStatus = selectedStatus
          $scope.paymentReference = paymentReference

          $scope.$hide()
        }

        $scope.$parent.$on('modal.hide.before', function (modal) {
          modal.targetScope.selectedStatus = $scope.selectedStatus
          modal.targetScope.paymentReference = $scope.paymentReference
        })

        $scope.close = function () {
          $scope.selectedStatus = ''
          $scope.paymentReference = ''
          $scope.$hide()
        }
      },
    ]

    $scope.editBillCollector = function (b) {
      $scope.selectedBill = b

      $scope.collector = ''
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/bill-update-collector.html',
        controller: EditBillingCollectorController,
        show: false,
      })

      var unsubscribe = $scope.$on('modal.hide', function (modal) {
        if (!modal.targetScope.cancelled) {
          if (modal.targetScope.collector === 'unassign') {
            $scope.loadingResults = billingService
              .unassignBillDebtCollector(b.id)
              .success(function (data) {
                $scope.refresh()
                unsubscribe()
              })
          } else {
            $scope.loadingResults = billingService
              .assignBillDebtCollector(
                b.id,
                modal.targetScope.collector,
                modal.targetScope.collectionDate
              )
              .success(function (data) {
                $scope.refresh()
                unsubscribe()
              })
          }
        } else {
          unsubscribe()
        }
      })
      modal.$promise.then(modal.show)
    }

    var EditBillingCollectorController = [
      '$scope',
      '$modal',
      '$filter',
      function ($scope, modal, $filter) {
        $scope.collector = ''
        $scope.collectionDate = ''
        $scope.cancelled = false

        if ($scope.selectedBill.collector) {
          const selectedCollector = $scope.data.collectors.find(
            (c) => c.name + ' ' + c.lastname === $scope.selectedBill.collector
          )
          $scope.collector = $scope.selectedBill ? selectedCollector : ''
        }

        if ($scope.selectedBill.collectionDate) {
          const selectedDate = $scope.selectedBill.collectionDate.split('-')
          $scope.collectionDate = new Date(
            selectedDate[2],
            selectedDate[1],
            selectedDate[0]
          )
        }

        $scope.submit = function (collector, collectionDate) {
          $scope.collector =
            !collector && !collectionDate
              ? 'unassign'
              : collector
              ? collector.id
              : 0
          if (collectionDate) {
            $scope.collectionDate =
              $filter('date')(collectionDate, 'dd-MM-yyyy', '-0300') + ''
          } else {
            $scope.collectionDate = 'null'
          }

          $scope.$hide()
        }

        $scope.$parent.$on('modal.hide.before', function (modal) {
          modal.targetScope.collector = $scope.collector
          modal.targetScope.collectionDate = $scope.collectionDate
          modal.targetScope.cancelled = $scope.cancelled
        })

        $scope.close = function () {
          $scope.collector = ''
          $scope.collectionDate = ''
          $scope.cancelled = true
          $scope.$hide()
        }
      },
    ]

    $scope.refreshFilters()
  },
])
