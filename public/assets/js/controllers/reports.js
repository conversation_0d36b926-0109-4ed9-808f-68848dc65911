app.controller('ReportsController', [
  '$scope',
  '$window',
  '$aside',
  'ReportService',
  'MachineService',
  'BuildingService',
  'AccountService',
  'BillingService',
  function (
    $scope,
    $window,
    $aside,
    reportService,
    machineService,
    buildingService,
    accountService,
    billingService
  ) {
    $scope.results = []

    $scope.filters = {
      orderBy: 'timestamp',
      reverse: true,
    }

    $scope.data = {
      machines: [],
    }

    $scope.clearFilter = function (filter) {
      $scope.filters[filter] = null
    }

    $scope.orderColumn = function (order) {
      $scope.filters.reverse =
        order !== $scope.filters.orderBy ? false : !$scope.filters.reverse
      $scope.filters.orderBy = order
    }

    $scope.loadWithUser = function () {
      accountService.getAccount().then(function (account) {
        $scope.filters.account = account
      })
    }

    $scope.refresh = function () {
      if ($scope.filters.to != null) {
        $scope.filters.to.setHours(23, 59, 59)
      }

      $scope.loadingResults = reportService
        .findUses($scope.filters)
        .then(function () {
          $scope.results = reportService.getUseResults()
          $scope.activeUses = $scope.results.filter(
            (u) => u.accredited && !u.isMaster
          ).length
          $scope.oldestKeepAlive = reportService.getOldestKeepAlive()
          $scope.serialNumber = reportService.getSerialNumber()
        })
    }

    $scope.refreshFilters = function () {
      $scope.loadingFilters = machineService.loadMachines().then(function () {
        $scope.data.machines = machineService.getMachines()
      })

      $scope.loadingFilters = buildingService
        .loadBuildings(1)
        .then(function () {
          $scope.data.buildings = buildingService.getBuildings()
        })
    }

    $scope.getUsesByUnit = function (u, results) {
      const uses = []
      angular.forEach(results, function (result, index) {
        if (result.unit && result.unit.id === u.id) uses.push(result)
      })

      return uses
    }

    $scope.exportToExcel = function () {
      if (!$scope.filters.building) {
        alert('Debe seleccionar un edificio')
        return
      }

      $scope.loadingResults = reportService
        .exportToExcel($scope.filters)
        .then(function (data) {
          const blob = new Blob([data.data], {
            type: 'application/vnd.ms-excel',
          })
          const objectUrl = URL.createObjectURL(blob)
          window.open(objectUrl)
        })
    }

    $scope.bill = function () {
      if (!$scope.filters.building) {
        alert('Debe seleccionar un edificio')
        return
      }

      if (!$scope.filters.from || !$scope.filters.to) {
        alert('Debe seleccionar un rango de fechas')
        return
      }

      if (
        confirm(
          'Esta seguro de que desea generar la facturación?\nSi hay usos anulados no seran incluidos.'
        )
      ) {
        $scope.loadingResults = billingService
          .billBuilding($scope.filters)
          .success(function (data) {
            alert('Se ha generado la facturación')
          })
      }
    }

    $scope.handleBuildingChange = function () {
      $scope.resetBuildingRelatedFilters()
      $scope.getBuilding()
      buildingService.loadUnits($scope.filters.building.id).then(function () {
        $scope.filters.units = buildingService
          .getUnits()
          .sortBy('tower', 'number', true)
      })
      buildingService
        .loadMachines($scope.filters.building.id)
        .then(function () {
          $scope.filters.machines = buildingService.getMachines()
        })
    }

    $scope.resetBuildingRelatedFilters = function () {
      $scope.filters.machine = ''
      $scope.filters.unit = ''
    }

    $scope.getBuilding = function () {
      $scope.loadingBuilding = buildingService
        .getBuildingById($scope.filters.building.id)
        .then(function (data) {
          $scope.filters.building = data.data
        })
    }

    $scope.refreshFilters()
  },
])
