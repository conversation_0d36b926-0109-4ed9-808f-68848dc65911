app.controller('TotemMachineMappingsController', [
  '$scope',
  'TotemService',
  function ($scope, totemService) {
    $scope.buildingsWithMappings = []
    $scope.targetLists = []
    $scope.sourceList = []

    $scope.selectedBuilding = null

    $scope.loadBuildingsWithMappings = function () {
      totemService.loadBuildingsWithMappings().success(function (data) {
        $scope.buildingsWithMappings = data.buildings
      })
    }

    $scope.loadBuildingsWithMappings()

    $scope.updateSelectedBuilding = function () {
      $scope.targetLists = []
      const foundBuilding = $scope.buildingsWithMappings.find(
        (building) => building.id === $scope.selectedBuilding.id
      )
      $scope.sourceList = foundBuilding.machines.map((machine) => {
        return {
          id: machine.id,
          label: `${machine.sortIndex} - ${machine.serialNumber} - ${machine.id}`,
        }
      })

      // target: {
      //      key, // laundromatUserId
      //      list: [
      //          { id, label // sortIndex - serialNumber - id }
      //      ], // machines
      //      label, // user email
      // }
      $scope.targetLists = foundBuilding.totemUsers.map((totemUser) => {
        const list = totemUser.totemUserMachineMappings.map(({ machine }) => {
          return {
            id: machine.id,
            label: `${machine.sortIndex} - ${machine.serialNumber} - ${machine.id}`,
          }
        })
        $scope.sourceList = $scope.sourceList.filter(
          (machine) => list.findIndex((item) => item.id === machine.id) < 0
        )
        return {
          key: totemUser.id,
          label: totemUser.email,
          list,
        }
      })
    }

    $scope.onSave = function (source, targets) {
      const mappings = []
      for (const target of targets) {
        mappings.push({
          totemUserId: target.key,
          machineIds: target.list.map((machine) => machine.id),
        })
      }
      totemService.createTotemPosMachineMappings(mappings)
    }
  },
])
