app.controller('TransactionController', [
  '$scope',
  '$modal',
  '$window',
  '$aside',
  'TransactionService',
  'BuildingService',

  function (
    $scope,
    $modal,
    $window,
    $aside,
    transactionService,
    buildingService
  ) {
    const TRANSACTIONS_QUERY_LEVEL = 2

    $scope.results = []

    $scope.data = {
      buildings: [],
    }

    const to = new Date()
    to.setHours(23, 59, 59)

    $scope.filters = {
      building: {},
      onlySent: true,
      to,
    }

    $scope.dropdownTranslations = {
      translationTexts: {
        buttonDefaultText: 'Seleccionar',
        searchPlaceholder: 'Buscar',
        uncheckAll: 'Borrar selección',
        checkAll: 'Seleccionar todo',
        selectGroup: '',
      },
    }

    $scope.dropdownEvents = {
      building: {
        onDeselectAll: $scope.clearBuildingFilter,
      },
    }

    $scope.dropdownSettings = {
      common: {
        scrollableHeight: '250px',
        scrollable: true,
        styleActive: true,
        showCheckAll: false,
        showUncheckAll: true,
        checkboxes: true,
      },
    }

    $scope.dropdownSettings.building = {
      ...$scope.dropdownSettings.common,
      selectionLimit: 1,
      searchField: 'name',
      enableSearch: true,
      template: '{{option.name}}',
      smartButtonMaxItems: 1,
      smartButtonTextConverter: function (itemText, originalItem) {
        return originalItem.name
      },
    }

    const initPagination = {
      page: 1,
      perPage: 50,
    }

    $scope.pagination = {
      ...initPagination,
      pageSizes: [30, 50, 100],
    }

    $scope.orderColumn = function (order) {
      $scope.filters.orderBy = order

      $scope.filters.reverse =
        order != $scope.filters.order ? true : !$scope.filters.reverse
      $scope.filters.order = order
      $scope.filters.direction = $scope.filters.reverse ? 'asc' : 'desc'

      $scope.refresh()
    }

    $scope.setPageSize = function (size) {
      $scope.pagination.perPage = size || 50
      $scope.refresh($scope.pagination)
    }

    $scope.setPage = function (page) {
      $scope.pagination.page = page || 1
      $scope.refresh($scope.pagination)
    }

    $scope.refreshFilters = function () {
      $scope.loadingBuildings = buildingService
        .loadBuildings(1)
        .then(function () {
          $scope.loadingBuildings = null
          $scope.data.buildings = buildingService.getBuildings()
          $scope.data.buildingOptions = $scope.data.buildings.map(
            ({ id, name }) => {
              return { id, label: name }
            }
          )
        })
    }

    $scope.refresh = function (pagination = initPagination) {
      const filters = {
        ...$scope.filters,
        ...pagination,
        order: $scope.filters.order,
        direction: $scope.filters.direction,
      }

      $scope.loadingResults = transactionService
        .findTransactions(filters, TRANSACTIONS_QUERY_LEVEL)
        .then(function () {
          $scope.results = transactionService.getTransactionResults()
          $scope.pagination = { ...$scope.pagination, ...pagination }
        })
    }

    $scope.exportToCSV = function () {
      transactionService
        .findTransactions(
          {
            building: $scope.filters.building,
            onlySent: $scope.filters.onlySent,
            from: $scope.filters.from,
            to: $scope.filters.to,
            isCSV: true,
          },
          TRANSACTIONS_QUERY_LEVEL
        )
        .then(function () {
          const results = transactionService.getTransactionResults() ?? []
          if (results.length === 0) {
            return
          }

          let csv = ''
          results.forEach((t) => {
            let line = `${t.id},`
            line += `${t.amount},`
            line += `${t.authorizationResultMessage},`
            line += `${t.creationDate},`
            line += `${t.email},`
            line += `${t.origin},`
            line += `${t.uid},`
            line += `${t.serie} - ${t.number},`
            line += `${t.recipientName},`
            line += `${t.comission}`
            line += '\r\n'
            csv += line
          })

          const blob = new Blob([csv], { type: 'text/csv' })
          $scope.url = (window.URL || window.webkitURL).createObjectURL(blob)
          window.location.href = $scope.url
        })
    }

    $scope.refreshFilters()
  },
])
