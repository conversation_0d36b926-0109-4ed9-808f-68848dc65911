app.controller('UsesByBillReportController', [
  '$scope',
  '$window',
  '$aside',
  'LMAPI',
  function ($scope, $window, $aside, api) {
    $scope.results = []

    $scope.filters = {
      billNumber: '',
    }

    $scope.orderColumn = function (order) {
      $scope.filters.reverse =
        order !== $scope.filters.orderBy ? false : !$scope.filters.reverse
      $scope.filters.orderBy = order
    }

    $scope.search = function () {
      if (!$scope.filters.billNumber || $scope.filters.billNumber === '') {
        alert('Parámetros obligatorios: Nro de factura')
        return
      }

      $scope.loadingResults = api
        .get(`/reports/uses/${$scope.filters.billNumber}`)
        .then(function ({ data }) {
          $scope.loadingResults = null
          $scope.results = data.items
          $scope.activeUses = $scope.results.filter(
            (u) => u.accredited && !u.isMaster
          ).length
        })
    }

    $scope.exportToCSV = function () {
      if (!$scope.results?.length) {
        return
      }

      let csv = ''
      $scope.results.forEach((use, index) => {
        let line = `${index},`
        line += `${use.machine.building.name},`
        line += `${use.machine.sortIndex + ' - ' + use.machine.serial_number},`
        line += `${use.typeDesc},`
        line += `${use.unit},`
        line += `${use.card.uuid},`
        line += `${use.date},`
        line += `${use.time},`
        line += `${use.result},`
        line += `${use.accredited ? 'Acreditado' : 'Desacreditado'}`
        line += '\r\n'
        csv += line
      })

      const blob = new Blob([csv], { type: 'text/csv' })
      $scope.url = (window.URL || window.webkitURL).createObjectURL(blob)
      window.location.href = $scope.url
    }
  },
])
