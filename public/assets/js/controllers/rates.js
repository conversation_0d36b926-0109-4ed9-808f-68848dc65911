app.controller('RatesController', [
  '$scope',
  '$modal',
  '$window',
  '$aside',
  'BuildingService',
  'RateService',
  function ($scope, $modal, $window, $aside, buildingService, rateService) {
    // settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: 'Add',
    }

    $scope.filters = {
      quickSearch: '',
    }

    // adding data
    $scope.rates = []

    $scope.refreshRates = function () {
      $scope.loadingRates = buildingService.loadRates(2).then(function (data) {
        $scope.rates = buildingService.getRates()
      })

      $scope.limitDate = new Date()
      $scope.limitDate.setMonth($scope.limitDate.getMonth() + 2)
    }

    // defining rate template
    var formTpl = $aside({
      scope: $scope,
      template: '/rate-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    // methods

    $scope.editRate = function (item) {
      if (item) {
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        showForm()
      }
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        showForm()
      }
    }

    $scope.createRate = function () {
      var item = {
        editing: true,
      }
      $scope.item = item
      $scope.settings.cmd = 'New'
      showForm()
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd == 'New') {
        $scope.errorMessage = null

        $scope.savingRate = buildingService
          .createRate($scope.item)
          .success(function () {
            hideForm()
            $scope.refreshRates()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear el ratio. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      } else if ($scope.settings.cmd == 'Edit') {
        $scope.errorMessage = null

        $scope.savingRate = buildingService
          .updateRate(item.id, item)
          .success(function () {
            hideForm()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido modificar el rate. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      }
    }

    showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
    }

    hideForm = function () {
      formTpl.hide()
      formEventTpl.hide()
    }

    $scope.$on('$destroy', function () {
      hideForm()
    })

    $scope.refreshRates()

    // defining rate event template
    var formEventTpl = $aside({
      scope: $scope,
      template: '/rate-event-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    $scope.addRateEvent = function (item) {
      $scope.errorMessage = null

      if (item) {
        $scope.event = {
          minUsesPerWasher: item.minUsesPerWasher,
          minUsesPerUnit: item.minUsesPerUnit,
          priceCustomer: item.priceCustomer,
          priceCompany: item.priceCompany,
          priceM3: item.priceM3,
          priceKWh: item.priceKWh,
          priceCardReplacement: item.priceCardReplacement,
          validFrom: item.validFrom,
          validUntil: item.validUntil,
          rate: item,
        }

        showEventForm()
      }
    }

    $scope.saveEvent = function (event) {
      $scope.errorMessage = null

      $scope.fixEventDates(event)
      if (event.cmd == 'Edit') {
        $scope.savingEvent = buildingService
          .editRateEvent(event)
          .success(function () {
            hideForm()
            $scope.refreshRates()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido editar el rango para el ratio. ' + reason
                ? reason
                : ''
          })
      } else {
        $scope.savingEvent = buildingService
          .createRateEvent(event)
          .success(function () {
            hideForm()
            $scope.refreshRates()
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear el nuevo rango para el ratio. ' + reason
                ? reason
                : ''
          })
      }
    }

    showEventForm = function (event) {
      if (event) {
        $scope.event = event
      }

      angular.element('.tooltip').remove()
      formEventTpl.show()
    }

    $scope.fixEventDates = function (event) {
      if (!event) {
        return
      }

      const validFromDate = new Date(event.validFrom)
      const validUntilDate = new Date(event.validUntil)

      const userTimezoneOffset = validFromDate.getTimezoneOffset() * 60000
      const validFromUTCFixed = new Date(
        validFromDate.getTime() - userTimezoneOffset
      )
      event.validFrom = validFromUTCFixed

      const validUntilUTCFixed = new Date(
        validUntilDate.getTime() - userTimezoneOffset
      )
      event.validUntil = validUntilUTCFixed
    }

    var RatesEventController = [
      '$scope',
      'BillingService',
      function ($scope, BillingService) {
        $scope.refreshRateEvents = function () {
          $scope.loadHistory = buildingService
            .getRateHistory($scope.selectedRate.id)
            .then(function (data) {
              $scope.events = data.data
            })
        }

        $scope.close = function () {
          $scope.$hide()
        }

        $scope.editRateHistory = function (rateEvent) {
          if (rateEvent) {
            $scope.close()

            rateEvent.cmd = 'Edit'
            showEventForm(rateEvent)
          }
        }

        $scope.deleteRateHistory = function (rateEvent) {
          if (rateEvent && confirm('Esta segunto de eliminar este evento?')) {
            buildingService
              .deleteRateHistory(rateEvent.rate?.id, rateEvent.id)
              .success(function () {
                $scope.refreshRateEvents()
              })
          }
        }

        $scope.refreshRateEvents()
      },
    ]

    $scope.viewHistory = function (rate) {
      if (rate) {
        $scope.selectedRate = rate

        var modal = $modal({
          scope: $scope,
          template: 'assets/tpl/partials/rate-history.html',
          controller: RatesEventController,
          show: false,
        })

        modal.$promise.then(modal.show)
      }
    }

    $scope.notifyNewRate = function (rate) {
      if (rate) {
        rateService.notifyNewRate(rate.id).then(function () {
          alert('Notificación enviada.')
          $scope.refreshRates()
        })
      }
    }
  },
])
