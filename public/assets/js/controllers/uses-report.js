app.controller('UsesReportController', [
  '$scope',
  '$window',
  '$aside',
  'ReportService',
  'MachineService',
  'BuildingService',
  function (
    $scope,
    $window,
    $aside,
    reportService,
    machineService,
    buildingService
  ) {
    $scope.results = []

    $scope.filters = {
      orderBy: 'timestamp',
      reverse: true,
      machines: [],
      units: [],
      building: {},
      daysOfWeek: [],
      to: new Date(),
    }

    $scope.data = {
      machines: [],
      buildings: [],
      units: [],
      daysOfWeek: [
        { id: 1, label: '<PERSON><PERSON>' },
        { id: 2, label: 'Mart<PERSON>' },
        { id: 3, label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
        { id: 4, label: 'Jueves' },
        { id: 5, label: '<PERSON><PERSON><PERSON>' },
        { id: 6, label: 'Sábad<PERSON>' },
        { id: 7, label: 'Domingo' },
      ],
    }

    $scope.orderColumn = function (order) {
      $scope.filters.reverse =
        order !== $scope.filters.orderBy ? false : !$scope.filters.reverse
      $scope.filters.orderBy = order
    }

    $scope.search = function () {
      if (
        !$scope.filters.from ||
        !$scope.filters.to ||
        (!$scope.filters.building?.id && !$scope.filters.card)
      ) {
        alert(
          'Parámetros obligatorios: Fecha desde, fecha hasta, edificio o tarjeta'
        )
        return
      }

      $scope.filters.to.setHours(23, 59, 59)

      $scope.loadingResults = reportService
        .findUses($scope.filters)
        .then(function () {
          $scope.loadingResults = null
          $scope.results = reportService.getUseResults()
          $scope.activeUses = $scope.results.filter(
            (u) => u.accredited && !u.isMaster
          ).length
          $scope.oldestKeepAlive = reportService.getOldestKeepAlive()
          $scope.serialNumber = reportService.getSerialNumber()
        })
    }

    $scope.refreshFilters = function () {
      $scope.loadingBuildings = buildingService
        .loadBuildings(1)
        .then(function () {
          $scope.loadingBuildings = null
          $scope.data.buildings = buildingService.getBuildings()
          $scope.data.buildingOptions = $scope.data.buildings.map(
            ({ id, name }) => {
              return { id, label: name }
            }
          )
        })
    }

    $scope.endMaintenanceUse = function (use) {
      if (use.isMaintenance) {
        reportService.discreditUse(use.id, 'Mantenimiento terminado')
        use.isMaintenance = !use.isMaintenance
      }
    }

    $scope.modifyAccreditUse = function (use) {
      if (!use.accredited) {
        reportService.accreditUse(use.id)
        use.accredited = !use.accredited
      } else {
        const reason = prompt(
          '¿Esta seguro de anular el uso? \n\nIngrese una razón (opcional): ',
          ''
        )
        if (reason != null) {
          reportService.discreditUse(use.id, reason)
          use.accredited = !use.accredited
        }
      }
    }

    $scope.exportToCSV = function () {
      if (!$scope.results?.length) {
        return
      }

      let csv = ''
      $scope.results.forEach((use, index) => {
        let line = `${index},`
        line += `${use.machine.building.name},`
        line += `${use.machine.sortIndex + ' - ' + use.machine.serial_number},`
        line += `${use.typeDesc},`
        line += `${use.unit},`
        line += `${use.card.uuid},`
        line += `${use.date},`
        line += `${use.time},`
        line += `${use.result},`
        line += `${use.accredited ? 'Acreditado' : 'Desacreditado'}`
        line += '\r\n'
        csv += line
      })

      const blob = new Blob([csv], { type: 'text/csv' })
      $scope.url = (window.URL || window.webkitURL).createObjectURL(blob)
      window.location.href = $scope.url
    }

    $scope.handleBuildingChange = function () {
      $scope.resetBuildingRelatedFilters()
      $scope.getBuilding()

      $scope.loadingUnits = buildingService
        .loadUnits($scope.filters.building.id)
        .then(function () {
          $scope.loadingUnits = null
          $scope.data.units = buildingService
            .getUnits()
            .sortBy('tower', 'number', true)
        })

      $scope.loadingMachines = buildingService
        .loadMachines($scope.filters.building.id)
        .then(function () {
          $scope.loadingMachines = null
          $scope.data.machines = buildingService.getMachines()
        })
    }

    $scope.resetBuildingRelatedFilters = function () {
      $scope.filters.machines = []
      $scope.filters.units = []
    }

    $scope.getBuilding = function () {
      $scope.loadingBuilding = buildingService
        .getBuildingById($scope.filters.building.id)
        .then(function (data) {
          $scope.loadingBuilding = null
          $scope.filters.building = data.data
        })
    }

    $scope.dropdownTranslations = {
      translationTexts: {
        buttonDefaultText: 'Seleccionar',
        searchPlaceholder: 'Buscar',
        uncheckAll: 'Borrar selección',
        checkAll: 'Seleccionar todo',
        selectGroup: '',
      },
    }

    $scope.clearBuildingFilter = function () {
      $scope.resetBuildingRelatedFilters()
      $scope.data.machines = $scope.allMachines
    }

    $scope.dropdownEvents = {
      building: {
        onItemSelect: $scope.handleBuildingChange,
        onDeselectAll: $scope.clearBuildingFilter,
      },
    }

    $scope.dropdownSettings = {
      common: {
        scrollableHeight: '250px',
        scrollable: true,
        styleActive: true,
        showCheckAll: false,
        showUncheckAll: true,
        checkboxes: true,
      },
    }

    $scope.dropdownSettings.building = {
      ...$scope.dropdownSettings.common,
      selectionLimit: 1,
      searchField: 'name',
      enableSearch: true,
      template: '{{option.name}}',
      smartButtonMaxItems: 1,
      smartButtonTextConverter: function (itemText, originalItem) {
        return originalItem.name
      },
    }

    $scope.dropdownSettings.unit = {
      ...$scope.dropdownSettings.common,
      searchField: 'name',
      enableSearch: true,
      template: '{{option.name}}',
      smartButtonMaxItems: 3,
      smartButtonTextConverter: function (itemText, originalItem) {
        return originalItem.name
      },
    }

    $scope.dropdownSettings.machine = {
      ...$scope.dropdownSettings.common,
      searchField: 'serial_number',
      enableSearch: true,
      template:
        '{{option.sortIndex}} - {{option.serial_number}} - {{option.machine_type_es}}',
      selectByGroups: ['LAVA', 'SECA'],
      groupByTextProvider: function (groupValue) {
        switch (groupValue) {
          case 'LAVA':
            return 'SOLO LAVA'
          case 'SECA':
            return 'SOLO SECA'
          default:
            return 'X'
        }
      },
      groupBy: 'machine_type_es',
      smartButtonMaxItems: 3,
      smartButtonTextConverter: function (itemText, originalItem) {
        return `${originalItem.sortIndex} - ${originalItem.serial_number} - ${originalItem.machine_type_es}`
      },
    }

    $scope.dropdownSettings.daysOfWeek = {
      ...$scope.dropdownSettings.common,
      showCheckAll: true,
    }

    $scope.refreshFilters()
  },
])
