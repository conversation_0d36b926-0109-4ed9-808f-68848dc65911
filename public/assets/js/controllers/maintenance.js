app.controller('MaintenanceController', [
  '$scope',
  '$filter',
  '$location',
  'MaintenanceService',
  'chroma',
  function ($scope, $filter, $location, maintenanceService, chroma) {
    $scope.results = []
    $scope.averages = []
    $scope.indicators = {}
    $scope.isLoading = false

    $scope.decimalOrderedColumns = [
      'average30Days',
      'average6Months',
      'average1Year',
    ]

    $scope.orderColumn = function (order) {
      try {
        if ($scope.decimalOrderedColumns.includes(order)) {
          $scope.comparator = $scope.orderByDecimal
        } else {
          $scope.comparator = undefined
        }
        $scope.filters.reverse =
          order != $scope.filters.orderBy ? false : !$scope.filters.reverse

        $scope.filters.orderBy = order
      } catch (error) {
        console.log(error)
      }
    }

    $scope.orderByDecimal = function (a, b) {
      return a.value.localeCompare(b.value, undefined, {
        numerical: true,
        sensitivity: 'base',
      })
    }

    $scope.data = {
      types: ['MP100', 'MP500', 'MP1200'],
    }

    $scope.departments = [
      { key: '', text: 'Todos', value: [] },
      {
        key: 'montevideo-colonia',
        text: 'Montevideo y Canelones',
        value: ['Montevideo', 'Canelones'],
      },
      { key: 'maldonado', text: 'Maldonado', value: ['Maldonado'] },
      {
        key: 'otros',
        text: 'Colonia y Otros',
        value: [
          'Colonia',
          'Santa Catarina',
          'Curitiba',
          'Asunción',
          'Florida',
          'Paysandú',
        ],
      },
    ]

    $scope.filters = {
      quickSearch: '',
      department: $scope.departments[0],
      orderBy: 'buildingId',
      reverse: false,
      comparator: undefined,
    }

    $scope.selectedDepartment = function (department) {
      $scope.averages = []
      $scope.results = $scope.unfilteredResults.filter((preventive) => {
        const found =
          !department.value.length ||
          department.value.some((f) => f === preventive.department)

        if (found) {
          const average = $scope.unfilteredAverages.find(
            (average) => average.buildingId === preventive.buildingId
          )
          $scope.averages.push(average)
        }

        return found
      })

      $scope.buildIndicators()
    }

    $scope.refresh = function () {
      $scope.isLoading = true
      $scope.loadingResults = maintenanceService
        .findPreventiveMaintenance()
        .then(function () {
          const maintenances =
            maintenanceService.getPreventiveMaintenanceResults()

          const averages = maintenanceService.getBuildingAverageUsesResults()

          const grouped = []

          for (let i = 0; i < maintenances.length; ) {
            const curr = maintenances[i]
            const toAdd = $scope.buildBase(curr)

            const average = averages.find(
              (a) => a.buildingId === toAdd.buildingId
            )

            $scope.buildAverage(toAdd, average)

            if (curr.maintenanceType === 'MP100') {
              $scope.buildMP100(toAdd, curr)
            } else if (curr.maintenanceType === 'MP500') {
              $scope.buildMP500(toAdd, curr)
            } else if (curr.maintenanceType === 'MP1200') {
              $scope.buildMP1200(toAdd, curr)
            }

            i++
            let next = maintenances[i]
            if (next && next.buildingId === toAdd.buildingId) {
              if (next.maintenanceType === 'MP100') {
                $scope.buildMP100(toAdd, next)
              } else if (next.maintenanceType === 'MP500') {
                $scope.buildMP500(toAdd, next)
              } else if (next.maintenanceType === 'MP1200') {
                $scope.buildMP1200(toAdd, next)
              }
            } else {
              grouped.push(toAdd)
              continue
            }

            i++
            next = maintenances[i]
            if (next && next.buildingId === toAdd.buildingId) {
              if (next.maintenanceType === 'MP100') {
                $scope.buildMP100(toAdd, next)
              } else if (next.maintenanceType === 'MP500') {
                $scope.buildMP500(toAdd, next)
              } else if (next.maintenanceType === 'MP1200') {
                $scope.buildMP1200(toAdd, next)
              }
            } else {
              grouped.push(toAdd)
              continue
            }
            i++

            grouped.push(toAdd)
          }

          $scope.unfilteredResults = grouped
          $scope.results = grouped
          $scope.unfilteredAverages = averages
          $scope.averages = averages
          $scope.buildIndicators()

          $scope.isLoading = false
          return null
        })
    }

    $scope.buildBase = function (maintenance) {
      const toAdd = {}
      toAdd.buildingId = maintenance.buildingId
      toAdd.buildingName = maintenance.buildingName
      toAdd.department = maintenance.department
      toAdd.parameter = {}
      return toAdd
    }

    $scope.buildAverage = function (toAdd, average) {
      if (average) {
        toAdd.average30Days = Number.parseFloat(average.average30Days)
        toAdd.average6Months = Number.parseFloat(average.average6Months)
        toAdd.average1Year = Number.parseFloat(average.average1Year)
      } else {
        toAdd.average30Days = 0
        toAdd.average6Months = 0
        toAdd.average1Year = 0
      }
    }

    $scope.buildMP100 = function (toAdd, maintenance) {
      if (!maintenance) {
        return
      }
      toAdd.parameter.mp100 = maintenance.maintenanceParameter.mp100
      toAdd.mp100 = toAdd.parameter.mp100 - maintenance.uses
      toAdd.mp100Tech = maintenance.technician
      toAdd.mp100Date = maintenance.maintenanceDate?.split(' ')[0] || ''
      toAdd.mp100Class = $scope.getClass(
        maintenance.maintenanceParameter.mp100,
        maintenance.uses,
        maintenance.maintenanceType
      )
    }

    $scope.buildMP500 = function (toAdd, maintenance) {
      if (!maintenance) {
        return
      }

      toAdd.parameter.mp500 = maintenance.maintenanceParameter.mp500
      toAdd.mp500 = toAdd.parameter.mp500 - maintenance.uses
      toAdd.mp500Tech = maintenance.technician
      toAdd.mp500Date = maintenance.maintenanceDate?.split(' ')[0] || ''
      toAdd.mp500Class = $scope.getClass(
        maintenance.maintenanceParameter.mp500,
        maintenance.uses,
        maintenance.maintenanceType
      )
    }

    $scope.buildMP1200 = function (toAdd, maintenance) {
      if (!maintenance) {
        return
      }

      toAdd.parameter.mp1200 = maintenance.maintenanceParameter.mp1200
      toAdd.mp1200 = toAdd.parameter.mp1200 - maintenance.uses
      toAdd.mp1200Tech = maintenance.technician
      toAdd.mp1200Date = maintenance.maintenanceDate?.split(' ')[0] || ''
      toAdd.mp1200Class = $scope.getClass(
        maintenance.maintenanceParameter.mp1200,
        maintenance.uses,
        maintenance.maintenanceType
      )
    }

    $scope.getClass = function (parameter, uses, mp) {
      let result = parameter - uses
      let min, mid, max
      switch (mp) {
        case 'MP100':
          min = 0
          mid = 25
          max = 50
          break
        case 'MP500':
          min = 50
          mid = 200
          max = 500
          break
        case 'MP1200':
          min = -300
          mid = 200
          max = 1000
          break
      }
      if (result < min) {
        result = min
      }
      if (result > max) {
        result = max
      }

      return $scope.perc2color(result, min, mid, max)
    }

    $scope.perc2color = function (x, min, mid, max) {
      const scale = chroma.chroma
        .scale(['red', 'yellow', 'green'])
        .domain([min, mid, max])

      return scale(x).hex()
    }

    $scope.showBuilding = function (buildingId) {
      $location.path('/maintenance/preventive/' + buildingId)
    }

    $scope.buildIndicators = function () {
      $scope.indicators.totalBuildings = $scope.results.length
      $scope.indicators.totalOperativeMachines = $scope.averages.reduce(
        (total, average) => total + average.dryerCount + average.washerCount,
        0
      )

      $scope.indicators.mp100Below0 = Math.round(
        ($scope.results.filter((result) => result.mp100 < 0).length /
          $scope.results.length) *
          100
      )
      $scope.indicators.mp1200Below0 = Math.round(
        ($scope.results.filter((result) => result.mp1200 < 0).length /
          $scope.results.length) *
          100
      )
      $scope.resetMpCount()
      angular.forEach($scope.results, function (result) {
        $scope.countMp100(result.parameter.mp100 - result.mp100)
        $scope.countMp500(result.parameter.mp500 - result.mp500)
        $scope.countMp1200(result.parameter.mp1200 - result.mp1200)

        $scope.indicators.averageMp100 += result.mp100
        $scope.indicators.averageMp500 += result.mp500
        $scope.indicators.averageMp1200 += result.mp1200
      })

      $scope.indicators.averageMp100 = Math.round(
        $scope.indicators.averageMp100 / $scope.results.length
      )
      $scope.indicators.averageMp500 = Math.round(
        $scope.indicators.averageMp500 / $scope.results.length
      )
      $scope.indicators.averageMp1200 = Math.round(
        $scope.indicators.averageMp1200 / $scope.results.length
      )
    }

    $scope.countMp100 = function (mp100) {
      switch (true) {
        case mp100 <= 50:
          $scope.indicators.mp100Under50++
          break
        case mp100 <= 70:
          $scope.indicators.mp100Under70++
          break
        case mp100 <= 100:
          $scope.indicators.mp100Under100++
          break
        case mp100 > 100:
          $scope.indicators.mp100Over100++
          break
        default:
          console.log(`Debug - mp100 is ${mp100}`)
          break
      }
    }

    $scope.countMp500 = function (mp500) {
      switch (true) {
        case mp500 <= 100:
          $scope.indicators.mp500Under100++
          break
        case mp500 <= 500:
          $scope.indicators.mp500Under500++
          break
        case mp500 <= 600:
          $scope.indicators.mp500Under600++
          break
        case mp500 > 600:
          $scope.indicators.mp500Over600++
          break
        default:
          console.log(`Debug - mp100 is ${mp500}`)
          break
      }
    }

    $scope.countMp1200 = function (mp1200) {
      switch (true) {
        case mp1200 <= 500:
          $scope.indicators.mp1200Under500++
          break
        case mp1200 <= 1000:
          $scope.indicators.mp1200Under1000++
          break
        case mp1200 <= 1500:
          $scope.indicators.mp1200Under1500++
          break
        case mp1200 > 1500:
          $scope.indicators.mp1200Over1500++
          break
        default:
          console.log(`Debug - mp100 is ${mp1200}`)
          break
      }
    }

    $scope.resetMpCount = function () {
      $scope.indicators.mp100Under50 = 0
      $scope.indicators.mp100Under70 = 0
      $scope.indicators.mp100Under100 = 0
      $scope.indicators.mp100Over100 = 0
      $scope.indicators.mp500Under100 = 0
      $scope.indicators.mp500Under500 = 0
      $scope.indicators.mp500Under600 = 0
      $scope.indicators.mp500Over600 = 0
      $scope.indicators.mp1200Under500 = 0
      $scope.indicators.mp1200Under1000 = 0
      $scope.indicators.mp1200Under1500 = 0
      $scope.indicators.mp1200Over1500 = 0
      $scope.indicators.averageMp100 = 0
      $scope.indicators.averageMp500 = 0
      $scope.indicators.averageMp1200 = 0
    }

    $scope.invalidateCache = function () {
      $scope.loadingResults = true
      maintenanceService.invalidateCache().then(function () {
        $scope.refresh()
      })
    }

    $scope.refresh()
  },
])
