app.controller('MaintenanceDetailController', [
  '$scope',
  '$location',
  '$routeParams',
  'MaintenanceDetailService',
  'BuildingService',
  'chroma',
  function (
    $scope,
    $location,
    $routeParams,
    maintenanceDetailService,
    buildingService,
    chroma
  ) {
    $scope.washerResults = []
    $scope.dryerResults = []

    $scope.filtersWasher = {
      quickSearch: '',
      department: '',
      orderBy: 'sort',
    }

    $scope.filtersDryer = {
      quickSearch: '',
      department: '',
      orderBy: 'sort',
    }

    $scope.orderColumnWasher = function (order) {
      $scope.filtersWasher.reverse =
        order != $scope.filtersWasher.orderBy
          ? false
          : !$scope.filtersWasher.reverse
      $scope.filtersWasher.orderBy = order
    }

    $scope.orderColumnDryer = function (order) {
      $scope.filtersDryer.reverse =
        order != $scope.filtersDryer.orderBy
          ? false
          : !$scope.filtersDryer.reverse
      $scope.filtersDryer.orderBy = order
    }

    $scope.data = {
      types: ['MP100', 'MP500', 'MP1200'],
    }

    $scope.refresh = function () {
      if (!$routeParams.bid) {
        return $location.path('/maintenance/preventive')
      }

      buildingService
        .getBuildingById($routeParams.bid)
        .then(function (building) {
          $scope.building = building.data
        })

      $scope.loadingResults = maintenanceDetailService
        .findPreventiveMaintenanceByBuilding($routeParams.bid)
        .then(function () {
          const results =
            maintenanceDetailService.getPreventiveMaintenanceResults()
          const averages =
            maintenanceDetailService.getMachineAverageUsesResults()

          const groupedWasher = []
          const groupedDyer = []
          for (let i = 0; i < results.length; i++) {
            const curr = results[i]

            const toAdd = $scope.buildBase(curr)

            const average = averages.find(
              (a) => a.machineId === toAdd.machineId
            )
            $scope.buildAverage(toAdd, average)

            if (curr.machineType === 'WASHER') {
              if (curr.maintenanceType === 'MP100') {
                $scope.buildMP100(toAdd, curr)
              } else if (curr.maintenanceType === 'MP500') {
                $scope.buildMP500(toAdd, curr)
              }
            } else if (curr.machineType === 'DRYER') {
              $scope.buildMP1200(toAdd, curr)
            }

            let next = results[i + 1]
            if (
              next &&
              next.machineId === toAdd.machineId &&
              next.machineType === 'WASHER'
            ) {
              if (next.maintenanceType === 'MP100') {
                $scope.buildMP100(toAdd, next)
              } else if (next.maintenanceType === 'MP500') {
                $scope.buildMP500(toAdd, next)
              }
              i++
            }

            if (toAdd.machineType === 'WASHER') {
              groupedWasher.push(toAdd)
            } else if (toAdd.machineType === 'DRYER') {
              groupedDyer.push(toAdd)
            }
          }

          $scope.washerResults = groupedWasher
          $scope.dryerResults = groupedDyer
        })
    }

    $scope.buildBase = function (maintenance) {
      const toAdd = {}
      toAdd.machineId = maintenance.machineId
      toAdd.sort = maintenance.sort
      toAdd.reference = maintenance.reference
      toAdd.serialNumber = maintenance.serialNumber
      toAdd.model = maintenance.model
      toAdd.machineType = maintenance.machineType
      toAdd.parameter = maintenance.maintenanceParameter
      return toAdd
    }

    $scope.buildAverage = function (toAdd, average) {
      if (average) {
        toAdd.average30Days = Number.parseFloat(average.average30Days).toFixed(
          2
        )
        toAdd.average6Months = Number.parseFloat(
          average.average6Months
        ).toFixed(2)
        toAdd.average1Year = Number.parseFloat(average.average1Year).toFixed(2)
        toAdd.totalUses = average.totalUses
      } else {
        toAdd.average30Days = 0
        toAdd.average6Months = 0
        toAdd.average1Year = 0
        toAdd.totalUses = 0
      }
    }

    $scope.buildMP100 = function (toAdd, maintenance) {
      toAdd.mp100 = maintenance.uses
      toAdd.mp100Tech = maintenance.technician
      toAdd.mp100Date = maintenance.maintenanceDate?.split(' ')[0]
      toAdd.mp100Class = $scope.getClass(
        maintenance.maintenanceParameter.mp100,
        maintenance.uses,
        'MP100'
      )
    }

    $scope.buildMP500 = function (toAdd, maintenance) {
      toAdd.MP500 = maintenance.uses
      toAdd.mp500Tech = maintenance.technician
      toAdd.mp500Date = maintenance.maintenanceDate?.split(' ')[0]
      toAdd.mp500Class = $scope.getClass(
        maintenance.maintenanceParameter.MP500,
        maintenance.uses,
        'MP500'
      )
    }

    $scope.buildMP1200 = function (toAdd, maintenance) {
      toAdd.mp1200 = maintenance.uses
      toAdd.mp1200Tech = maintenance.technician
      toAdd.mp1200Date = maintenance.maintenanceDate?.split(' ')[0]
      toAdd.mp1200Class = $scope.getClass(
        maintenance.maintenanceParameter.mp1200,
        maintenance.uses,
        'MP1200'
      )
    }

    $scope.getClass = function (parameter, uses, mp) {
      let result = parameter - uses
      let min, mid, max
      switch (mp) {
        case 'MP100':
          min = 0
          mid = 20
          max = 100
          break
        case 'MP500':
          min = 50
          mid = 100
          max = 320
          break
        case 'MP1200':
          min = -300
          mid = 0
          max = 1000
          break
      }
      if (result < min) {
        result = min
      }
      if (result > max) {
        result = max
      }

      return $scope.perc2color(result, min, mid, max)
    }

    $scope.perc2color = function (x, min, mid, max) {
      const scale = chroma.chroma
        .scale(['red', 'yellow', 'green'])
        .domain([min, mid, max])

      return scale(x).hex()
    }

    $scope.showBuilding = function () {
      $location.path('/buildings/' + $routeParams.bid)
    }

    $scope.refresh()
  },
])
