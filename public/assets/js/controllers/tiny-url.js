app.controller('TinyUrlController', [
  '$scope',
  '$window',
  '$aside',
  'TinyUrlService',
  'CopyService',
  function ($scope, $window, $aside, tinyUrlService, copyService) {
    const TINY_URLS_QUERY_LEVEL = 2

    // TODO: add order functionality

    $scope.data = {
      entities: [],
    }

    $scope.toast = {
      text: '',
      type: '',
    }

    $scope.filters = {
      quickSearch: '',
      orderBy: 'id',
    }

    $scope.settings = {
      cmd: '',
    }

    $scope.refreshEntities = function () {
      $scope.loadingEntities = tinyUrlService
        .getTinyUrls(TINY_URLS_QUERY_LEVEL)
        .then(function (data) {
          $scope.data.entities = data.data.tinyUrls
        })
    }

    $scope.refreshAll = function () {
      $scope.refreshEntities()
    }

    $scope.refreshAll()

    /* ----------- entity sort ----------- */

    $scope.orderColumn = function (order) {
      $scope.filters.reverse =
        order != $scope.filters.orderBy ? false : !$scope.filters.reverse
      $scope.filters.orderBy = order
    }

    /* ----------- entity form ----------- */

    const FORM_CMD = {
      edit: 'Edit',
      new: 'New',
    }

    const formTpl = $aside({
      scope: $scope,
      template: '/tiny-url-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    $scope.showForm = () => {
      $scope.refreshFormData()

      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = () => {
      formTpl.hide()
    }

    $scope.$on('$destroy', () => {
      $scope.hideForm()
    })

    $scope.refreshFormData = () => {}

    $scope.submitEntity = (entity) => {
      if ($scope.settings.cmd == FORM_CMD.new) {
        $scope.saveEntity(entity)
      } else if ($scope.settings.cmd == FORM_CMD.edit) {
        $scope.updateEntity(entity)
      }
    }

    $scope.clearError = () => {
      $scope.errorMessage = ''
    }

    /* ----------- create entity ----------- */

    $scope.createEntity = () => {
      $scope.entity = {
        editing: true,
      }

      $scope.settings.cmd = FORM_CMD.new

      $scope.showForm()
    }

    $scope.saveEntity = (entity) => {
      $scope.errorMessage = null

      $scope.submittingEntity = tinyUrlService
        .createTinyUrl($scope.entity)
        .success(() => {
          $scope.hideForm()

          $scope.refreshEntities()
        })
        .error((reason) => {
          $scope.errorMessage = `No se ha podido crear la URL. ${
            reason?.result_detail ?? ''
          }`
        })
    }

    /* ----------- bulk entity creation ----------- */

    $scope.createEntities = () => {
      $scope.errorMessage = null

      const count = parseInt(prompt('Cuantas URLs desea crear?', 10))

      if (!count || typeof count !== 'number') {
        $scope.toast.text = 'Ingrese un valor numerico mayor a 0.'
        $scope.toast.type = 'Warning'
        return
      }

      $scope.submittingEntity = tinyUrlService
        .createBulkTinyUrls(count)
        .success(() => {
          $scope.toast.text = 'Operación realizada con éxito!'
          $scope.toast.type = 'Success'
        })
        .error((reason) => {
          $scope.toast.text = `No se ha podido crear las URLs. ${
            reason?.result_detail ?? ''
          }`
          $scope.toast.type = 'Error'
        })
    }

    /* ----------- edit entity ----------- */

    $scope.editEntity = (entity) => {
      if (!entity) return

      $scope.entity = entity
      $scope.entity.editing = true
      $scope.settings.cmd = FORM_CMD.edit

      $scope.showForm()
    }

    $scope.updateEntity = (entity) => {
      $scope.errorMessage = null

      $scope.submittingEntity = tinyUrlService
        .updateTinyUrl(entity.id, entity)
        .success(() => {
          $scope.hideForm()
        })
        .error((reason) => {
          $scope.errorMessage = `No se ha podido actualizar la URL. ${
            reason?.result_detail ?? ''
          }`
        })
    }

    /* ----------- activate entity ----------- */

    $scope.toggleActivation = (entity) => {
      if (entity.isActive && !entity.destinationUrl) {
        $scope.toast.text =
          'No se puede activar una URL sin URL DESTINO definida!'
        $scope.toast.type = 'Warning'
        entity.isActive = false
        return
      }

      $scope.submittingEntity = tinyUrlService
        .updateTinyUrl(entity.id, { isActive: entity.isActive })
        .success(() => {
          $scope.toast.text = 'Actualizada con éxito!'
          $scope.toast.type = 'Success'
        })
        .error((reason) => {
          $scope.toast.text = `No se ha podido actualizar la URL. ${
            reason?.result_detail ?? ''
          }`
          $scope.toast.type = 'Error'
        })
    }

    /* ----------- copy action ----------- */

    $scope.copy = function (event, value) {
      copyService.copy(value).then(function () {
        const element = event.target
        element.classList.add('text-success')
        setTimeout(function () {
          element.classList.remove('text-success')
        }, 2000)

        $scope.toast.text = 'Copiado!'
        $scope.toast.type = 'Info'
      })
    }
  },
])
