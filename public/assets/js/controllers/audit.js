app.controller('AuditController', [
  '$scope',
  '$window',
  '$aside',
  'AuditService',
  function ($scope, $window, $aside, auditService) {
    $scope.results = []

    $scope.filters = {}

    $scope.data = {}

    const initPagination = {
      page: 1,
      perPage: 50,
    }

    $scope.pagination = {
      ...initPagination,
      pageSizes: [30, 50, 100],
    }

    $scope.orderColumn = function (order) {
      $scope.filters.orderBy = order

      $scope.filters.reverse =
        order != $scope.filters.order ? true : !$scope.filters.reverse
      $scope.filters.order = order
      $scope.filters.direction = $scope.filters.reverse ? 'asc' : 'desc'

      $scope.refresh()
    }

    $scope.setPageSize = function (size) {
      $scope.pagination.perPage = size || 50
      $scope.refresh($scope.pagination)
    }

    $scope.setPage = function (page) {
      $scope.pagination.page = page || 1
      $scope.refresh($scope.pagination)
    }

    $scope.refresh = function (pagination = initPagination) {
      const filters = {
        ...$scope.filters,
        ...pagination,
        order: $scope.filters.order,
        direction: $scope.filters.direction,
      }

      $scope.loadingResults = auditService
        .findAudits(filters)
        .then(function () {
          $scope.results = auditService.getAuditResults()
          $scope.pagination = { ...$scope.pagination, ...pagination }
        })
    }
  },
])
