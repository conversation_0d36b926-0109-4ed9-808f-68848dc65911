app.controller('MachineModelController', [
  '$scope',
  '$window',
  '$aside',
  '$modal',
  'MachineModelService',
  function ($scope, $window, $aside, $modal, machineModelService) {
    const NEW = 'New'
    const ADD = 'Add'
    const EDIT = 'Edit'
    const VIEW = 'View'

    // settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: ADD,
    }

    $scope.filters = {
      quickSearch: '',
    }

    $scope.machineModels = []

    $scope.refreshMachineModels = function () {
      $scope.loadingMachineModel = machineModelService
        .loadMachineModels()
        .then(function (data) {
          $scope.machineModels = machineModelService.getMachineModels()
        })
    }

    var formTpl = $aside({
      scope: $scope,
      template: '/machine-model-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    // methods
    $scope.checkAll = function () {
      angular.forEach($scope.data, function (item) {
        item.selected = !item.selected
      })
    }

    $scope.editMachineModel = function (item) {
      if (!item) {
        return
      }

      item.editing = true
      $scope.item = item
      $scope.settings.cmd = EDIT
      $scope.showForm()
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = VIEW
        $scope.showForm()
      }
    }

    $scope.createMachineModel = function () {
      var item = {
        editing: true,
      }
      $scope.item = item
      $scope.settings.cmd = NEW
      $scope.showForm()
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd === NEW) {
        $scope.addItem()
      } else if ($scope.settings.cmd === EDIT) {
        $scope.editItem()
      }
    }

    $scope.addItem = function () {
      $scope.errorMessage = null
      $scope.savingMachineModel = machineModelService
        .createMachineModel($scope.item)
        .success(function () {
          $scope.hideForm()
          $scope.refreshMachineModels()
        })
        .error(function (reason) {
          $scope.errorMessage =
            'No se ha podido crear el Modelo de máquina. ' +
            reason.result_detail
              ? reason.result_detail
              : ''
        })
    }

    $scope.editItem = function () {
      $scope.errorMessage = null
      $scope.savingMachineModel = machineModelService
        .updateMachineModel(item.id, item)
        .success(function () {
          $scope.hideForm()
        })
        .error(function (reason) {
          $scope.errorMessage =
            'No se ha podido modificar la administración. ' +
            reason.result_detail
              ? reason.result_detail
              : ''
        })
    }

    $scope.deleteMachineModel = function (item) {
      if (confirm('Estás seguro?')) {
        machineModelService.deleteMachineModel(item.id).success(function () {
          $scope.refreshMachineModels()
        })
      }
    }

    $scope.sendRefreshTokenReminder = function (item) {
      if (confirm('Estás seguro?')) {
        machineModelService
          .sendRefreshTokenReminder(item.id)
          .success(function () {})
      }
    }

    $scope.showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = function () {
      formTpl.hide()
    }

    $scope.$on('$destroy', function () {
      $scope.hideForm()
    })

    $scope.refreshMachineModels()
  },
])
