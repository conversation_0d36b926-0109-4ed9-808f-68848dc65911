app
  .run([
    'defaultErrorMessageResolver',
    function (defaultErrorMessageResolver) {
      defaultErrorMessageResolver
        .getErrorMessages()
        .then(function (errorMessages) {
          errorMessages['eightOrTenCharacters'] =
            'Largo de UID 8 o 10 caracteres, solo letras y números.'
        })
    },
  ])
  .controller('PartsController', [
    '$scope',
    '$modal',
    '$window',
    '$aside',
    'PartsService',
    'BuildingService',
    'MachineModelService',
    'MachineService',
    'BillingService',
    'AccountService',
    function (
      $scope,
      $modal,
      $window,
      $aside,
      partsService,
      buildingService,
      machineModelService,
      machineService,
      billingService,
      accountService
    ) {
      // settings
      $scope.settings = {
        singular: 'Item',
        plural: 'Items',
        cmd: 'Add',
      }

      $scope.data = {
        parts: [],
        machines: [],
        cards: [],
        tools: [],
        dispensers: [],
        rates: [],
        machineModels: [],
      }

      $scope.filters = {
        quickSearch: '',
        orderBy: 'id',
      }

      $scope.account = accountService.account

      $scope.roles = accountService.ROLES

      $scope.activeTab = 0
      $scope.handleTabChange = function (index) {
        $scope.activeTab = index
        if ($scope.activeTab === 2) {
          $scope.loadBuildingFilter()
        }
      }

      $scope.orderColumn = function (order) {
        $scope.filters.reverse =
          order != $scope.filters.orderBy ? false : !$scope.filters.reverse
        $scope.filters.orderBy = order
      }

      //obtener rates para asignar a maquinas

      $scope.refreshRates = function () {
        $scope.loadingRates = buildingService.loadRates().then(function (data) {
          $scope.data.rates = buildingService.getRates()
        })
      }

      // PARTS
      $scope.refreshParts = function () {
        $scope.loadingParts = partsService
          .loadParts('REPLACEMENT')
          .then(function (data) {
            $scope.data.parts = data.data.replacements
          })
      }

      var partFormTpl = $aside({
        scope: $scope,
        template: '/part-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editPart = function (item) {
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.settings.cmd = 'Edit'
          $scope.showPartForm()
        }
      }

      $scope.viewPart = function (item) {
        if (item) {
          item.editing = false
          $scope.item = item
          $scope.settings.cmd = 'View'
          $scope.showPartForm()
        }
      }

      $scope.createPart = function () {
        var item = {
          editing: true,
        }
        $scope.item = item
        $scope.settings.cmd = 'New'
        $scope.showPartForm()
      }

      $scope.savePart = function (item, partType) {
        if (item.uuid) {
          item.uuid = item.uuid.toLowerCase()

          if (item.uuid.indexOf('0x') < 0 && item.uuid.length <= 8) {
            item.uuid = '0x' + item.uuid
          }
        }

        if ($scope.settings.cmd == 'New') {
          $scope.errorMessage = null

          $scope.savingPart = partsService
            .createPart($scope.item, partType)
            .success(function () {
              $scope.hidePartForm()
              $scope.refreshAll(partType === 'MACHINE')
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido crear el item. ' +
                (reason.result_detail || reason.result_message)
            })
        } else if ($scope.settings.cmd == 'Edit') {
          $scope.errorMessage = null

          $scope.savingParts = partsService
            .updatePart(item.id, item)
            .success(function () {
              $scope.hidePartForm()
              $scope.refreshAll(partType === 'MACHINE')
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido modificar el item. ' +
                (reason.result_detail || reason.result_message)
            })
        }
      }

      $scope.deletePart = function (item) {
        if (confirm('Are you sure?')) {
          //Esta haciendo una baja fisica, comentado hasta implementar baja logica
          /*partsService.deletePart(item.id).success(function(){
                                                $scope.refreshParts();
                                            });*/
        }
      }

      $scope.showPartForm = function () {
        angular.element('.tooltip').remove()
        partFormTpl.show()
      }

      $scope.hidePartForm = function () {
        partFormTpl.hide()
        machineFormTpl.hide()
        cardFormTpl.hide()
        toolFormTpl.hide()
        soapDispenserFormTpl.hide()
      }

      // MACHINES
      $scope.refreshMachines = function () {
        $scope.loadingMachines = partsService
          .loadParts('MACHINE', $scope.filters)
          .then(function (data) {
            $scope.data.machines = data.data.machines
          })
      }

      var machineFormTpl = $aside({
        scope: $scope,
        template: '/machine-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editMachine = function (item) {
        $scope.refreshRates()
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.settings.cmd = 'Edit'
          $scope.showMachineForm()
        }
      }

      $scope.viewMachine = function (item) {
        if (item) {
          item.editing = false
          $scope.item = item
          $scope.settings.cmd = 'View'
          $scope.showMachineForm()
        }
      }

      $scope.showMachineHistory = function (machine) {
        $scope.selectedMachine = machine

        const modal = $modal({
          scope: $scope,
          template: 'assets/tpl/partials/machine-history-record.html',
          controller: 'MachineHistoryController',
          show: false,
        })

        const unsubscribe = $scope.$on('modal.hide', function (modal) {
          $scope.selectedMachine = null
          unsubscribe()
        })

        modal.$promise.then(modal.show)
      }

      $scope.createMachine = function () {
        $scope.refreshRates()
        var item = {
          editing: true,
        }
        $scope.item = item
        $scope.settings.cmd = 'New'
        $scope.showMachineForm()
      }

      $scope.showMachineForm = function () {
        angular.element('.tooltip').remove()
        machineFormTpl.show()
      }

      $scope.hideMachineForm = function () {
        machineFormTpl.hide()
      }

      // CARDS
      $scope.refreshCards = function () {
        $scope.loadingCards = partsService
          .loadParts('CARD', $scope.filters)
          .then(function (data) {
            $scope.data.cards = data.data.cards
          })
      }

      var cardFormTpl = $aside({
        scope: $scope,
        template: '/card-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editCard = function (item) {
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.item.start_time = new Date(item.start_time)
          $scope.item.end_time = new Date(item.end_time)
          $scope.settings.cmd = 'Edit'
          $scope.showCardForm()
        }
      }

      $scope.createCard = function () {
        var item = {
          editing: true,
        }
        $scope.item = item
        $scope.settings.cmd = 'New'
        $scope.showCardForm()
      }

      $scope.showCardForm = function () {
        angular.element('.tooltip').remove()
        cardFormTpl.show()
      }

      $scope.hideCardForm = function () {
        cardFormTpl.hide()
      }

      $scope.loadBuildingFilter = function () {
        $scope.loadingBuildingFilters = buildingService
          .loadBuildings(0)
          .then(function () {
            $scope.data.buildings = buildingService.getBuildings()
          })
      }

      $scope.clearFilter = function (filter) {
        $scope.filters[filter] = null
      }
      $scope.handleBuildingChange = function () {
        $scope.resetBuildingRelatedFilters()
        $scope.getBuilding()

        buildingService.loadUnits($scope.filters.building.id).then(function () {
          $scope.filters.units = buildingService
            .getUnits()
            .sortBy('tower', 'number', true)
        })
      }

      $scope.resetBuildingRelatedFilters = function () {
        $scope.filters.unit = ''
      }

      $scope.getBuilding = function () {
        $scope.loadingBuilding = buildingService
          .getBuildingById($scope.filters.building.id)
          .then(function (data) {
            $scope.filters.building = data.data
          })
      }

      var updateCardBalanceController = [
        '$scope',
        '$modal',
        'PartsService',
        'BillingService',
        function ($scope, modal, partsService, billingService) {
          const filters = {
            cardId: $scope.transaction.cardId,
            limit: 10,
          }

          $scope.details = {
            REFUND: 'DEVOLUCIÓN',
            COURTESY: 'CORTESIA',
            CASH: 'PAGO EN EFECTIVO',
            OTHER: 'OTHER',
          }

          $scope.transferBalanceDetails = {
            BALANCE_TRANSFER: 'TRANSFERENCIA A OTRA TARJETA',
            OTHER: 'OTRA',
          }

          $scope.vouchers = {
            noVoucher: 'Sin Comprobante',
            bill: 'Facturar',
            creditNote: 'Nota de Crédito',
          }

          $scope.consumers = {
            FINAL: 'FINAL',
            BUSINESS: 'EMPRESA',
          }

          $scope.recipientDocTypes = {
            2: 'RUT',
            3: 'CI',
          }

          $scope.transactionType = {
            BALANCE_TRANSFER_BETWEEN_CARDS: 'BALANCE_TRANSFER_BETWEEN_CARDS',
            BALANCE_CREDIT: 'BALANCE_CREDIT',
            BALANCE_CREDIT_WITH_BILL: 'BALANCE_CREDIT_WITH_BILL',
            BALANCE_DEBIT: 'BALANCE_DEBIT',
            BALANCE_DEBIT_WITH_CREDIT_NOTE: 'BALANCE_DEBIT_WITH_CREDIT_NOTE',
          }

          $scope.transactionValidations = {
            isConsumerFormVisible: false,
            isBusinessFormVisible: false,
            isCreditNoteFormVisible: false,
            isCardWithoutBills: false,
            isMachineListEmpty: true,
          }

          $scope.loadingMachines = machineService
            .findMachinesWithDifferentRatesByBuildingId(
              $scope.selectedCard.buildingId
            )
            .then(function () {
              $scope.machines = machineService.getMachines()
              $scope.transactionValidations.isMachineListEmpty =
                !$scope.machines.length
            })

          $scope.handleConsumerChange = function (consumer) {
            if (consumer === 'BUSINESS') {
              $scope.transactionValidations.isBusinessFormVisible = true
            } else {
              $scope.transactionValidations.isBusinessFormVisible = false
            }
          }

          $scope.handleVoucherChange = function (voucher) {
            if (voucher === 'creditNote') {
              $scope.transactionValidations.isConsumerFormVisible = false
              $scope.transactionValidations.isBusinessFormVisible = false
              $scope.transaction.consumer = ''
              $scope.transaction.isBillRequired = false

              $scope.creditNoteErrorMessage = ''

              $scope.transaction.transactionType =
                $scope.transactionType.BALANCE_DEBIT_WITH_CREDIT_NOTE
              $scope.loadingBills = billingService
                .findBillsByCard(filters)
                .then(
                  function () {
                    $scope.bills = billingService.getBillResults()
                    if ($scope.bills.length != 0) {
                      $scope.transactionValidations.isCreditNoteFormVisible = true
                      $scope.transactionValidations.isCardWithoutBills = false
                    } else {
                      $scope.transactionValidations.isCardWithoutBills = true
                      $scope.facturas =
                        'No se encuentran facutras asociadas a esta tarjeta'
                      $scope.transactionValidations.isCreditNoteFormVisible = false
                    }
                  },
                  function (response) {
                    $scope.creditNoteErrorMessage = `Ha ocurrido un error. ERROR: ${response.data.result_message}`
                  }
                )
            } else if (voucher === 'bill') {
              $scope.transactionValidations.isConsumerFormVisible = true
              $scope.transactionValidations.isCreditNoteFormVisible = false
              $scope.transaction.transactionType =
                $scope.transactionType.BALANCE_CREDIT_WITH_BILL
              $scope.transactionValidations.isCardWithoutBills = false
              $scope.creditNoteErrorMessage = ''
            } else if (voucher === 'noVoucher') {
              $scope.transactionValidations.isConsumerFormVisible = false
              $scope.transactionValidations.isBusinessFormVisible = false
              $scope.transactionValidations.isCreditNoteFormVisible = false
              $scope.transaction.consumer = ''
              $scope.transaction.transactionType =
                $scope.transactionType.BALANCE_CREDIT
              $scope.transactionValidations.isCardWithoutBills = false
              $scope.creditNoteErrorMessage = ''
            }
          }

          $scope.selectBill = function (selectedBillId) {
            for (let i = 0; i < $scope.bills.length; i++) {
              if ($scope.bills[i].id !== selectedBillId) {
                $scope.bills[i].selected = false
              }
            }

            if ($scope.transaction.billId === selectedBillId) {
              $scope.transaction.billId = 0
            } else {
              $scope.transaction.billId = selectedBillId
            }
          }

          $scope.transferBalanceFilters = {
            cardSearch: '',
          }

          $scope.cardReceiver = null
          $scope.validateCardReceiver = function () {
            $scope.loadingCards = partsService
              .loadParts('CARD', $scope.transferBalanceFilters)
              .then(function (data) {
                $scope.cardReceiver = null

                const foundCards = data?.data?.cards
                if (!foundCards?.length) {
                  return
                }

                if (foundCards.length === 1) {
                  $scope.cardReceiver = foundCards[0]
                }
              })
          }

          $scope.transferBalance = function () {
            $scope.transaction.transactionType =
              $scope.transactionType.BALANCE_TRANSFER_BETWEEN_CARDS
            $scope.transaction.cardBalance =
              $scope.transaction.cardBalance -
              $scope.transaction.balanceToTransfer
            $scope.updateCardBalance()
          }

          $scope.updateCardBalance = function () {
            $scope.transaction.cardReceiverId =
              $scope.cardReceiver != null ? $scope.cardReceiver.id : 0

            $scope.errorMessage = null

            if (
              $scope.selectedCard.balance < $scope.transaction.cardBalance &&
              !$scope.transaction.transactionType
            ) {
              $scope.transaction.transactionType =
                $scope.transactionType.BALANCE_CREDIT
            }

            $scope.loadingNewBalance = partsService
              .updateCardBalance($scope.transaction)
              .then(
                function (response) {
                  $scope.selectedCard.balance = $scope.transaction.cardBalance
                  $scope.$hide()
                },
                function (response) {
                  $scope.errorMessage = `No se ha podido modificar el saldo de la tarjeta. ERROR: ${response.data.result_message}`
                }
              )
          }
        },
      ]

      $scope.showUpdateCardBalanceForm = function (card) {
        const modal = $modal({
          scope: $scope,
          template: 'assets/tpl/partials/update-balance.html',
          controller: updateCardBalanceController,
        })

        $scope.transaction = {
          cardId: card.id,
          cardBalance: card.balance,
        }
        $scope.selectedCard = card

        modal.$promise.then(modal.show)
      }

      $scope.showTransferCardBalanceForm = function (card) {
        const modal = $modal({
          scope: $scope,
          template: 'assets/tpl/partials/transfer-card-balance.html',
          controller: updateCardBalanceController,
        })

        $scope.transaction = {
          cardId: card.id,
        }
        $scope.selectedCard = card

        $scope.cardReceiver = null
        $scope.cardMessage = 'No se ha encontrado ninguna tarjeta'
        $scope.filters.quickSearch = ''

        modal.$promise.then(modal.show)
      }

      // TOOLS
      $scope.refreshTools = function () {
        $scope.loadingTools = partsService
          .loadParts('TOOL')
          .then(function (data) {
            $scope.data.tools = data.data.parts
          })
      }

      var toolFormTpl = $aside({
        scope: $scope,
        template: '/tool-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editTool = function (item) {
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.settings.cmd = 'Edit'
          $scope.showToolForm()
        }
      }

      $scope.viewTool = function (item) {
        if (item) {
          item.editing = false
          $scope.item = item
          $scope.settings.cmd = 'View'
          $scope.showToolForm()
        }
      }

      $scope.createTool = function () {
        var item = {
          editing: true,
        }
        $scope.item = item
        $scope.settings.cmd = 'New'
        $scope.showToolForm()
      }

      $scope.showToolForm = function () {
        angular.element('.tooltip').remove()
        toolFormTpl.show()
      }

      $scope.hideToolForm = function () {
        toolFormTpl.hide()
      }

      //SOAP DISPENSER
      $scope.refreshDispensers = function () {
        $scope.loadingDispensers = partsService
          .loadSoapDispensers()
          .then(function (data) {
            $scope.data.dispensers = data.data.dispensers
          })
      }

      const soapDispenserFormTpl = $aside({
        scope: $scope,
        template: '/soapdisp-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editSoapdisp = function (item) {
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.settings.cmd = 'Edit'
          $scope.showSoapDispenserForm()
        }
      }

      $scope.viewSoapdisp = function (item) {
        if (item) {
          item.editing = false
          $scope.item = item
          $scope.settings.cmd = 'View'
          $scope.showSoapDispenserForm()
        }
      }

      $scope.createSoapdisp = function () {
        var item = {
          editing: true,
        }
        $scope.item = item
        $scope.settings.cmd = 'New'
        $scope.showSoapDispenserForm()
      }

      $scope.saveSoapdisp = function (item) {
        if ($scope.settings.cmd == 'New') {
          $scope.errorMessage = null

          $scope.savingSoapdisp = partsService
            .createSoapDispenser($scope.item)
            .success(function () {
              $scope.hidePartForm()
              $scope.refreshAll()
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido crear el item. ' +
                (reason.result_detail || reason.result_message)
            })
        } else if ($scope.settings.cmd == 'Edit') {
          $scope.errorMessage = null

          $scope.savingSoapdispUpd = partsService
            .updateSoapDispenser(item.id, item)
            .success(function () {
              $scope.hidePartForm()
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido modificar el item. ' +
                (reason.result_detail || reason.result_message)
            })
        }
      }

      $scope.deleteDispenser = function (item) {
        if (confirm('Estás seguro?')) {
          partsService.deleteSoapDispenser(item.id).success(function () {
            $scope.refreshDispensers()
          })
        }
      }

      $scope.showSoapDispenserForm = function () {
        angular.element('.tooltip').remove()
        soapDispenserFormTpl.show()
      }

      $scope.hideSoapdispForm = function () {
        soapDispenserFormTpl.hide()
      }

      var soapDispenserController = [
        '$scope',
        '$modal',
        function ($scope, modal, PartsService) {
          $scope.errorMessage = null

          $scope.replenishUses = function () {
            $scope.item.isReplenish = true
            $scope.updateUses = partsService
              .replenishSoapDispenserUses($scope.dispenserId, $scope.item)
              .success(function () {
                $scope.refreshAll()
                $scope.$hide()
              })
              .error(function (reason) {
                $scope.errorMessage =
                  'No se pudo actualizar el dispensador. ' +
                  (reason.result_detail || reason.result_message)
              })
          }
        },
      ]

      $scope.showReplenishSoapDispenserForm = function (dispenserId) {
        const modal = $modal({
          scope: $scope,
          template: 'assets/tpl/partials/replenish-soap-dispenser.html',
          controller: soapDispenserController,
        })

        $scope.dispenserId = dispenserId

        modal.$promise.then(modal.show)
      }

      // MACHINE MODELS
      $scope.refreshMachineModels = function () {
        $scope.loadingMachineModel = machineModelService
          .loadMachineModels()
          .then(function (data) {
            $scope.data.machineModels = machineModelService.getMachineModels()
          })
      }

      const machineModelFormTpl = $aside({
        scope: $scope,
        template: '/machine-model-form.html',
        show: false,
        placement: 'left',
        backdrop: false,
        animation: 'am-slide-left',
      })

      $scope.editMachineModel = function (item) {
        if (item) {
          item.editing = true
          $scope.item = item
          $scope.settings.cmd = 'Edit'
          $scope.showMachineModdelForm()
        }
      }

      $scope.viewMachineModel = function (item) {
        if (item) {
          item.editing = false
          $scope.item = item
          $scope.settings.cmd = 'View'
          $scope.showMachineModdelForm()
        }
      }

      $scope.createMachineModel = function () {
        $scope.item = {
          editing: true,
        }
        $scope.settings.cmd = 'New'
        $scope.showMachineModdelForm()
      }

      $scope.saveMachineModel = function (item) {
        if ($scope.settings.cmd === 'New') {
          $scope.errorMessage = null

          $scope.savingMachineModel = machineModelService
            .createMachineModel($scope.item)
            .success(function () {
              $scope.hideMachineModdelForm()
              $scope.refreshMachineModels()
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido crear el Modelo de máquina. ' +
                reason.result_detail
                  ? reason.result_detail
                  : ''
            })
        } else if ($scope.settings.cmd === 'Edit') {
          $scope.errorMessage = null

          $scope.savingMachineModel = machineModelService
            .updateMachineModel(item.id, item)
            .success(function () {
              $scope.hideMachineModdelForm()
              $scope.refreshMachineModels()
            })
            .error(function (reason) {
              $scope.errorMessage =
                'No se ha podido modificar la administración. ' +
                reason.result_detail
                  ? reason.result_detail
                  : ''
            })
        }
      }

      $scope.deleteMachineModel = function (item) {
        if (confirm('Estás seguro?')) {
          machineModelService.deleteMachineModel(item.id).success(function () {
            $scope.refreshMachineModels()
          })
        }
      }

      $scope.showMachineModdelForm = function () {
        angular.element('.tooltip').remove()
        machineModelFormTpl.show()
      }

      $scope.hideMachineModdelForm = function () {
        machineModelFormTpl.hide()
      }

      $scope.checkAll = function () {
        angular.forEach($scope.data, function (item) {
          item.selected = !item.selected
        })
      }

      $scope.$on('$destroy', function () {
        $scope.hidePartForm()
      })

      $scope.refreshAll = function (refreshMachines = false) {
        $scope.loadBuildingFilter()

        $scope.refreshParts()
        $scope.refreshTools()
        $scope.refreshDispensers()
        $scope.refreshMachineModels()

        if (refreshMachines) {
          $scope.refreshMachines()
        }
      }

      $scope.refreshAll()
    },
  ])
