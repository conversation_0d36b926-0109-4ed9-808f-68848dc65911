app.controller('MachineSelectorController', [
  '$scope',
  '$modal',
  'MachineService',
  'BuildingService',
  'PartsService',
  function ($scope, modal, machineService, buildingService, partsService) {
    $scope.machines = []
    $scope.filters = {}
    $scope.selectedMachine = null

    $scope.close = function () {
      $scope.$hide()
    }

    $scope.$parent.$on('modal.hide.before', function (modal) {
      modal.targetScope.selectedMachine = $scope.selectedMachine
    })

    $scope.loadMachines = function () {
      if (
        !$scope.filters.machineQuickSearch ||
        $scope.filters.machineQuickSearch.length < 6
      ) {
        $scope.errorMessage =
          'El número de serie debe tener al menos 6 caracteres.'
        return
      }

      $scope.errorMessage = null
      $scope.loadingMachines = machineService
        .findMachines({ serialNumber: $scope.filters.machineQuickSearch })
        .then(function (data) {
          $scope.machines = data?.data?.machines
        })
    }

    $scope.toggleSelection = function (machine) {
      if ($scope.selectedMachine === machine) {
        $scope.selectedMachine = null
      } else {
        $scope.selectedMachine = machine
      }
    }

    $scope.notAssigned = function (item) {
      return item.building === null
    }

    $scope.assignMachine = function (machine) {
      $scope.loadingBuilding = buildingService
        .assignMachine($scope.building.id, machine.id)
        .then(function () {
          $scope.refreshBuilding()
        })
      $scope.$hide()
    }
  },
])

app.controller('CardSelectorController', [
  '$scope',
  '$modal',
  'MachineService',
  'PartsService',
  'BuildingService',
  function ($scope, modal, machineService, partsService, buildingService) {
    $scope.cards = []
    $scope.filters = {}
    $scope.selectedCard = null

    $scope.close = function () {
      $scope.selectedCard = null
      $scope.$hide()
    }

    $scope.$parent.$on('modal.hide.before', function (modal) {
      modal.targetScope.selectedCard = $scope.selectedCard
    })

    $scope.selectCard = function (c) {
      $scope.selectedCard = c
      $scope.$hide()
    }

    $scope.loadCards = function () {
      $scope.loadingCards = partsService
        .loadParts('CARD', $scope.filters)
        .then(function (data) {
          const foundCards = data?.data?.cards
          if (!foundCards?.length) {
            return
          }

          if (foundCards.length === 1) {
            $scope.card = foundCards[0]
            $scope.card.isActive = true
          }
        })
    }

    $scope.assignCard = function (card) {
      $scope.errorMessage = null

      $scope.loadingBuilding = buildingService
        .assignCard(
          $scope.building.id,
          $scope.unit.id,
          card.uuid,
          card.isActive
        )
        .error(function (reason) {
          $scope.errorMessage =
            'No se ha podido asignar la tarjeta a la unidad. ' +
            reason.result_message
        })
        .then(function () {
          $scope.loadUnits(true)
          $scope.$hide()
        })
    }

    $scope.notAssigned = function (item) {
      return item.unit_id === null
    }
  },
])

app.controller('RPIParentController', [
  '$scope',
  '$modal',
  function ($scope, modal) {
    $scope.machines = $scope.$parent.machines.filter(
      (machine) =>
        machine.id !== $scope.$parent.selectedRPIParent.id &&
        !machine.rpi_child &&
        !$scope.$parent.isRPIChild(machine)
    )
    $scope.selectedRPIParent = $scope.$parent.selectedRPIParent

    $scope.close = function () {
      $scope.selectedRPIChild = null
      $scope.$hide()
    }

    $scope.submit = function () {
      $scope.$hide()
    }

    $scope.$parent.$on('modal.hide.before', function (modal) {
      modal.targetScope.selectedRPIChild = $scope.selectedRPIChild
    })
  },
])

app.controller('AssignQrController', [
  '$scope',
  '$modal',
  function ($scope, modal) {
    $scope.selectedMachine = $scope.$parent.selectedMachine

    $scope.close = function () {
      $scope.qrToken = null
      $scope.$hide()
    }

    $scope.submit = function () {
      $scope.$hide()
    }

    $scope.$parent.$on('modal.hide.before', function (modal) {
      modal.targetScope.qrToken = $scope.qrToken
    })
  },
])

app.controller('MachineHistoryController', [
  '$scope',
  '$modal',
  'MachineService',
  function ($scope, modal, machineService) {
    $scope.close = function () {
      $scope.$hide()
    }

    $scope.getMachineHistory = function () {
      $scope.loadingMachineHistory = machineService
        .getMachineHistory($scope.$parent.selectedMachine.id)
        .then(function (data) {
          $scope.machine = data.data.machine
        })
    }

    $scope.getMachineHistory()
  },
])
