app.directive('matchValidator', function () {
  return {
    restrict: 'A', // only activate on element attribute
    require: '?ngModel', // get a hold of NgModelController
    link: function (scope, elem, attrs, ngModel) {
      if (!ngModel) return // do nothing if no ng-model

      var Value = null

      // watch own value and re-validate on change
      scope.$watch(attrs.ngModel, function (val) {
        Value = val

        validate()
      })

      // observe the other value and re-validate on change
      attrs.$observe('matchValidator', function () {
        validate()
      })

      var validate = function () {
        // values
        var val1 = Value
        var val2 = attrs.matchValidator
        // set validity

        if (val1 !== '' && val1 !== undefined) {
          ngModel.$setValidity('match', val1 == val2)
        } else {
          ngModel.$setValidity('match', true)
        }
      }
    },
  }
})

/**
 * the "noPropagate" directive provides a way to bind a model (noPropagateModel)
 * and execute a method (noPropagate) defined in the parent scope when a click
 * event occurs on the element. It ensures that the click event does not
 * propagate further up the DOM tree.
 *
 * Usage:
 *  <div
 *    no-propagate-model="myModel"
 *    no-propagate="myMethod()"
 *  >
 *    Click me
 *  </div>
 *
 * When the element is clicked, the myMethod() function will be called, either
 * with or without the myModel argument depending on its availability. The
 * click event will not propagate further up the DOM tree.
 */
app.directive('noPropagate', function ($parse) {
  return {
    restrict: 'A',
    scope: {
      model: '=noPropagateModel',
      method: '&noPropagate',
    },
    link: function (scope, elem, attrs) {
      $(elem).on('click', function (e, rowid) {
        if (scope.method !== undefined) {
          scope.$apply(function () {
            if (scope.model !== undefined) {
              scope.method(scope.model)
            } else {
              scope.method()
            }
          })
        }

        e.stopPropagation()
      })
    },
  }
})
