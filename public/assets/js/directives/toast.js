/**
 * The toast directive provides a way to display toast messages.
 * It allows you to display different types of toast messages
 * such as 'Error', 'Success', 'Info', and 'Warning'.
 *
 * Usage:
 *  // controller.js
 *    $scope.message = { type: 'Success', text: 'Operation completed successfully!' }
 *  // template.html
 *    <toast message="message"></toast>
 */
app.directive('toast', function ($timeout) {
  return {
    restrict: 'E',
    scope: {
      message: '=',
    },
    controller: function ($scope) {
      $scope.visible = false

      $scope.$watch(
        'message',
        function (newMessage, oldMessage) {
          if (
            !newMessage?.text ||
            !newMessage?.type ||
            newMessage?.text === oldMessage?.text
          ) {
            return
          }

          $scope.visible = true

          $timeout(function () {
            $scope.visible = false
            $scope.message = {}
          }, 3000)
        },
        true
      )
    },
    templateUrl: 'assets/tpl/directives/toast.html',
  }
})
