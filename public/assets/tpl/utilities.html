<div
  class="dashboard lighten-3 dev-utilities"
  ng-controller="UtilitiesController"
>
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Dev Utilities</h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-12 col-md-6 col-lg-4">
      <div
        class="card"
        data-ng-click="openMqttMessage()"
        style="cursor: pointer"
      >
        <div class="card-content">
          <div class="col-12 text-center text-success item--lg">
            <p><span class="md md-chat"></span></p>
            <p>MQTT Message</p>
          </div>
        </div>
      </div>

      <div
        class="card red lighten-4"
        data-ng-click="openTask()"
        style="cursor: pointer"
      >
        <div class="card-content">
          <div class="col-12 text-center text-danger item--lg">
            <p><span class="md md-report"></span></p>
            <p>Run task</p>
          </div>
        </div>
      </div>

      <div
        class="card"
        data-ng-click="openExchangeRates()"
        style="cursor: pointer"
      >
        <div class="card-content">
          <div class="col-12 text-center text-warning item--lg">
            <p><span class="md md-attach-money"></span></p>
            <p>Ver cotizaciones</p>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-6 col-lg-8">
      <div class="card" style="cursor: pointer">
        <div class="card-header">
          <div class="card-title">Materialism resources</div>
        </div>
        <div class="card-content">
          <div class="row">
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('buttons')"
            >
              <p><span class="md md-radio-button-on"></span></p>
              <p>Button</p>
            </div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('cards')"
            >
              <p><span class="md md-view-carousel"></span></p>
              <p>Cards</p>
            </div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('colors')"
            >
              <p><span class="md md-color-lens"></span></p>
              <p>Color</p>
            </div>
            <div class="clearfix visible-md-block"></div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('grid')"
            >
              <p><span class="md md-dashboard"></span></p>
              <p>Grid</p>
            </div>
            <div class="clearfix visible-lg-block"></div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('icons')"
            >
              <p><span class="md md-insert-emoticon"></span></p>
              <p>Icons</p>
            </div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('lists')"
            >
              <p><span class="md md-format-list-bulleted"></span></p>
              <p>List styles</p>
            </div>
            <div class="clearfix visible-md-block"></div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('messages')"
            >
              <p><span class="md md-notifications-on"></span></p>
              <p>Messages &amp; Notifications</p>
            </div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('tabs')"
            >
              <p><span class="md md-tab"></span></p>
              <p>Tabs &amp; Accordions</p>
            </div>
            <div class="clearfix visible-lg-block"></div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('typography')"
            >
              <p><span class="md md-format-size"></span></p>
              <p>Typography</p>
            </div>
            <div class="clearfix visible-md-block"></div>
            <div
              class="col-sm-4 col-lg-3 text-center text-info item--md"
              data-ng-click="openResource('weather-icons')"
            >
              <p><span class="md wi wi-umbrella"></span></p>
              <p>Weather Icons</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-6 col-lg-4"></div>
  </div>
</div>
