<div class="dashboard lighten-3" cg-busy="loadingBuilding">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Edificio {{building.name}}
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refresh()"
        >
          Recargar
        </button>
        <button
          class="btn btn-xs btn-primary pull-right"
          style="margin-right: 10px"
          ng-click="showLoadUses()"
        >
          Cargar Usos
        </button>
        <button
          class="btn btn-xs btn-danger pull-right"
          style="margin-right: 10px"
          ng-click="rechargePrepaidUses()"
          ng-if="building.prepaidRechargeableUses > 0"
        >
          Recargar Usos PREPAGO
        </button>
      </h4>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <strong>Informacion general</strong>
        </div>
        <div class="panel-body">
          <div class="row">
            <div class="col-lg-6">
              <p style="color: red">
                <strong>Keep Alive: </strong>{{building.oldestKeepAliveFixed |
                date:'yyyy-MM-dd HH:mm:ss'}}
              </p>
              <p><strong>ID</strong>: {{building.id}}</p>
              <p><strong>Dirección</strong>: {{building.address}}</p>
              <p>
                <strong>Administra</strong>: {{building.administration.name}}
              </p>
              <p
                ng-style="{'color': building.isIncludedInClosure ? 'green' : 'red'}"
              >
                <strong ng-if="building.isIncludedInClosure"
                  >Cierre Habilitado</strong
                >
                <strong ng-if="!building.isIncludedInClosure"
                  >Cierre Deshabilitado</strong
                >
                <button
                  ng-if="!building.isIncludedInClosure"
                  class="btn btn-xs btn-success"
                  ng-click="enableAdministrationClosure(building)"
                >
                  Habilitar
                </button>
                <button
                  ng-if="building.isIncludedInClosure"
                  class="btn btn-xs btn-danger"
                  ng-click="disableAdministrationClosure(building)"
                >
                  Deshabilitar
                </button>
              </p>
              <p
                ng-style="{'color': building.isEnabledForMaintenance ? 'green' : 'red'}"
              >
                <strong ng-if="building.isEnabledForMaintenance"
                  >Mantenimiento Habilitado</strong
                >
                <strong ng-if="!building.isEnabledForMaintenance"
                  >Mantenimiento Deshabilitado</strong
                >
                <button
                  ng-if="!building.isEnabledForMaintenance"
                  class="btn btn-xs btn-success"
                  ng-click="enableForMaintenance(building)"
                >
                  Habilitar
                </button>
                <button
                  ng-if="building.isEnabledForMaintenance"
                  class="btn btn-xs btn-danger"
                  ng-click="disableForMaintenance(building)"
                >
                  Deshabilitar
                </button>
              </p>
              <p
                ng-style="{'color': building.isPagosWebWithSplitEnabled ? 'green' : 'red'}"
              >
                <strong ng-if="building.isPagosWebWithSplitEnabled"
                  >PagosWeb con Split Habilitado</strong
                >
                <strong ng-if="!building.isPagosWebWithSplitEnabled"
                  >PagosWeb con Split Deshabilitado</strong
                >
                <button
                  ng-if="!building.isPagosWebWithSplitEnabled"
                  class="btn btn-xs btn-success"
                  ng-click="enablePagosWebWithSplit(building)"
                >
                  Habilitar
                </button>
                <button
                  ng-if="building.isPagosWebWithSplitEnabled"
                  class="btn btn-xs btn-danger"
                  ng-click="disablePagosWebWithSplit(building)"
                >
                  Deshabilitar
                </button>
              </p>
              <p
                ng-style="{'color': building.isRedPagosWithSplitEnabled ? 'green' : 'red'}"
              >
                <strong ng-if="building.isRedPagosWithSplitEnabled"
                  >RedPagos con Split Habilitado</strong
                >
                <strong ng-if="!building.isRedPagosWithSplitEnabled"
                  >RedPagos con Split Deshabilitado</strong
                >
                <button
                  ng-if="!building.isRedPagosWithSplitEnabled"
                  class="btn btn-xs btn-success"
                  ng-click="enableRedPagosWithSplit(building)"
                >
                  Habilitar
                </button>
                <button
                  ng-if="building.isRedPagosWithSplitEnabled"
                  class="btn btn-xs btn-danger"
                  ng-click="disableRedPagosWithSplit(building)"
                >
                  Deshabilitar
                </button>
              </p>
              <p>
                <strong>Precio al Cliente/Empresa</strong>:
                ${{building.rate.priceCustomer}}
              </p>
              <p>
                <strong>Precio Tarjeta</strong>:
                ${{building.rate.priceCardReplacement}}
              </p>
              <p>
                <strong>Precio Empresa</strong>: ${{building.rate.priceCompany}}
              </p>
            </div>
            <div class="col-lg-6">
              <p>
                <span ng-if="!building.editClosureType"
                  ><strong>Tipo de Cierre</strong>:
                  {{buildingClosureTypes[building.closureType]}}
                </span>
                <span ng-if="building.editClosureType"
                  ><strong>Tipo de Cierre</strong>:
                  <select
                    class="form-control"
                    ng-model="building.closureType"
                    ng-options="key as value for (key , value) in buildingClosureTypes"
                  ></select>
                </span>
                <button
                  ng-if="!building.editClosureType"
                  class="btn btn-xs btn-info"
                  ng-click="building.editClosureType = true"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="building.editClosureType"
                  class="btn btn-xs btn-success"
                  ng-click="editClosureType()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="building.editClosureType"
                  class="btn btn-xs btn-danger"
                  ng-click="building.editClosureType = false"
                >
                  CANCELAR
                </button>
              </p>
              <p>
                <strong>Día de Cierre</strong>:
                {{building.administration.closureDay}}
              </p>
              <div ng-if="building.showPaymentMethod">
                <p>
                  <span ng-if="!building.editPaymentMethod"
                    ><strong>Método de Pago</strong>: {{
                    findPaymentMethod(building.paymentMethod) }}
                  </span>
                  <span ng-if="building.editPaymentMethod"
                    ><strong>Método de Pago</strong>:
                    <select
                      class="form-control"
                      style="width: 180px; display: inline"
                      ng-model="building.paymentMethod"
                    >
                      <option
                        ng-repeat="method in paymentMethods"
                        value="{{method.key}}"
                      >
                        {{method.value}}
                      </option>
                    </select>
                  </span>
                  <button
                    ng-if="!building.editPaymentMethod"
                    class="btn btn-xs btn-info"
                    ng-click="building.editPaymentMethod = true"
                  >
                    CAMBIAR
                  </button>
                  <button
                    ng-if="building.editPaymentMethod"
                    class="btn btn-xs btn-success"
                    ng-click="savePaymentMethod()"
                  >
                    GUARDAR
                  </button>
                  <button
                    ng-if="building.editPaymentMethod"
                    class="btn btn-xs btn-danger"
                    ng-click="building.editPaymentMethod = false"
                  >
                    CANCELAR
                  </button>
                </p>
              </div>
              <p>
                <strong>COBRO MIN X UNIDAD</strong>: {{
                building.rate.minUsesPerUnit }}
              </p>
              <p>
                <span ng-if="!building.editBuildingPrepaidRechargableUses"
                  ><strong>Recarga PREPAGO</strong>: {{
                  building.prepaidRechargeableUses }}
                </span>
                <span ng-if="building.editBuildingPrepaidRechargableUses"
                  ><strong>Recarga PREPAGO</strong>:
                  <input
                    type="text"
                    class="form-control"
                    ng-model="building.prepaidRechargeableUses"
                    style="width: 180px; display: inline"
                  />
                </span>
                <button
                  ng-if="!building.editBuildingPrepaidRechargableUses"
                  class="btn btn-xs btn-info"
                  ng-click="building.editBuildingPrepaidRechargableUses = true"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="building.editBuildingPrepaidRechargableUses"
                  class="btn btn-xs btn-success"
                  ng-click="saveBuildingPrepaidRechargableUses()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="building.editBuildingPrepaidRechargableUses"
                  class="btn btn-xs btn-danger"
                  ng-click="building.editBuildingPrepaidRechargableUses = false"
                >
                  CANCELAR
                </button>
              </p>

              <p>
                <span ng-if="!building.editBuildingMaxNumberOfUnits"
                  ><strong>Máxima cant. unidades</strong>: {{
                  building.maxNumberOfUnits }}
                </span>
                <span ng-if="building.editBuildingMaxNumberOfUnits"
                  ><strong>Máxima cant. unidades</strong>:
                  <input
                    type="number"
                    min="0"
                    class="form-control"
                    ng-model="building.maxNumberOfUnits"
                    style="width: 180px; display: inline"
                  />
                </span>
                <button
                  ng-if="!building.editBuildingMaxNumberOfUnits"
                  class="btn btn-xs btn-info"
                  ng-click="building.editBuildingMaxNumberOfUnits = true"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="building.editBuildingMaxNumberOfUnits"
                  class="btn btn-xs btn-success"
                  ng-click="saveBuildingMaxNumberOfUnits()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="building.editBuildingMaxNumberOfUnits"
                  class="btn btn-xs btn-danger"
                  ng-click="building.editBuildingMaxNumberOfUnits = false"
                >
                  CANCELAR
                </button>
              </p>

              <p>
                <span ng-if="!building.editPreBlockedUses"
                  ><strong>Usos PRE Bloqueo</strong>: {{
                  building.setting.preBlockedUses }}
                </span>
                <span ng-if="building.editPreBlockedUses"
                  ><strong>Máxima cant. unidades</strong>:
                  <input
                    type="number"
                    min="0"
                    class="form-control"
                    ng-model="building.setting.preBlockedUses"
                    style="width: 180px; display: inline"
                  />
                </span>
                <button
                  ng-if="!building.editPreBlockedUses"
                  class="btn btn-xs btn-info"
                  ng-click="building.editPreBlockedUses = true"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="building.editPreBlockedUses"
                  class="btn btn-xs btn-success"
                  ng-click="saveBuildingPreBlockedUses()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="building.editPreBlockedUses"
                  class="btn btn-xs btn-danger"
                  ng-click="building.editPreBlockedUses = false"
                >
                  CANCELAR
                </button>
              </p>

              <p
                ng-if="building.buildingType == 'LAUNDROMAT' || building.buildingType == 'THIRD_PARTY_LAUNDROMAT'"
              >
                <span ng-if="!editingBuildingTime">
                  <strong>Horario de Local</strong>: {{ building.openingTime |
                  date: 'HH:mm' }} - {{ building.closingTime | date: 'HH:mm' }}
                </span>
                <span ng-if="editingBuildingTime">
                  <strong>Horario de Local</strong>:
                  <input
                    type="time"
                    class="form-control"
                    ng-model="building.openingDateTime"
                    style="width: 180px; display: inline"
                  />
                  -
                  <input
                    type="time"
                    class="form-control"
                    ng-model="building.closingDateTime"
                    style="width: 180px; display: inline"
                  />
                </span>
                <button
                  ng-if="!editingBuildingTime"
                  class="btn btn-xs btn-info"
                  ng-click="editBuildingTime()"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="editingBuildingTime"
                  class="btn btn-xs btn-success"
                  ng-click="saveBuildingTime()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="editingBuildingTime"
                  class="btn btn-xs btn-danger"
                  ng-click="editingBuildingTime = false"
                >
                  CANCELAR
                </button>
              </p>
            </div>
          </div>
          <hr class="m-5" />
          <div class="row">
            <div class="col-lg-6">
              <strong>Informacion de contacto</strong>
              <p>
                <span ng-if="!building.editContact"
                  >Contacto:
                  <textarea rows="5" cols="50" disabled>
{{building.contact}}</textarea
                  >
                </span>
                <span ng-if="building.editContact"
                  >Contacto:
                  <textarea
                    rows="5"
                    cols="50"
                    ng-model="building.contact"
                  ></textarea>
                </span>
                <button
                  ng-if="!building.editContact"
                  class="btn btn-xs btn-info"
                  ng-click="building.editContact = true"
                >
                  CAMBIAR
                </button>
                <button
                  ng-if="building.editContact"
                  class="btn btn-xs btn-success"
                  ng-click="saveContact()"
                >
                  GUARDAR
                </button>
                <button
                  ng-if="building.editContact"
                  class="btn btn-xs btn-danger"
                  ng-click="building.editContact = false"
                >
                  CANCELAR
                </button>
              </p>
            </div>
          </div>

          <hr class="m-5" />
          <div class="row">
            <div class="col-lg-12">
              <strong>Asistente:</strong>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-6">
              <span><strong>URL:</strong></span>
              <span>{{building.assistantUrl}}</span>
              <span
                class="md md-content-copy pointer"
                ng-click="copy($event, building.assistantUrl)"
              />
            </div>
            <div class="col-lg-6">
              <span
                ><strong>Google Maps:</strong> (solo aplica a Laundromats)
                -</span
              >
              <span>
                {{building.googleMaps && building.googleMaps.name}} |
                {{building.googleMaps && building.googleMaps.link}}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div>
          <ul class="nav nav-tabs">
            <li ng-class="{'active': activeTab === 0 }" class="ng-scope">
              <a ng-click="setActiveTab(0)" class="ng-binding">Unidades</a>
            </li>
            <li ng-class="{'active': activeTab === 1 }" class="ng-scope">
              <a ng-click="setActiveTab(1)" class="ng-binding">Máquinas</a>
            </li>
            <li ng-class="{'active': activeTab === 2 }" class="ng-scope">
              <a ng-click="setActiveTab(2)" class="ng-binding"
                >Mantenimientos</a
              >
            </li>
          </ul>
          <div title="Unidades" ng-if="activeTab === 0" cg-busy="loadingUnits">
            <div
              ng-include
              src="'assets/tpl/partials/building-detail-units-tab.html'"
            ></div>
          </div>
          <div
            title="Máquinas"
            ng-if="activeTab === 1"
            cg-busy="loadingMachines"
          >
            <div
              ng-include
              src="'assets/tpl/partials/building-detail-machines-tab.html'"
            ></div>
          </div>
          <div
            title="Mantenimientos"
            ng-if="activeTab === 2"
            cg-busy="loadingMaintenances"
          >
            <div
              ng-include
              src="'assets/tpl/partials/building-detail-maintenances-tab.html'"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/ng-template" id="/unit-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingUnit">

              <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nueva'}} Unidad
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Torre</label>
                          <input type="text" class="form-control" ng-model="item.tower" ng-disabled="!item.editing"
                                 required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Número</label>
                          <input type="text" class="form-control" ng-model="item.number" ng-disabled="!item.editing"
                                 required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Email Dueño</label>
                          <input type="text" class="form-control" ng-model="item.contact" ng-disabled="!item.editing">
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<script type="text/ng-template" id="/uses-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="uploadingUses">

              <form class="form-floating" novalidate="novalidate">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>Cargar archivo de Usos</legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Archivo</label>
                          <input type="file" ngf-select class="form-control" required ng-model="data.file">
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-click="uploadFile(data.file)">Cargar
                          </button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<script type="text/ng-template" id="/building-file-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="uploadingUses">

              <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>Cargar archivo</legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Archivo</label>
                          <input type="file" ngf-select class="form-control" required ng-model="data.file">
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-click="uploadBuildingFile(data.file)">
                              Cargar
                          </button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<script type="text/ng-template" id="/maintenance-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingMaintenance">

              <form class="form-floating" ng-submit="saveMaintenance(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nueva'}}
                          Mantenimiento
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Mantenimiento</label>
                          <select class="form-control" style="width:180px; display:inline"
                                  ng-model="item.maintenance_type" required>
                              <option value="0">MP100</option>
                              <option value="1">MP500</option>
                              <option value="2">MP1200</option>
                          </select>
                      </div>

                      <div class="form-group">
                          <label class="control-label"><i class="fa fa-calendar"></i>Fecha y Hora</label><br>
                          <div class="form-group">
                              <input type="text" size="10" class="form-control" ng-model="item.timestamp"
                                     data-autoclose="1" placeholder="Fecha" bs-datepicker required>
                          </div>
                          <div class="form-group">
                              <input type="text" size="8" class="form-control" ng-model="item.timestamp"
                                     data-time-format="HH:mm" data-autoclose="1" placeholder="Hora" bs-timepicker
                                     required>
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Técnico</label>
                          <select class="form-control" style="width:180px; display:inline"
                                  ng-model="item.technician" required
                                  ng-options="technician as technician for technician in technicians track by technician">
                          </select>
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>
