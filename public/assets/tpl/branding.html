<div class="dashboard lighten-3" ng-controller="BrandingController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Administración de Accesorios</h4>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div bs-tabs>
          <div bs-pane title="Solicitudes">
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshRequests()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.partQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingRequests">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>Solicitado</th>
                      <th>Cantidad</th>
                      <th>Accesorio Nombre</th>
                      <th>Email</th>
                      <th>Direcci&oacute;n</th>
                      <th>Entregado?</th>
                      <th>Entregado</th>
                      <th>Accesorio Id</th>
                      <th>Transacci&oacute;n Id</th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="request in data.requests | filter:filters.partQuickSearch"
                    >
                      <td>{{request.id}}</td>
                      <td>{{request.creation_date | date:'yyyy-MM-dd'}}</td>
                      <td>{{request.quantity}}</td>
                      <td>{{request.item_name}}</td>
                      <td>{{request.contact_email}}</td>
                      <td>{{request.address}}</td>
                      <td>
                        <span
                          class="glyphicon"
                          ng-class="[request.delivered ? 'glyphicon-ok text-success' : 'glyphicon-remove text-danger']"
                          title="{{request.delivered ? 'Entregado' : 'Pendiente'}}"
                        >
                        </span>
                      </td>
                      <td>{{request.delivery_date | date:'yyyy-MM-dd'}}</td>
                      <td>{{request.item_id}}</td>
                      <td>{{request.transaction_id}}</td>
                      <td>
                        <button
                          class="btn btn-xs btn-success"
                          ng-click="deliverRequest(request)"
                          ng-if="!request.delivered"
                        >
                          Entregar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div bs-pane title="Accesorios">
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createItem()"
                  >
                    Crear Accesorio
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshItems()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.partQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingItems">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>Nombre</th>
                      <th>Precio</th>
                      <th>Descripci&oacute;n</th>
                      <th>Referencia</th>
                      <th>Imagen</th>
                      <th>Acci&oacute;n</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="item in data.items | filter:filters.partQuickSearch"
                    >
                      <td>{{item.id}}</td>
                      <td>{{item.name}}</td>
                      <td>{{item.price}}</td>
                      <td>
                        <div ng-bind-html="item.description"></div>
                      </td>
                      <td>{{item.reference}}</td>
                      <td>{{item.imageName}}</td>
                      <td>
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editItem(item)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deleteItem(item)"
                        >
                          Borrar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------- branding item form ----------- -->
<script type="text/ng-template" id="/branding-item-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">

    <div class="close">
      <div class="btn btn-round btn-info" ng-click="$hide()">
        <i class="md md-close"></i>
      </div>
    </div>

    <div class="aside-dialog">
      <div class="aside-body bs-sidebar" cg-busy="submittingItem">
        <form class="form-floating" novalidate="novalidate" ng-submit="submitItem(item)">
          <fieldset>
            <legend><span ng-bind-html="item.icon"></span>{{settings.cmd == 'New' ? 'Crear' : 'Editar'}} Accesorio</legend>

            <div class="bs-component" ng-if="errorMessage != null">
              <div class="alert alert-dismissible alert-danger">
                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                <h4>Error</h4>
                {{errorMessage}}
              </div>
            </div>

            <div class="form-group filled">
              <label class="control-label">Nombre</label>
              <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing" required>
              <p>
                <span class="md md-info-outline"></span>
                <i>Nombre del accesorio visible en el sitio.</i>
              </p>
            </div>

            <div class="form-group filled">
              <label class="control-label">Descripción</label>
              <trix-editor
                angular-trix ng-model="item.description"
                ng-disabled="!item.editing"
                trix-initialize='hideTrixToolbarItems()'
              ></trix-editor>
              <p>
                <span class="md md-info-outline"></span>
                <i>Descripción del accesorio visible en el sitio.</i>
              </p>
            </div>

            <div class="form-group filled">
              <label class="control-label">Imagen</label>
              <input type="text" class="form-control" ng-model="item.imageName" ng-disabled="!item.editing">
              <p>
                <span class="md md-info-outline"></span>
                <i>Nombre de la imagen que va a ser visible en el sitio. Coordinar con desarrollo el valor de este campo.</i>
              </p>
            </div>

            <div class="form-group filled">
              <label class="control-label">Referencia</label>
              <input type="text" class="form-control" ng-model="item.reference" ng-disabled="!item.editing">
              <p>
                <span class="md md-info-outline"></span>
                <i>Referencia para la navegación en el sitio, es el valor que se encuentra al final de la URL. Ejemplo: "washing-bag" en https://www.lavomat.com.uy/accessories#washing-bag</i>
              </p>
            </div>

            <div class="form-group filled">
              <label class="control-label">Rate</label>
              <select class="form-control" ng-model="item.rateId" ng-disabled="!item.editing" ng-options="r.id as r.name for r in data.rates track by r.id"></select>
              <p>
                <span class="md md-info-outline"></span>
                <i>Rate que indica el precio del cliente en el sitio.</i>
              </p>
            </div>

            <div class="form-group">
              <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
            </div>

          </fieldset>
        </form>
      </div>
    </div>

  </div>
</script>
