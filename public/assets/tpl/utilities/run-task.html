<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Run task</h4>
      </div>

      <form class="form-floating" novalidate="novalidate" ng-submit="runTask()">
        <div class="modal-body" cg-busy="task.sending">
          <div class="form-group filled">
            <label class="control-label">Key</label>
            <input
              type="text"
              class="form-control"
              ng-model="task.key"
              required
            />
          </div>

          <details ng-if="task.result || task.error" class="collapsible-item">
            <summary
              ng-class="{'text-success': task.result, 'text-danger': task.error}"
            >
              <span ng-click="toggleTask()"
                >{{(task.result && 'Response') || (task.error &&
                'Error')}}</span
              >
            </summary>

            <div
              ng-bind-html="task.result || task.error"
              class="response"
            ></div>
          </details>
        </div>

        <div class="modal-footer">
          <div class="form-group">
            <button type="submit" class="btn btn-primary">Run</button>
            <button
              type="button"
              class="btn btn-default"
              ng-click="cleanTask()"
            >
              Clean
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
