<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Get {{results.action}}</h4>
      </div>

      <form
        class="form-floating"
        novalidate="novalidate"
        ng-submit="getResults()"
      >
        <div class="modal-body" cg-busy="results.sending">
          <details
            ng-if="results.result || results.error"
            class="collapsible-item"
          >
            <summary
              ng-class="{'text-success': results.result, 'text-danger': results.error}"
            >
              <span ng-click="toggleResults()"
                >{{(results.result && 'Response') || (results.error &&
                'Error')}}</span
              >
            </summary>

            <div
              ng-bind-html="results.result || results.error"
              class="response"
            ></div>
          </details>
        </div>

        <div class="modal-footer">
          <div class="form-group">
            <button type="submit" class="btn btn-primary">Get</button>
            <button
              type="button"
              class="btn btn-default"
              ng-click="cleanResults()"
            >
              Clean
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
