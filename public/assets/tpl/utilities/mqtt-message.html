<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Enviar un MQTT de prueba</h4>
      </div>

      <form
        class="form-floating"
        novalidate="novalidate"
        ng-submit="sendMessage(message)"
      >
        <div class="modal-body" cg-busy="message.sending">
          <div class="form-group filled">
            <label class="control-label">Building Id</label>
            <input
              type="number"
              class="form-control"
              ng-model="message.buildingId"
              required
            />
          </div>

          <div class="form-group filled">
            <label class="control-label">Machine serial number</label>
            <input
              type="text"
              class="form-control"
              ng-model="message.serialNumber"
              required
            />
          </div>

          <div class="form-group filled">
            <label class="control-label">JSON</label>
            <textarea
              class="form-control"
              ng-model="message.json"
              placeholder='{"UUID": "Ox45476df","BUILDING_ID": "1234","MACHINE_SERIAL": "machine2335h","TRANSACTION_ID": "1","RESULT": "2","TIMESTAMP": "1685549200"}'
              required
              resize="none"
            >
            </textarea>
          </div>

          <details
            ng-if="message.result || message.error"
            class="collapsible-item"
          >
            <summary
              ng-class="{'text-success': message.result, 'text-danger': message.error}"
            >
              <span ng-click="toggleMessage()"
                >{{(message.result && 'Response') || (message.error &&
                'Error')}}</span
              >
            </summary>

            <div
              ng-bind-html="message.result || message.error"
              class="response"
            ></div>
          </details>
        </div>

        <div class="modal-footer">
          <div class="form-group">
            <button type="submit" class="btn btn-primary">Enviar</button>
            <button
              type="button"
              class="btn btn-default"
              ng-click="cleanMessage()"
            >
              Limpiar
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
