<div class="dashboard lighten-3" ng-controller="MachineModelController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Modelos de máquinas
        <button
          class="btn btn-xs btn-info pull-right"
          ng-click="createMachineModel()"
        >
          Crear Nuevo
        </button>
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refreshMachineModel()"
        >
          Recargar
        </button>
        <input
          type="text"
          class="form-control input-sm pull-right"
          style="margin-right: 10px; max-width: 25%"
          ng-model="filters.quickSearch"
          placeholder="Búsqueda Rápida"
        />
      </h4>
    </div>
  </div>
  <div class="row" cg-busy="loadingMachineModels">
    <div class="col-lg-12">
      <table class="table table-full table-hover">
        <thead>
          <tr>
            <th>Id</th>
            <th>Nombre</th>
            <th>MP100</th>
            <th>MP1300</th>
            <th>MP1200</th>
            <th>Acción</th>
          </tr>
        </thead>
        <tbody>
          <tr ng-repeat="model in machineModels | filter:filters.quickSearch">
            <td>{{model.id}}</td>
            <td>{{model.name}}</td>
            <td>{{model.mp100}}</td>
            <td>{{model.mp500}}</td>
            <td>{{model.mp1200}}</td>
            <td style="overflow: visible">
              <button
                class="btn btn-xs btn-warning"
                ng-click="editMachineModel(model)"
              >
                Editar
              </button>
              <button
                class="btn btn-xs btn-danger"
                ng-click="deleteMachineModel(model)"
              >
                Borrar
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script type="text/ng-template" id="/machine-model-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingMachineModel">

              <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                  <fieldset>
                      <legend>
                          <span ng-bind-html="item.icon"></span>Crear nuevo Modelo de Máquina
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" min="0" ng-model="item.name"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP100</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp100"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP500</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp500"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP1200</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp1200"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>
