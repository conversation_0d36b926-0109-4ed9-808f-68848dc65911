<div class="dashboard lighten-3" ng-controller="ReportsController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Reportes</h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>

              <div class="row">
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.from"
                    data-max-date="{{untilDate}}"
                    placeholder="Desde"
                    bs-datepicker
                  />
                </div>
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.to"
                    data-min-date="{{fromDate}}"
                    placeholder="Hasta"
                    bs-datepicker
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  Edificio
                  <select
                    class="form-control"
                    ng-model="filters.building"
                    style="width: 100%"
                    ng-options="building as building.name for building in data.buildings track by building.id"
                  ></select>
                </div>
                <div class="col-md-3">
                  Unidad
                  <select
                    class="form-control"
                    ng-model="filters.unit"
                    ng-disabled="!filters.building"
                    style="width: 100%"
                    ng-options="unit as unit.number for unit in filters.building.units track by unit.id"
                  ></select>
                </div>
                <div class="col-md-3">
                  Máquina
                  <select
                    class="form-control"
                    ng-model="filters.machine"
                    style="width: 100%"
                    ng-options="machine.serial_number for machine in data.machines track by machine.id"
                  ></select>
                </div>
                <div class="col-md-3">
                  Tarjeta
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.card"
                    style="width: 100%"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-body" cg-busy="loadingResults">
          <table class="table">
            <thead>
              <tr>
                <th>Edificio</th>
                <th>Concepto</th>
                <th>Tarjeta / unidad</th>
                <th>Fecha</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="u in results">
                <td>{{u.unit.building.name}}</td>
                <td>{{u.concept}}</td>
                <td>
                  {{u.card.uuid + ' / ' + u.card.unit_tower + '-' +
                  u.card.unit_number}}
                </td>
                <td>{{u.timestamp | date}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
