<div class="dashboard lighten-3" ng-controller="UsersController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Administración de Usuarios</h4>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="row-fluid">
          <div class="panel-body">
            <div class="col-lg-12" cg-busy="loadingUsers">
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon" id="basic-addon1"></span>
                  <div class="bs-component" ng-if="errorMessage">
                    <div class="alert alert-dismissible alert-danger">
                      <button
                        type="button"
                        class="close"
                        data-dismiss="alert"
                        ng-click="clearError()"
                      >
                        ×
                      </button>
                      <h4>Error</h4>
                      {{errorMessage}}
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-3">
                      Búsqueda Rápida
                      <input
                        class="form-control"
                        ng-model="filters.keyword"
                        style="width: 100%"
                        type="text"
                      />
                    </div>
                    <div class="col-md-3">
                      ROL
                      <button
                        class="btn btn-xs"
                        ng-click="clearFilter('role')"
                        style="float: right"
                      >
                        <span
                          class="glyphicon glyphicon-remove"
                          title="Limpiar filtro"
                        ></span>
                      </button>
                      <select
                        class="form-control"
                        ng-model="filters.role"
                        ng-options="key as value for (key , value) in roles"
                        style="width: 100%; display: inline"
                      ></select>
                    </div>
                    <div class="panel-footer text-right">
                      <button
                        class="btn btn-xs btn-info pull-right"
                        ng-click="createUser()"
                      >
                        Crear Usuario
                      </button>
                      <button
                        class="btn btn-xs btn-warning pull-right"
                        style="margin-right: 10px"
                        ng-click="refreshUsers()"
                      >
                        Buscar
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="height: 20px"></div>
          <div class="row">
            <div class="col-lg-12">
              <table class="table table-full table-hover">
                <thead>
                  <tr>
                    <th>Id</th>
                    <th>Nombre Completo</th>
                    <th>Email</th>
                    <th>Rol</th>
                    <th>Acción</th>
                  </tr>
                </thead>
                <tbody>
                  <tr ng-repeat="user in users">
                    <td>{{user.id}}</td>
                    <td>{{user.name}} {{user.lastname}}</td>
                    <td>{{user.email}}</td>
                    <td>{{user.role}}</td>
                    <td>
                      <button
                        class="btn btn-xs btn-warning"
                        ng-click="editUser(user)"
                      >
                        Editar
                      </button>
                      <button
                        class="btn btn-xs btn-danger"
                        ng-click="deleteUser(user)"
                      >
                        Borrar
                      </button>
                      <button
                        ng-if="!user.validated"
                        class="btn btn-xs btn-primary"
                        ng-click="resendAccountValidationEmail(user)"
                      >
                        Reenviar Validación
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/ng-template" id="/user-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
        <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>
      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingUser">
              <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' :'Crear nuevo'}} Usuario</legend>
                      <div class="bs-component" ng-if="errorMessage">
                          <div class="alert alert-dismissible alert-danger">
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing" required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Apellido</label>
                          <input type="text" class="form-control" ng-model="item.lastname" ng-disabled="!item.editing" required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Email</label>
                          <input type="text" class="form-control" ng-model="item.email" ng-disabled="!item.editing" required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Password</label>
                          <input type="password" class="form-control" ng-model="item.password" required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Confirmación de Password</label>
                          <input type="password" class="form-control" ng-model="item.passwordConfirm" required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Rol</label>
                          <select class="form-control" ng-model="item.role" ng-disabled="!item.editing"
                                  ng-options="key as value for (key , value) in roles" ng-change="loadDependenciesByRole(item.role)">
                          </select>
                      </div>
                      <div class="form-group filled" ng-if="item.role === 'TOTEM'" cg-busy="loadingBuildings">
                          <label class="control-label">Edificio</label>
                          <select class="form-control" ng-model="item.buildingId" style="width:100%" ng-disabled="!item.editing"
                              ng-options="building.id as building.name for building in buildings">
                          </select>
                      </div>
                      <div class="form-group filled" ng-if="item.role === 'BUILDING_ADM'" cg-busy="loadingAdministrations">
                          <label class="control-label">Administración</label>
                          <select class="form-control" ng-model="item.administrationId" style="width:100%" ng-disabled="!item.editing"
                                  ng-options="administration.id as administration.name for administration in administrations">
                          </select>
                      </div>
                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>
                  </fieldset>
              </form>
          </div>
      </div>
  </div>
</script>
