<div class="dashboard lighten-3" ng-controller="RatesController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Administración de Rates
        <button class="btn btn-xs btn-info pull-right" ng-click="createRate()">
          Crear rate
        </button>
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refreshRates()"
        >
          Recargar
        </button>
        <input
          type="text"
          class="form-control input-sm pull-right"
          style="margin-right: 10px; max-width: 25%"
          ng-model="filters.quickSearch"
          placeholder="Búsqueda Rápida"
        />
      </h4>
    </div>
  </div>
  <div class="row" cg-busy="loadingRates">
    <div class="col-lg-12">
      <table class="table table-full table-hover">
        <thead>
          <tr>
            <th>Id</th>
            <th>Nombre</th>
            <th>Tipo</th>
            <th>Mensaje de Descripción</th>
            <th>IVA aplicado</th>
            <th><PERSON><PERSON>imo de Usos Totales</th>
            <th>Mínimo de Usos por Unidad</th>
            <th>Precio Cliente</th>
            <th>Precio Empresa</th>
            <th>Precio m3</th>
            <th>Precio KWh</th>
            <th>Precio Tarjeta</th>
            <th>Periodo</th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="rate in rates | filter:filters.quickSearch"
            ng-class="{ 'tr-danger': rate.lastExpiration <= limitDate, 'black-text tr-warning': rate.canNotifyAdmin }"
          >
            <td>{{rate.id}}</td>
            <td>{{rate.name}}</td>
            <td>{{rate.rateType}}</td>
            <td>{{rate.descriptiveMessage}}</td>
            <td>{{rate.appliesIVA ? 'Sí' : 'No'}}</td>
            <td>{{rate.minUsesPerWasher}}</td>
            <td>{{rate.minUsesPerUnit}}</td>
            <td>{{rate.priceCustomer}}</td>
            <td>{{rate.priceCompany}}</td>
            <td>{{rate.priceM3}}</td>
            <td>{{rate.priceKWh}}</td>
            <td>{{rate.priceCardReplacement}}</td>
            <td>{{rate.validFromPretty}} / {{rate.validUntilPretty}}</td>
            <td>
              <button class="btn btn-xs btn-warning" ng-click="editRate(rate)">
                Editar
              </button>
              <button
                class="btn btn-xs btn-success"
                ng-click="addRateEvent(rate)"
              >
                Nuevo Rango
              </button>
              <button
                class="btn btn-xs btn-default"
                ng-click="viewHistory(rate)"
              >
                Historico
              </button>
              <button
                class="btn btn-xs btn-primary"
                ng-if="rate.canNotifyAdmin"
                ng-click="notifyNewRate(rate)"
              >
                Notificar Adm
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script type="text/ng-template" id="/rate-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
  <div class="close">
    <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
  </div>

  <div class="aside-dialog">
    <div class="aside-body bs-sidebar" cg-busy="savingRate">

      <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
        <fieldset>
          <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' :'Crear nuevo'}} Rate</legend>

  		<div class="bs-component" ng-if="errorMessage != null">
              <div class="alert alert-dismissible alert-danger">
                  <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                  <h4>Error</h4>
                  {{errorMessage}}
              </div>
          </div>

          <div class="form-group filled">
            <label class="control-label">Nombre</label>
            <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing" required>
          </div>

          <div class="form-group filled">
              <label class="control-label">Tipo</label>
              <select class="form-control" ng-model="item.rateType" ng-disabled="!item.editing" required>
                  <option value="FIXED_RETURN">FIXED_RETURN</option>
                  <option value="CONSUMPTION_BASED">CONSUMPTION_BASED</option>
              </select>
          </div>

          <div class="form-group filled">
              <label class="control-label">Descripcion Visible</label>
              <input type="text" class="form-control" ng-model="item.descriptiveMessage" ng-disabled="!item.editing" required>
          </div>

  		<div class="form-group filled">
              <label class="control-label">Aplicar IVA?</label>
              <div class="checkbox">
                  <br>
                  <input type="checkbox" ng-model="item.appliesIVA" ng-disabled="!item.editing"/>
              </div>
          </div>

          <div class="form-group">
            <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
          </div>

        </fieldset>
      </form>

    </div>
  </div>
  </div>
</script>

<script type="text/ng-template" id="/rate-event-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingEvent || loadHistory">

              <form class="form-floating" novalidate="novalidate" ng-submit="saveEvent(event)">
                  <fieldset>
                      <legend><span ng-bind-html="event.icon"></span>{{event.cmd == 'Edit' ? 'Editar' :'Agregar Nuevo'}} Rango para <strong>{{event.rate.name}}</strong></legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                        <label class="control-label">Mínimo de Usos Totales</label>
                        <input type="number" class="form-control" ng-model="event.minUsesPerWasher" ng-pattern="/^[0-9]+$/">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Mínimo de Usos Por Unidad</label>
                          <input type="number" class="form-control" ng-model="event.minUsesPerUnit" ng-pattern="/^[0-9]+$/">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio Cliente</label>
                          <input type="number" class="form-control" ng-model="event.priceCustomer" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio Empresa</label>
                          <input type="number" class="form-control" ng-model="event.priceCompany" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio m3</label>
                          <input type="number" class="form-control" ng-model="event.priceM3">
                      </div>


                      <div class="form-group filled">
                          <label class="control-label">Precio KWh</label>
                          <input type="number" class="form-control" ng-model="event.priceKWh">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio Tarjeta</label>
                          <input type="number" class="form-control" ng-model="event.priceCardReplacement" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Desde</label>
                          <input type="text" class="form-control" ng-model="event.validFrom" data-max-date="{{event.validUntil}}" bs-datepicker required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Hasta</label>
                          <input type="text" class="form-control" ng-model="event.validUntil" data-min-date="{{event.validFrom}}" bs-datepicker required>
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>
