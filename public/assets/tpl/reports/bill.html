<div class="dashboard lighten-3" ng-controller="ReportsController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Reportes</h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  Edificio
                  <select
                    class="form-control"
                    ng-model="filters.building"
                    style="width: 100%"
                    ng-options="building as building.name for building in data.buildings track by building.id"
                    ng-change="handleBuildingChange()"
                  ></select>
                </div>
                <div class="col-md-3">
                  <PERSON><PERSON><PERSON>?
                  <input
                    type="checkbox"
                    class="form-control"
                    ng-model="filters.onlyCardEvents"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>

              <div class="row">
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.from"
                    data-max-date="{{untilDate}}"
                    placeholder="Desde"
                    bs-datepicker
                  />
                </div>
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.to"
                    data-min-date="{{fromDate}}"
                    placeholder="Hasta"
                    bs-datepicker
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
          <button class="btn btn-success btn-xs" ng-click="exportToExcel()">
            Exportar a Excel
          </button>
          <button class="btn btn-success btn-xs" ng-click="bill()">
            Facturar
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-body" cg-busy="loadingResults || loadingBuilding">
          <div
            class="row"
            ng-repeat="unit in filters.units"
            style="border-bottom: 1px solid #ddd; padding: 10px"
          >
            <div class="col-md-12">
              <strong>
                Torre {{unit.tower}}, Unidad {{unit.number}},
                {{getUsesByUnit(unit, results).length > 0 ? getUsesByUnit(unit,
                results).length : 'Sin'}} items
              </strong>
            </div>
            <div
              class="col-md-12"
              ng-repeat="u in getUsesByUnit(unit, results)"
            >
              {{u.concept}} el {{u.timestamp | date}}
              <span
                class="glyphicon glyphicon-exclamation-sign"
                ng-if="u.alert"
                title="Uso sospechoso"
              ></span>
              <span
                class="glyphicon glyphicon-remove"
                ng-if="!u.accredited"
                title="Uso anulado"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
