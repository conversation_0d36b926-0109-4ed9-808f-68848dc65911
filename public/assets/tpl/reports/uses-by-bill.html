<div class="dashboard lighten-3" ng-controller="UsesByBillReportController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Usos por factura</h4>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <span class="input-group-addon" id="basic-addon2"></span>
            <div class="row">
              <div class="col-md-3">
                Factura
                <input
                  class="form-control"
                  ng-model="filters.billNumber"
                  style="width: 100%"
                  type="text"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button
            class="btn btn-success btn-sm"
            ng-click="search()"
            ng-disabled="loadingResults"
          >
            Buscar
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row" ng-if="results && results.length">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div cg-busy="loadingResults" class="panel-body">
          <p><strong>Datos de la búsqueda: </strong></p>
          <p><strong>Total de usos activos: </strong>{{activeUses}}</p>
          <button
            class="btn btn-info btn-xs pull-right"
            ng-click="exportToCSV()"
            ng-disabled="loadingResults"
          >
            Exportar CSV
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row" cg-busy="loadingResults">
    <div class="col-lg-12">
      <table class="table table-full table-hover table-mp">
        <thead class="sticky-below-navbar">
          <th style="text-align: center; vertical-align: middle">Índice</th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('machine.building.name')"
          >
            Edificio
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'machine.building.name'"
            />
          </th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('machine.serial_number')"
          >
            Máquina
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'machine.serial_number'"
            />
          </th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('typeDesc')"
          >
            Tipo
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'typeDesc'"
            />
          </th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('unitCard')"
          >
            Unidad / tarjeta
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'unitCard'"
            />
          </th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('timestamp')"
          >
            Fecha
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'timestamp'"
            />
          </th>
          <th style="text-align: center; vertical-align: middle">Hora</th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('result')"
          >
            Result
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'result'"
            />
          </th>
          <th
            style="text-align: center; vertical-align: middle"
            ng-click="orderColumn('channel')"
          >
            Medio
            <span
              class="sortorder"
              ng-class="{reverse: filters.reverse}"
              ng-show="filters.orderBy === 'channel'"
            />
          </th>
          <th style="text-align: center; vertical-align: middle">Acción</th>
        </thead>
        <tbody>
          <tr
            ng-repeat-start="use in results | orderBy: filters.orderBy: filters.reverse"
          >
            <td style="text-align: center; vertical-align: middle">
              {{($index + 1)}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.machine.building.name}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.machine.sortIndex + " - " + use.machine.serial_number}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.typeDesc}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.unit}} {{use.card.uuid}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.date}}
              <span
                class="glyphicon glyphicon-exclamation-sign"
                ng-if="use.alert"
              ></span>
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.time}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.result}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              {{use.channel}}
            </td>
            <td style="text-align: center; vertical-align: middle">
              <button
                ng-class="{'btn btn-xs btn-danger': use.accredited, 'btn btn-xs btn-warning': !use.accredited}"
                no-propagate="modifyAccreditUse(use)"
                no-propagate-model="use"
                ng-if="!use.isMaintenance"
              >
                <span>{{use.accredited ? 'Desacreditar' : 'Acreditar'}}</span>
              </button>
            </td>
          </tr>
          <!--                        LEAVING THIS FOR A FUTURE CARD-->
          <tr ng-if="filters.building" ng-repeat-end>
            <!--                <td colspan="11">CIERRE PERIODO 2023-19-34</td>-->
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
