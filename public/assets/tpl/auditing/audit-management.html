<div class="dashboard lighten-3" ng-controller="AuditController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Auditoría de Saldos</h4>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>
              <div class="row">
                <div class="col-md-6">
                  <input
                    bs-datepicker
                    class="form-control"
                    data-max-date="{{untilDate}}"
                    ng-model="filters.from"
                    placeholder="Desde"
                    type="text"
                  />
                </div>
                <div class="col-md-6">
                  <input
                    bs-datepicker
                    class="form-control"
                    data-min-date="{{fromDate}}"
                    ng-model="filters.to"
                    placeholder="Hasta"
                    type="text"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  UID
                  <input
                    class="form-control"
                    ng-model="filters.sender"
                    style="width: 100%"
                    type="text"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div cg-busy="loadingResults" class="panel-body table-responsive">
          <table class="table">
            <thead>
              <td colspan="9">
                <div class="p-20">
                  <ul class="pager ng-cloak">
                    <div class="btn-group">
                      <button
                        class="btn btn-default"
                        ng-class="{'active': size == pagination.perPage, 'disabled': results && !results.length }"
                        ng-click="setPageSize(size)"
                        ng-repeat="size in pagination.pageSizes"
                        ng-value="size"
                        type="button"
                      >
                        {{size}}
                      </button>
                    </div>
                    <li>
                      <button
                        class="btn btn-default pull-left"
                        ng-class="{'disabled': pagination.page == 1}"
                        ng-click="setPage(pagination.page-1)"
                        type="button"
                      >
                        &laquo; Previo
                      </button>
                    </li>
                    <li>
                      <button
                        class="btn btn-default pull-right"
                        ng-class="{'disabled': results && !results.length}"
                        ng-click="setPage(pagination.page + 1)"
                        type="button"
                      >
                        Siguiente &raquo;
                      </button>
                    </li>
                  </ul>
                </div>
              </td>
            </thead>
            <tr>
              <th ng-click="orderColumn('id')" class="pointer">
                <u>Id</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'id'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('amount')" class="pointer">
                <u>Cantidad</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'amount'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('creationTimestamp')" class="pointer">
                <u>Fecha de Creación</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'creationTimestamp'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('details')" class="pointer">
                <u>Detalles</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'detailsx'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('type')" class="pointer">
                <u>Tipo</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'type'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('sender')" class="pointer">
                <u>UID Emisora</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'sender'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('receiver')" class="pointer">
                <u>UID Receptora</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'receiver'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Balance Previo</th>
              <th>Balance Nuevo</th>
            </tr>
            <tr ng-repeat="audit in results">
              <td>{{audit.id}}</td>
              <td>{{audit.amount}}</td>
              <td>{{audit.creationTimestamp}}</td>
              <td>{{audit.details}}</td>
              <td>{{audit.type}}</td>
              <td>{{audit.sender}}</td>
              <td>{{audit.receiver}}</td>
              <td>{{audit.previousBalance}}</td>
              <td>{{audit.newBalance}}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
