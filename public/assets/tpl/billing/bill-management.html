<div class="dashboard lighten-3" ng-controller="BillingController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Gestión de Facturas
        <button
          class="btn btn-xs btn-primary pull-right"
          ng-click="showCreateNewBill()"
        >
          Crear Factura
        </button>
      </h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  Administración
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('administration')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.administration"
                    style="width: 100%"
                    ng-options="administration as administration.name for administration in data.administrations track by administration.id"
                  ></select>
                </div>
                <div class="col-md-3">
                  Edificio
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('building')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.building"
                    style="width: 100%"
                    ng-options="building as building.name for building in data.buildings track by building.id"
                  ></select>
                </div>

                <div class="col-md-3">
                  Tipo
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('type')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.type"
                    style="width: 100%"
                    ng-options="item for item in data.types"
                  ></select>
                </div>

                <div class="col-md-3">
                  POST/PRE
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('mode')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.mode"
                    style="width: 100%"
                    ng-options="item for item in data.modes"
                  ></select>
                </div>

                <div class="col-md-3">
                  Número DGI
                  <input
                    class="form-control"
                    ng-model="filters.dgiNumber"
                    style="width: 100%"
                    type="text"
                    placeholder="A - 1"
                  />
                </div>

                <div class="col-md-3">
                  Cobrador
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('collector')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.collector"
                    style="width: 100%"
                  >
                    <option
                      value="{{user.id}}"
                      ng-repeat="user in data.collectors"
                    >
                      {{user.name}} {{user.lastname}}
                    </option>
                  </select>
                </div>

                <div class="col-md-3">
                  Cobranza
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('collectionStatus')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.collectionStatus"
                    style="width: 100%"
                  >
                    <option
                      value="{{key}}"
                      ng-repeat="key in collectionStatusKeys"
                    >
                      {{collectionStatus[key]}}
                    </option>
                  </select>
                </div>

                <div class="col-md-3">
                  Estado
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('state')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.state"
                    style="width: 100%"
                    ng-options="state.key as state.text for state in states"
                  ></select>
                </div>

                <div class="col-md-3">
                  Medio de Pago
                  <button
                    class="btn btn-xs"
                    ng-click="clearFilter('billOrigin')"
                    style="float: right"
                  >
                    <span
                      class="glyphicon glyphicon-remove"
                      title="Limpiar filtro"
                    ></span>
                  </button>
                  <select
                    class="form-control"
                    ng-model="filters.billOrigin"
                    style="width: 100%"
                    ng-options="billOrigin.key as billOrigin.text for billOrigin in billOrigins"
                  ></select>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>

              <div class="row">
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.from"
                    data-max-date="{{untilDate}}"
                    placeholder="Desde"
                    bs-datepicker
                  />
                </div>
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.to"
                    data-min-date="{{fromDate}}"
                    placeholder="Hasta"
                    bs-datepicker
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
          <button class="btn btn-success btn-xs" ng-click="exportToExcel()">
            Exportar a Excel
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-body table-responsive" cg-busy="loadingResults">
          <table class="table">
            <thead>
              <td colspan="9">
                <div class="p-20">
                  <ul class="pager ng-cloak">
                    <div class="btn-group">
                      <button
                        ng-repeat="size in pagination.pageSizes"
                        type="button"
                        class="btn btn-default"
                        ng-value="size"
                        ng-click="setPageSize(size)"
                        ng-class="{'active': size == pagination.perPage, 'disabled': results && !results.length }"
                      >
                        {{size}}
                      </button>
                    </div>
                    <li>
                      <button
                        type="button"
                        class="btn btn-default pull-left"
                        ng-class="{'disabled': pagination.page == 1}"
                        ng-click="setPage(pagination.page-1)"
                      >
                        &laquo; Previo
                      </button>
                    </li>
                    <li>
                      <button
                        type="button"
                        class="btn btn-default pull-right"
                        ng-class="{'disabled': results && !results.length}"
                        ng-click="setPage(pagination.page + 1)"
                      >
                        Siguiente &raquo;
                      </button>
                    </li>
                  </ul>
                </div>
              </td>
            </thead>
            <tr>
              <th>ID</th>
              <th>Tipo</th>
              <th>Receptor</th>
              <th ng-click="orderColumn('timestamp')" class="pointer">
                <u>Fecha de Factura</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'timestamp'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Estado</th>
              <th ng-click="orderColumn('number')" class="pointer">
                <u>Número DGI</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'number'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Periodo</th>
              <th ng-click="orderColumn('total')" class="pointer">
                <u>Monto</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'total'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>POST/PRE</th>
              <th>Cobranza</th>
              <th>Cobrador</th>
              <th>Fecha de Cobro</th>
              <th ng-click="orderColumn('billOrigin')" class="pointer">
                <u>Medio de Pago</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'billOrigin'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Acción</th>
            </tr>
            <tr
              ng-repeat="bill in results"
              ng-class="{ 'tr-danger': bill.hasCreditOrDebitNote }"
            >
              <td>{{bill.id}}</td>
              <td>{{bill.billType}}</td>
              <td>{{bill.billTo}}</td>
              <td>{{bill.timestamp}}</td>
              <td>{{bill.state}}</td>
              <td>{{bill.serie}} - {{bill.number}}</td>
              <td>
                {{bill.billedPeriodStart | date:'yyyy-MM-dd'}} /
                {{bill.billedPeriodEnd | date:'yyyy-MM-dd'}}
              </td>
              <td>{{bill.total | number:2}}</td>
              <td>{{bill.billToType}}</td>
              <td>{{collectionStatus[bill.billCollectionStatus]}}</td>
              <td>{{bill.collector}}</td>
              <td>{{bill.collectionDate}}</td>
              <td>{{bill.billOrigin}}</td>
              <td>
                <button
                  class="btn btn-xs btn-warning"
                  ng-click="viewBillDetails(bill)"
                >
                  Ver Detalle
                </button>
                <button
                  class="btn btn-xs btn-primary"
                  ng-click="viewBillPDF(bill)"
                  ng-if="bill.state != 'BELOW_MINIMUM'"
                >
                  Ver PDF
                </button>
                <button
                  class="btn btn-xs btn-danger"
                  ng-click="cancelBill(bill)"
                >
                  Anular
                </button>
                <button
                  class="btn btn-xs btn-success"
                  ng-click="editBillCollectionStatus(bill)"
                >
                  Promover
                </button>
                <button
                  class="btn btn-xs btn-default"
                  ng-click="editBillCollector(bill)"
                >
                  Asignar
                </button>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
