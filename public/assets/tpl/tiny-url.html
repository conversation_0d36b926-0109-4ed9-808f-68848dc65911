<div class="dashboard lighten-3" ng-controller="TinyUrlController">
  <div
    class="row sticky-below-navbar grey.lighten-5"
    style="background: #fafafa"
  >
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Administración de URLs

        <button
          class="btn btn-xs btn-success pull-right"
          style="margin-left: 10px"
          ng-click="createEntities()"
        >
          Crear en masa
        </button>
        <button
          class="btn btn-xs btn-info pull-right"
          ng-click="createEntity()"
        >
          Crear URL
        </button>
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refreshEntities()"
        >
          Recargar
        </button>
        <input
          type="text"
          class="form-control input-sm pull-right"
          style="margin-right: 10px; max-width: 25%"
          ng-model="filters.quickSearch"
          placeholder="Búsqueda Rápida"
        />
      </h4>
    </div>
  </div>

  <div class="row" cg-busy="loadingEntities">
    <div class="col-lg-12">
      <toast message="toast"></toast>

      <table class="table table-full table-hover">
        <thead style="position: sticky; top: 135px; z-index: 2">
          <tr>
            <th>Id</th>
            <th>Descripción</th>
            <th>URL origen</th>
            <th>URL destino</th>
            <th>Asociada a</th>
            <th ng-click="orderColumn('isActive')">
              Activa?
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'isActive'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('createdAt')">
              Creada
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'createdAt'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('updatedAt')">
              Editada
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'updatedAt'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="entity in data.entities | filter:filters.quickSearch | orderBy: filters.orderBy: filters.reverse"
          >
            <td>{{entity.id}}</td>
            <td>{{entity.description}}</td>
            <td>
              {{entity.url}}
              <div>
                <a class="pointer" ng-click="copy($event, entity.url)">
                  Copiar URL
                </a>
                |
                <a class="pointer" ng-click="copy($event, entity.token)">
                  Copiar token
                </a>
              </div>
            </td>
            <td>{{entity.destinationUrl}}</td>
            <td>{{entity.associatedTo}}</td>
            <td>
              <div class="switch">
                <label>
                  <input
                    type="checkbox"
                    ng-model="entity.isActive"
                    ng-change="toggleActivation(entity)"
                  />
                  <span class="lever"></span>
                </label>
              </div>
            </td>
            <td>{{entity.createdAt}}</td>
            <td>{{entity.updatedAt}}</td>
            <td>
              <button
                class="btn btn-xs btn-warning"
                no-propagate="editEntity(entity)"
                no-propagate-model="entity"
              >
                Editar
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- ----------- Entity form ----------- -->
<script type="text/ng-template" id="/tiny-url-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">

    <div class="close">
      <div class="btn btn-round btn-info" ng-click="$hide()">
        <i class="md md-close"></i>
      </div>
    </div>

    <div class="aside-dialog">
      <div class="aside-body bs-sidebar" cg-busy="submittingEntity">
        <form class="form-floating" novalidate="novalidate" ng-submit="submitEntity(entity)">
          <fieldset>
            <legend><span ng-bind-html="entity.icon"></span>{{settings.cmd == 'New' ? 'Crear' : 'Editar'}} URL</legend>

            <div class="bs-component" ng-if="errorMessage != null">
              <div class="alert alert-dismissible alert-danger">
                <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                <h4>Error</h4>
                {{errorMessage}}
              </div>
            </div>

            <div class="form-group filled">
              <label class="control-label">Descripción</label>
              <input type="text" class="form-control" ng-model="entity.description" ng-disabled="!entity.editing">
            </div>

            <div class="form-group filled" ng-if="!entity.associatedTo">
              <label class="control-label">URL destino</label>
              <input type="url" class="form-control" pattern="https://.*" ng-model="entity.destinationUrl" ng-disabled="!entity.editing" ng-required="entity.isActive">
            </div>

            <div class="form-group filled">
              <label class="control-label">Activo?</label>
              <div class="switch">
                <label>
                  <input
                    type="checkbox"
                    ng-model="entity.isActive"
                  />
                  <span class="lever"></span>
                </label>
              </div>
            </div>

            <div class="form-group">
              <button type="submit" class="btn btn-primary" ng-hide="!entity.editing">Guardar</button>
            </div>

          </fieldset>
        </form>
      </div>
    </div>

  </div>
</script>
