<div class="panel-heading">
  <button
    class="btn btn-xs btn-warning pull-right"
    style="margin-right: 10px"
    ng-click="loadUnits(true)"
  >
    Recargar</button
  >&nbsp;
  <button
    class="btn btn-xs btn-info pull-right"
    style="margin-right: 10px"
    ng-click="createUnit()"
  >
    Agregar Unidad
  </button>
  <button
    class="btn btn-xs btn-info pull-right"
    style="margin-right: 10px"
    ng-click="showLoadBuidling()"
  >
    Importar Archivo
  </button>
</div>
<div class="panel-body">
  <table class="table table-full table-hover">
    <thead>
      <tr>
        <th ng-click="orderUnitsColumn('tower')">
          Torre
          <span
            class="sortorder"
            ng-show="filters.unitsOrderBy === 'tower'"
            ng-class="{reverse: filters.unitsReverse}"
          />
        </th>
        <th ng-click="orderUnitsColumn('number')">
          Numero
          <span
            class="sortorder"
            ng-show="filters.unitsOrderBy === 'number'"
            ng-class="{reverse: filters.unitsReverse}"
          />
        </th>
        <th ng-click="orderUnitsColumn('contact')">
          Contacto
          <span
            class="sortorder"
            ng-show="filters.unitsOrderBy === 'contact'"
            ng-class="{reverse: filters.unitsReverse}"
          />
        </th>
        <th>Tarjetas</th>
        <th>Acción</th>
      </tr>
    </thead>
    <tbody>
      <tr
        ng-repeat="unit in units | orderBy: filters.unitsOrderBy: filters.unitsReverse"
      >
        <td>{{unit.tower}}</td>
        <td>{{unit.number}}</td>
        <td>{{unit.contact}}</td>
        <td>
          &#013;
          <div class="btn-group" ng-repeat="card in unit.assigned_cards">
            <button
              style="border-radius: 15px"
              class="btn badge dropdown-toggle"
              ng-class="{'blue':card.state=='ACTIVE','gray':(card.state=='INACTIVE' && card.substate=='SUSPENDED'), 'red':(card.state=='INACTIVE' && (card.substate=='LOST'||card.substate=='DAMAGED')), 'orange':(card.state=='PRE_BLOCKED')}"
              style="margin-right: 2px"
              data-toggle="dropdown"
              ng-click="loadCardEvents(card)"
            >
              {{card.uuid}}
            </button>
            <ul class="dropdown-menu" style="width: 240px">
              <li ng-click="copy($event, card.uuid)">
                <a>Copiar</a>
              </li>
              <li class="divider"></li>
              <li ng-if="card.state!='ACTIVE'">
                <a ng-click="enableCard(card, unit)">Habilitar</a>
              </li>
              <li ng-if="card.state=='ACTIVE'">
                <a ng-click="disableCard(card, unit)">Deshabilitar</a>
              </li>
              <li
                ng-if="building.setting.isPreBlockedUseEnabled && card.state != 'PRE_BLOCKED'"
              >
                <a ng-click="preBlockCard(card, unit)">Pre Bloquear</a>
              </li>
              <li ng-if="card.state=='ACTIVE'">
                <a ng-click="lostCard(card, unit)">Perdida</a>
              </li>
              <li ng-if="card.state=='ACTIVE'">
                <a ng-click="damagedCard(card, unit)">Dañada</a>
              </li>
              <li class="divider"></li>
              <li>
                <a href ng-click="unassignCardFromUnit(card, unit)"
                  >Desasignar</a
                >
              </li>
              <li class="divider"></li>
              <li class="m-15">
                <p ng-repeat="cardEvent in cardEvents">{{cardEvent}}</p>
              </li>
            </ul>
          </div>
          <span ng-if="unit.assigned_cards.length == 0">No tiene</span>
        </td>
        <td style="overflow: visible">
          <button class="btn btn-xs btn-primary" ng-click="editUnit(unit)">
            Editar
          </button>
          <div class="btn-group">
            <button
              ng-if="unit.assigned_card == null"
              class="btn btn-xs btn-warning"
              ng-click="showAssignCard(unit)"
            >
              Asignar Tarjeta
            </button>
          </div>
          <button class="btn btn-xs btn-danger" ng-click="deleteUnit(unit)">
            Borrar
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
