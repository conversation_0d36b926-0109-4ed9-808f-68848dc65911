<div class="panel-heading">
  <div class="col-lg-6">
    <strong>Parámetros de Mantenimiento</strong>
    <p>
      <span ng-if="!building.editMP100">
        <strong>MP100</strong>: {{ building.mp100 }}
      </span>
      <span ng-if="building.editMP100">
        <strong>MP100</strong>:
        <input
          type="text"
          class="form-control"
          ng-model="building.mp100"
          style="width: 180px; display: inline"
        />
      </span>
      <button
        ng-if="!building.editMP100"
        class="btn btn-xs btn-info"
        ng-click="building.editMP100 = true"
      >
        CAMBIAR
      </button>
      <button
        ng-if="building.editMP100"
        class="btn btn-xs btn-success"
        ng-click="saveMP100Parameter()"
      >
        GUARDAR
      </button>
      <button
        ng-if="building.editMP100"
        class="btn btn-xs btn-danger"
        ng-click="building.editMP100 = false"
      >
        CANCELAR
      </button>
    </p>

    <p>
      <span ng-if="!building.editMP500"
        ><strong>MP500</strong>: {{ building.mp500 }}
      </span>
      <span ng-if="building.editMP500"
        ><strong>MP500</strong>:
        <input
          type="text"
          class="form-control"
          ng-model="building.mp500"
          style="width: 180px; display: inline"
        />
      </span>
      <button
        ng-if="!building.editMP500"
        class="btn btn-xs btn-info"
        ng-click="building.editMP500 = true"
      >
        CAMBIAR
      </button>
      <button
        ng-if="building.editMP500"
        class="btn btn-xs btn-success"
        ng-click="saveMP500Parameter()"
      >
        GUARDAR
      </button>
      <button
        ng-if="building.editMP500"
        class="btn btn-xs btn-danger"
        ng-click="building.editMP500 = false"
      >
        CANCELAR
      </button>
    </p>

    <p>
      <span ng-if="!building.editMP1200"
        ><strong>MP1200</strong>: {{ building.mp1200 }}
      </span>
      <span ng-if="building.editMP1200"
        ><strong>MP1200</strong>:
        <input
          type="text"
          class="form-control"
          ng-model="building.mp1200"
          style="width: 180px; display: inline"
        />
      </span>
      <button
        ng-if="!building.editMP1200"
        class="btn btn-xs btn-info"
        ng-click="building.editMP1200 = true"
      >
        CAMBIAR
      </button>
      <button
        ng-if="building.editMP1200"
        class="btn btn-xs btn-success"
        ng-click="saveMP1200Parameter()"
      >
        GUARDAR
      </button>
      <button
        ng-if="building.editMP1200"
        class="btn btn-xs btn-danger"
        ng-click="building.editMP1200 = false"
      >
        CANCELAR
      </button>
    </p>
  </div>
  <button
    class="btn btn-xs btn-info pull-right"
    style="margin-right: 10px"
    ng-click="createMaintenance()"
  >
    Agregar Mantenimiento
  </button>
</div>
<div class="panel-body">
  <table class="table table-full table-hover">
    <thead>
      <tr>
        <th ng-click="orderMaintenanceColumn('timestamp')">
          Fecha
          <span
            class="sortorder"
            ng-show="filters.maintenanceOrderBy === 'timestamp'"
            ng-class="{reverse: filters.maintenanceReverse}"
          />
        </th>
        <th ng-click="orderMaintenanceColumn('maintenanceType')">
          Tipo
          <span
            class="sortorder"
            ng-show="filters.maintenanceOrderBy === 'maintenanceType'"
            ng-class="{reverse: filters.maintenanceReverse}"
          />
        </th>
        <th ng-click="orderMaintenanceColumn('technician')">
          Técnico
          <span
            class="sortorder"
            ng-show="filters.maintenanceOrderBy === 'technician'"
            ng-class="{reverse: filters.maintenanceReverse}"
          />
        </th>
        <th>Acción</th>
      </tr>
    </thead>
    <tbody>
      <tr
        ng-repeat="maintenance in maintenances | orderBy: filters.maintenanceOrderBy: filters.maintenanceReverse"
      >
        <td>{{maintenance.timestamp}}</td>
        <td>{{maintenance.maintenanceType}}</td>
        <td>{{maintenance.technician}}</td>
        <td style="overflow: visible">
          <button
            class="btn btn-xs btn-warning"
            ng-click="editMaintenance(maintenance)"
          >
            Editar
          </button>
          <button
            class="btn btn-xs btn-danger"
            ng-click="deleteMaintenance(maintenance)"
          >
            Borrar
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
