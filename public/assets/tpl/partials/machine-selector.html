<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="form-group">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" ng-click="$hide()">
            &times;
          </button>
          <h4 class="modal-title">Seleccionar <PERSON></h4>
        </div>
        <div class="bs-component" ng-if="errorMessage">
          <div class="alert alert-dismissible alert-danger">
            <button
              type="button"
              class="close"
              data-dismiss="alert"
              ng-click="clearError()"
            >
              ×
            </button>
            <h4>Error</h4>
            {{errorMessage}}
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <h4 class="p-15 grey-text">
              <button
                class="btn btn-xs btn-success pull-right"
                style="margin-right: 10px"
                ng-click="loadMachines()"
              >
                Buscar
              </button>
              <input
                type="text"
                class="form-control input-sm pull-right"
                style="margin-right: 10px; max-width: 35%"
                ng-model="filters.machineQuickSearch"
                placeholder="Número de Serie"
              />
            </h4>
          </div>
        </div>
        <div class="modal-body" cg-busy="loadingMachines">
          <table class="table">
            <thead>
              <tr>
                <th class="text-center align-middle">ID</th>
                <th class="text-center align-middle">Número de serie</th>
                <th class="text-center align-middle">Tipo</th>
                <th class="text-center align-middle">Seleccionar</th>
              </tr>
            </thead>
            <tbody>
              <tr
                ng-repeat="machine in machines"
                ng-class="{'selected': machine === selectedMachine}"
              >
                <td class="text-center align-middle">{{machine.id}}</td>
                <td class="text-center align-middle">
                  {{machine.serial_number}}
                </td>
                <td class="text-center align-middle">
                  {{machine.machine_type == 'WASHER' ? 'Lavadora' : 'Secadora'}}
                </td>
                <td class="text-center align-middle">
                  <input
                    type="checkbox"
                    class="form-group"
                    ng-checked="machine === selectedMachine"
                    ng-click="toggleSelection(machine)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
          <hr class="m-5" />
          <div class="modal-footer">
            <input
              type="submit"
              class="btn btn-success"
              ng-click="assignMachine(selectedMachine)"
              value="Asignar"
              ng-disabled="!selectedMachine"
            />
            <button type="button" class="btn btn-danger" ng-click="$hide()">
              Cancelar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
