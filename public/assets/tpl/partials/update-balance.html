<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="form-group">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" ng-click="$hide()">
            &times;
          </button>
          <h4 class="modal-title">Modificar Saldo</h4>
        </div>

        <div class="modal-body" cg-busy="loadingNewBalance">
          <form ng-submit="updateCardBalance()">
            <fieldset>
              <p><strong>Datos generales</strong></p>
              <div class="row">
                <div class="col-md-6 text-left">
                  <p><strong>UID Tarjeta</strong>: {{selectedCard.uuid}}</p>
                  <p><strong>Saldo Actual</strong>: {{selectedCard.balance}}</p>
                  <p>
                    <strong>Unidad/Edificio</strong>:
                    {{selectedCard.unit_number}}/{{selectedCard.unit_tower}} -
                    {{selectedCard.building}}
                  </p>
                  <p>
                    <strong>Precio Compañía / Precio Edificio</strong>:
                    ${{selectedCard.priceCompany}} /
                    ${{selectedCard.priceCustomer}}
                  </p>
                </div>
                <div class="col-md-6">
                  <div class="bs-component" ng-if="machinesErrorMessage">
                    <div class="alert alert-dismissible alert-danger">
                      <button
                        type="button"
                        class="close"
                        data-dismiss="alert"
                        ng-click="clearError()"
                      >
                        ×
                      </button>
                      <h4>Error</h4>
                      {{machinesErrorMessage}}
                    </div>
                  </div>
                  <div ng-if="!transactionValidations.isMachineListEmpty">
                    <div class="row-fluid" cg-busy="loadingMachines">
                      <div class="panel panel-default">
                        <div class="panel-body table-responsive">
                          <p><strong>Máquinas con distintos precios</strong></p>
                          <table class="table">
                            <tr>
                              <th>Sort Index</th>
                              <th>Precio</th>
                            </tr>
                            <tr ng-repeat="machine in machines">
                              <td>{{machine.sort_index}}</td>
                              <td>{{machine.priceMachine}}</td>
                            </tr>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <hr class="m-5" />
              <div class="bs-component" ng-if="errorMessage">
                <div class="alert alert-dismissible alert-danger">
                  <button
                    type="button"
                    class="close"
                    data-dismiss="alert"
                    ng-click="clearError()"
                  >
                    ×
                  </button>
                  <h4>Error</h4>
                  {{errorMessage}}
                </div>
              </div>
              <div>
                <p>
                  <span
                    ><strong>Nuevo Saldo:</strong>
                    <input
                      type="number"
                      step="0.01"
                      class="form-control"
                      ng-model="transaction.cardBalance"
                      style="font-size: 12px; width: 100px; display: inline"
                      placeholder="Saldo"
                      required
                    />
                    <strong>Monto modificado</strong>: {{transaction.cardBalance
                    - selectedCard.balance}}
                  </span>
                </p>
              </div>
              <div>
                <p>
                  <span
                    ><strong>Causa</strong>
                    <select
                      class="form-control"
                      ng-model="transaction.details"
                      ng-options="key as value for (key , value) in details"
                      style="width: 300px; display: inline"
                    ></select>
                  </span>
                </p>
              </div>
              <div>
                <input
                  type="text"
                  class="form-control"
                  ng-if="transaction.details === 'OTHER'"
                  ng-model="transaction.otherDetail"
                  style="font-size: 12px"
                  placeholder="Otra Causa"
                  required
                />
              </div>
              <div>
                <input
                  type="checkbox"
                  class="form-group"
                  ng-model="transaction.isTransactionRequired"
                  ng-change="transaction.isTransactionRequired"
                  id="isTransactionRequired"
                />
                <label for="isTransactionRequired">Efectivo</label>
              </div>
              <div>
                <p>
                  <span
                    ><strong>Comprobante</strong>
                    <select
                      class="form-control"
                      style="width: 180px; display: inline"
                      ng-model="transaction.voucher"
                      ng-change="handleVoucherChange(transaction.voucher)"
                      ng-options="key as value for (key , value) in vouchers"
                    ></select>
                  </span>
                </p>
              </div>
              <div ng-if="transactionValidations.isConsumerFormVisible">
                <p>
                  <span
                    ><strong>Consumidor</strong>
                    <select
                      class="form-control"
                      style="width: 150px; display: inline"
                      ng-model="transaction.consumer"
                      ng-change="handleConsumerChange(transaction.consumer)"
                      ng-options="key as value for (key , value) in consumers"
                      required
                    ></select>
                  </span>
                </p>
              </div>
              <div ng-if="transactionValidations.isBusinessFormVisible">
                <div>
                  <label style="font-weight: bold !important; margin-top: 20px"
                    >Datos del Receptor</label
                  >
                </div>
                <div>
                  <p>
                    <span
                      ><strong>Nombre:</strong>
                      <input
                        type="text"
                        class="form-control"
                        ng-model="transaction.recipientName"
                        style="font-size: 12px; width: 180px; display: inline"
                        required
                        placeholder="Nombre"
                        required
                      />
                    </span>
                  </p>
                </div>
                <div>
                  <p>
                    <span
                      ><strong>Tipo Documento</strong>
                      <select
                        class="form-control"
                        style="width: 100px; display: inline"
                        ng-model="transaction.recipientDocType"
                        ng-options="key as value for (key , value) in recipientDocTypes"
                        required
                      ></select>
                      <input
                        type="text"
                        class="form-control"
                        ng-model="transaction.recipientDoc"
                        style="font-size: 12px; width: 180px; display: inline"
                        required
                        placeholder="Documento"
                        required
                      />
                    </span>
                  </p>
                </div>
              </div>
              <div class="bs-component" ng-if="creditNoteErrorMessage">
                <div class="alert alert-dismissible alert-danger">
                  <button
                    type="button"
                    class="close"
                    data-dismiss="alert"
                    ng-click="clearError()"
                  >
                    ×
                  </button>
                  <h4>Error</h4>
                  {{creditNoteErrorMessage}}
                </div>
              </div>
              <div
                class="col-lg-12"
                ng-if="transactionValidations.isCardWithoutBills"
              >
                <div class="alert alert-dismissible alert-info">
                  <button
                    type="button"
                    class="close"
                    data-dismiss="alert"
                    ng-click="clearError()"
                  >
                    ×
                  </button>
                  {{facturas}}
                </div>
              </div>
              <div ng-if="transactionValidations.isCreditNoteFormVisible">
                <div class="row-fluid">
                  <div class="col-lg-12">
                    <div class="panel panel-default">
                      <div
                        class="panel-body table-responsive"
                        cg-busy="loadingBills"
                      >
                        <p>
                          <strong>SELECCIONE UNA FACTURA DE LA LISTA</strong>
                        </p>
                        <p>
                          La factura seleccionada se asociará a la Nota de
                          Crédito
                        </p>
                        <table class="table">
                          <tr>
                            <th>Fecha</th>
                            <th>Número DGI</th>
                            <th>Monto</th>
                            <th>Seleccionar Factura</th>
                          </tr>
                          <tr
                            ng-repeat="bill in bills"
                            ng-class="{'selected': bill.selected}"
                          >
                            <td>{{bill.timestamp}}</td>
                            <td>{{bill.serie}} - {{bill.number}}</td>
                            <td>{{bill.total | number:2}}</td>
                            <td>
                              <input
                                type="checkbox"
                                class="form-group"
                                ng-model="bill.selected"
                                ng-change="selectBill(bill.id)"
                              />
                            </td>
                          </tr>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
            <div class="modal-footer">
              <input type="submit" class="btn btn-success" value="Modificar" />
              <button type="button" class="btn btn-danger" ng-click="$hide()">
                Cancelar
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
