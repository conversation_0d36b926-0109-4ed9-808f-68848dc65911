<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="form-group">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" ng-click="$hide()">
            &times;
          </button>
          <h4 class="modal-title">Crear nueva factura</h4>
        </div>
        <div
          class="modal-body"
          cg-busy="loadingNewBill && loadingBillItemMeasures"
        >
          <div class="form-group">
            <form>
              <div class="bs-component" ng-if="errorMessage">
                <div class="alert alert-dismissible alert-danger">
                  <button
                    type="button"
                    class="close"
                    data-dismiss="alert"
                    ng-click="clearError()"
                  >
                    ×
                  </button>
                  <h4>Error</h4>
                  {{errorMessage}}
                </div>
              </div>
              <label style="font-weight: bold !important">
                Datos generales
              </label>
              <fieldset title="General">
                <input
                  type="text"
                  class="form-control"
                  style="font-size: 12px"
                  ng-model="newBill.timestamp"
                  data-max-date="{{untilDate}}"
                  placeholder="Fecha de factura"
                  bs-datepicker
                  data-model-date-format="yyyy-MM-dd"
                  data-date-type="string"
                />
                <input
                  type="text"
                  class="form-control"
                  style="font-size: 12px"
                  ng-model="newBill.periodStart"
                  data-max-date="{{untilDate}}"
                  placeholder="Periodo desde"
                  bs-datepicker
                  data-model-date-format="yyyy-MM-dd"
                  data-date-type="string"
                />
                <input
                  type="text"
                  class="form-control"
                  style="font-size: 12px"
                  ng-model="newBill.periodEnd"
                  data-max-date="{{untilDate}}"
                  placeholder="Periodo hasta"
                  bs-datepicker
                  data-model-date-format="yyyy-MM-dd"
                  data-date-type="string"
                />
                <p style="margin-top: 10px">
                  Moneda
                  <select
                    ng-model="newBill.currency"
                    style="width: 100px"
                    ng-options="currencie.key as currencie.text for currencie in currencies"
                  ></select>
                </p>
                <p>
                  Forma de Pago
                  <select
                    ng-model="newBill.paymentMethod"
                    style="width: 100px"
                    ng-options="paymentMethod.key as paymentMethod.text for paymentMethod in paymentMethods"
                  ></select>
                </p>
                <p>
                  Factura Exportación
                  <input type="checkbox" ng-model="newBill.isExportation" />
                </p>
              </fieldset>
              <label style="font-weight: bold !important; margin-top: 20px">
                Datos del Receptor
              </label>
              <p>
                Tipo de receptor
                <select
                  ng-model="newBill.billToType"
                  ng-change="handleBillToTypeChange()"
                  style="width: 100px"
                  ng-options="billToType.key as billToType.text for billToType in billToTypes"
                ></select>
              </p>
              <div ng-show="newBill.billToType === billToType.MISC">
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.name"
                  style="font-size: 12px"
                  placeholder="Nombre"
                />
                Tipo Documento
                <select
                  ng-model="newBill.recipient.docType"
                  style="width: 100px"
                  ng-options="docType.key as docType.text for docType in docTypes"
                ></select>
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.doc"
                  style="font-size: 12px"
                  placeholder="Documento"
                />
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.address"
                  style="font-size: 12px"
                  placeholder="Dirección"
                />
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.city"
                  style="font-size: 12px"
                  placeholder="Ciudad"
                />
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.department"
                  style="font-size: 12px"
                  placeholder="Departamento"
                />
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.country"
                  style="font-size: 12px"
                  placeholder="Pais"
                  value="Ururguay"
                />
                <input
                  type="text"
                  class="form-control"
                  ng-model="newBill.recipient.countryCode"
                  style="font-size: 12px"
                  placeholder="Código del País"
                  value="UY"
                />
              </div>
              <div ng-show="newBill.billToType === billToType.BUILDING">
                <select
                  class="form-control"
                  ng-model="building"
                  style="width: 100%"
                  ng-options="building as building.name for building in data.buildings track by building.id"
                ></select>
              </div>
              <div ng-show="newBill.billToType === billToType.UNIT">
                <div class="row" cg-busy="loadingBuilding">
                  <div class="col-md-6">
                    Edificio
                    <button
                      class="btn btn-xs"
                      ng-click="clearFilter('building')"
                      style="float: right"
                    >
                      <span
                        class="glyphicon glyphicon-remove"
                        title="Limpiar filtro"
                      ></span>
                    </button>
                    <select
                      class="form-control"
                      ng-model="data.building"
                      style="width: 100%"
                      ng-options="building as building.name for building in data.buildings track by building.id"
                      ng-change="handleBuildingChange()"
                    ></select>
                  </div>
                  <div class="col-md-3">
                    Unidad
                    <button
                      class="btn btn-xs"
                      ng-click="clearFilter('unit')"
                      style="float: right"
                    >
                      <span
                        class="glyphicon glyphicon-remove"
                        title="Limpiar filtro"
                      ></span>
                    </button>
                    <select
                      class="form-control"
                      ng-model="unit"
                      ng-disabled="!data.building"
                      style="width: 100%"
                      ng-options="unit as unit.number for unit in data.building.units track by unit.id"
                    ></select>
                  </div>
                </div>
              </div>
              <div>
                <label style="font-weight: bold !important; margin-top: 20px">
                  Detalle de la factura
                </label>
              </div>
              <table class="table">
                <tbody>
                  <tr ng-repeat="item in newBill.items">
                    <td>{{item.amount}}</td>
                    <td>{{item.name}}</td>
                    <td>{{item.measureUnit}}</td>
                    <td>{{item.unitPrice}}</td>
                    <td>{{item.amount * item.unitPrice | number:2}}</td>
                    <td style="text-align: left">
                      <button
                        type="button"
                        class="btn btn-danger btn-xs"
                        ng-click="removeItem(item)"
                      >
                        <span class="fa fa-minus"></span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <label>Agregar Item</label>
              <table class="table">
                <tbody>
                  <tr>
                    <td style="width: 15%; padding: 0px">
                      <input
                        type="number"
                        class="form-control"
                        placeholder="Cantidad"
                        ng-model="newItem.amount"
                        style="font-size: 12px"
                      />
                    </td>
                    <td style="width: 40%; padding: 0px">
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Nombre"
                        ng-model="newItem.name"
                        style="font-size: 12px"
                      />
                    </td>
                    <td style="width: 20%; padding: 0px">
                      <select
                        type="text"
                        class="form-control"
                        ng-model="newItem.measureUnit"
                        style="font-size: 12px"
                        ng-options="data for data in billItemMeasures"
                      ></select>
                    </td>
                    <td style="width: 20%; padding: 0px">
                      <input
                        type="number"
                        class="form-control"
                        placeholder="Precio Unitario"
                        ng-model="newItem.unitPrice"
                        style="font-size: 12px"
                      />
                    </td>
                    <td style="width: 5%; padding: 0px; text-align: left">
                      <button
                        type="button"
                        class="btn btn-success btn-xs"
                        ng-click="addNewItem()"
                      >
                        <span class="fa fa-plus"></span>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              <div class="text-right" style="border-top: 3px solid #aaa">
                <p>Subtotal $ {{getSubtotal() | number:2}}</p>
                <p>IVA $ {{getIva() | number:2}}</p>
                <p><strong>Total $ {{getTotal() | number:2}}</strong></p>
              </div>
              <div class="modal-footer">
                <button
                  type="submit"
                  class="btn btn-success"
                  ng-click="createBill()"
                >
                  Generar
                </button>
                <button type="button" class="btn btn-danger" ng-click="close()">
                  Cancelar
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
