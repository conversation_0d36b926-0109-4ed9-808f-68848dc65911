<div class="panel-heading">
  <button
    class="btn btn-xs btn-warning pull-right"
    style="margin-right: 10px"
    ng-click="loadMachines(true)"
  >
    Recargar</button
  >&nbsp;
  <button
    class="btn btn-xs btn-info pull-right"
    style="margin-right: 10px"
    ng-click="showAssignMachine()"
  >
    A<PERSON><PERSON><PERSON>
  </button>
</div>
<div class="panel-body">
  <table class="table table-full table-hover">
    <thead>
      <tr>
        <th class="text-center" ng-click="orderMachineColumn('id')">
          ID
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'id'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('sort_index')">
          Sort Index
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'sort_index'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('reference')">
          Referencia
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'reference'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('serial_number')">
          Número de Serie
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'serial_number'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('machine_type')">
          Tipo
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'machine_type'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th
          class="text-center"
          ng-click="orderMachineColumn('last_keep_alive')"
        >
          Keep Alive
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'last_keep_alive'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('pending_uses')">
          Usos pendientes
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'pending_uses'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center" ng-click="orderMachineColumn('last_use')">
          Último Uso
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'last_use'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center">Usos post prox. cierre</th>
        <th class="text-center">Keep alive post cierre</th>
        <th
          class="text-center"
          ng-click="orderMachineColumn('rpi_child.serial_number')"
        >
          RPI Hijo
          <span
            class="sortorder"
            ng-show="filters.machinesOrderBy === 'rpi_child.serial_number'"
            ng-class="{reverse: filters.machineReverse}"
          />
        </th>
        <th class="text-center">QR</th>
        <th class="text-center">Acción</th>
      </tr>
    </thead>
    <tbody>
      <tr
        class="text-center"
        ng-class="{'tr-warning': machine.inMaintenance}"
        ng-repeat="machine in machines | orderBy: filters.machinesOrderBy: filters.machineReverse"
      >
        <td class="text-center">{{ machine.id }}</td>
        <td class="text-center">{{ machine.sort_index }}</td>
        <td class="text-center">{{ machine.reference }}</td>
        <td class="text-center">{{ machine.serial_number }}</td>
        <td class="text-center">
          {{ machine.machine_type == 'WASHER' ? "Lavadora" : "Secadora" }}
        </td>
        <td class="text-center">
          <div>{{ machine.last_keep_alive | date:'yyyy/MM/dd' }}</div>
          <div>{{ machine.last_keep_alive | date:'HH:mm:ss'}}</div>
        </td>
        <td class="text-center">{{ machine.pending_uses }}</td>
        <td class="text-center">
          <div>{{ machine.last_use | date:'yyyy/MM/dd' }}</div>
          <div>{{ machine.last_use | date:'HH:mm'}}</div>
          Apto: {{ machine.last_unit_use }}
        </td>
        <td class="text-center">
          <i
            class="md md-check-box bold-green"
            ng-if="machine.uses_after_closure_date"
          ></i>
          <i
            class="md md-cancel bold-red"
            ng-if="!machine.uses_after_closure_date"
          ></i>
        </td>
        <td class="text-center">
          <i
            class="md md-check-box bold-green"
            ng-if="machine.keep_alive_after_closure_date"
          ></i>
          <i
            class="md md-cancel bold-red"
            ng-if="!machine.keep_alive_after_closure_date"
          ></i>
        </td>
        <td class="text-center">
          <span ng-if="!!machine.rpi_child">
            {{machine.rpi_child.serial_number}}
          </span>
        </td>
        <td class="text-center">
          <span ng-if="!!machine.qr"> {{machine.qr.token}} </span>
        </td>
        <td class="text-left">
          <button
            class="btn btn-xs btn-danger"
            ng-click="unassignMachineFromBuilding(machine)"
          >
            Desasignar
          </button>
          <button
            class="btn btn-xs btn-success"
            ng-if="!machine.rpi_child && !isRPIChild(machine)"
            ng-click="assignRPIChild(machine)"
          >
            Asignar RPI Hijo
          </button>
          <button
            class="btn btn-xs btn-warning"
            ng-if="!!machine.rpi_child"
            ng-click="unassignRPIChild(machine)"
          >
            Desasignar RPI
          </button>
          <button
            class="btn btn-xs btn-primary"
            ng-if="machine.pending_uses"
            ng-click="zeroingPendingUses(machine)"
          >
            0 Usos Pendientes
          </button>
          <button
            class="btn btn-xs purple darken-3"
            ng-if="!machine.qr"
            ng-click="assignQr(machine)"
          >
            Asignar QR
          </button>
          <button
            class="btn btn-xs purple darken-3"
            ng-if="!!machine.qr"
            ng-click="unassignQr(machine)"
          >
            Desasignar QR
          </button>
          <button
            class="btn btn-xs indigo"
            ng-click="showMachineHistory(machine)"
          >
            Historial
          </button>
          <button
            ng-if="!machine.inMaintenance"
            class="btn btn-xs green"
            ng-click="setMachineIntoMaintenance(machine)"
          >
            Iniciar Mantenimiento
          </button>
          <button
            ng-if="machine.inMaintenance"
            class="btn btn-xs red"
            ng-click="interruptMachineMaintenance(machine)"
          >
            Interrumpir Mantenimiento
          </button>
        </td>
      </tr>
    </tbody>
  </table>

  <toast message="toast"></toast>
</div>
