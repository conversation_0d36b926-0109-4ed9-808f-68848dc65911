<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Esta seguro de cancelar esta facturación?</h4>
        <small>Seleccione la modalidad para la cancelación</small>
      </div>
      <div class="modal-body">
        <strong>Motivo (opcional): </strong> <br />
        <input
          type="text"
          class="form-control"
          style="font-size: 12px"
          ng-model="reason"
        />
        <br />
        <table class="table">
          <thead>
            <tr>
              <th>Descripcion</th>
              <th>Cantidad</th>
              <th>Cantidad a desacreditar</th>
              <th>Unidad</th>
              <th class="text-right">Precio unitario</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="l in lines">
              <td>{{l.itemName}}</td>
              <td>{{l.amount}}</td>
              <td>
                <input
                  type="number"
                  class="form-control"
                  style="font-size: 12px; height: auto"
                  ng-model="l.discreditedAmount"
                  min="0"
                  max="{{l.amount}}"
                  ng-disabled="{{l.unit == 'Uso'}}"
                  ng-change="calculateDiscreditedAmount()"
                />
              </td>
              <td>{{l.unit}}</td>
              <td align="right">$ {{l.unitPrice | number:2}}</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4" align="right">
                Anulación parcial $ {{selectedBill.discreditedAmount |
                number:2}}
              </td>
            </tr>
            <tr>
              <td colspan="4" align="right">
                Anulación total $ {{selectedBill.total | number:2}}
              </td>
            </tr>
          </tfoot>
        </table>

        <div class="row-fluid" ng-show="discrediteResults.length > 0">
          <div class="col-lg-12">
            <div class="panel panel-default">
              <div class="panel-body">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Maquina</th>
                      <th>Tipo</th>
                      <th>Tarjeta / unidad</th>
                      <th>Fecha</th>
                      <th>Razón</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="u in discrediteResults">
                      <td>{{u.machine.id}}</td>
                      <td>
                        {{u.machine.machine_type == 'WASHER' ? 'Lavado' :
                        'Secado'}}
                      </td>
                      <td>
                        {{u.card ? (u.card.uuid + ' / ' + u.card.unit_tower +
                        '-' + u.card.unit_number):''}}
                      </td>
                      <td>{{u.timestamp | date}}</td>
                      <td>{{u.reason}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-warning"
          ng-click="submit('partial')"
          ng-disabled="selectedBill.discreditedAmount < 1 || !isNumberDiscreditedAmount || selectedBill.isCreditNote"
        >
          Parcial
        </button>
        <button type="button" class="btn btn-danger" ng-click="submit('total')">
          Total
        </button>
      </div>
    </div>
  </div>
</div>
