<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">
          Histórico <strong>{{selectedRate.name}}</strong>
        </h4>
      </div>
      <div class="modal-body" cg-busy="loadHistory">
        <table class="table">
          <thead>
            <tr>
              <th>Mínimo de Usos Totales</th>
              <th><PERSON><PERSON>imo de Usos Por Unidad</th>
              <th>Precio Cliente</th>
              <th>Precio Empresa</th>
              <th>Precio m3</th>
              <th>Precio KWh</th>
              <th>Precio Tarjeta</th>
              <th>Periodo</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="rate in events">
              <td>{{rate.minUsesPerWasher}}</td>
              <td>{{rate.minUsesPerUnit}}</td>
              <td>{{rate.priceCustomer}}</td>
              <td>{{rate.priceCompany}}</td>
              <td>{{rate.priceM3}}</td>
              <td>{{rate.priceKWh}}</td>
              <td>{{rate.priceCardReplacement}}</td>
              <td>{{rate.validFromPretty}} / {{rate.validUntilPretty}}</td>
              <td>
                <button
                  class="btn btn-xs btn-warning"
                  ng-click="editRateHistory(rate)"
                >
                  Editar
                </button>
                <button
                  class="btn btn-xs btn-danger"
                  ng-click="deleteRateHistory(rate)"
                >
                  Eliminar
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close()">
          Cerrar
        </button>
      </div>
    </div>
  </div>
</div>
