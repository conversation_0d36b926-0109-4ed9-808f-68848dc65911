<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Factura: Actualizar estado de cobranza</h4>
        <small>Seleccione un estado de cobranza</small>
      </div>
      <div class="modal-body">
        <strong>Estado anterior: </strong> <br />
        <p>{{previousSelectedStatus}}</p>

        <strong>Estado: </strong> <br />

        <select
          id="status"
          ng-model="selectedStatus"
          required
          data-error="Seleccione un estado!"
          tabindex="1"
        >
          <option value="" selected>Seleccione un estado</option>
          <option value="{{key}}" ng-repeat="key in collectionStatusKeys">
            {{collectionStatus[key]}}
          </option>
        </select>
        <br />
        <br />

        <strong>Referencia: </strong> <br />

        <input type="text" ng-model="paymentReference" />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close()">
          Cancelar
        </button>
        <button
          type="button"
          class="btn btn-primary"
          ng-click="submit(selectedStatus, paymentReference)"
        >
          Actualizar
        </button>
      </div>
    </div>
  </div>
</div>
