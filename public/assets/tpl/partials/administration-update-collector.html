<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Factura: Actualizar cobrador</h4>
        <small>Seleccione un cobrador a asignar</small>
      </div>
      <div class="modal-body">
        <strong>Cobrador: </strong> <br />

        <select
          id="debtCollectors"
          ng-model="collector"
          required
          data-error="Seleccione un cobrador!"
          tabindex="1"
          ng-options="user as user.name + ' ' + user.lastname for user in collectors track by user.id"
        >
          <option value="" selected>Seleccione un cobrador</option>
        </select>
        <br />

        <strong>Fecha de Cobro: </strong> <br />

        <input
          type="text"
          class="form-control"
          ng-model="collectionDate"
          placeholder="Fecha de cobro"
          bs-datepicker
        />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close()">
          Cancelar
        </button>
        <button type="button" class="btn btn-warning" ng-click="submit()">
          Desasignar
        </button>
        <button
          type="button"
          class="btn btn-primary"
          ng-click="submit(collector, collectionDate)"
        >
          Asignar
        </button>
      </div>
    </div>
  </div>
</div>
