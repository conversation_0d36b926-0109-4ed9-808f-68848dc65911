<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">Test modal</h4>
      </div>
      <div class="modal-body" cg-busy="loadMachines">
        <h4>Text in a modal</h4>
        <p ng-bind-html="content"></p>
        <pre>2 + 3 = {{ 2 + 3 }}</pre>

        <h4>Popover in a modal</h4>
        <p>
          This
          <button
            role="button"
            class="btn btn-default popover-test"
            data-title="A Title"
            data-content="And here's some amazing content. It's very engaging. right?"
            bs-popover
          >
            button
          </button>
          should trigger a popover on click.
        </p>

        <h4>Tooltips in a modal</h4>
        <p>
          <a href="" class="tooltip-test" data-title="Tooltip" bs-tooltip
            >This link</a
          >
          and
          <a href="" class="tooltip-test" data-title="Tooltip" bs-tooltip
            >that link</a
          >
          should have tooltips on hover.
        </p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="$hide()">
          Close
        </button>
        <button type="button" class="btn btn-primary" ng-click="$hide()">
          Save changes
        </button>
      </div>
    </div>
  </div>
</div>
