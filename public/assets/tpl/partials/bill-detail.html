<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" ng-click="$hide()">&times;</button>
        <h4 class="modal-title">
          Detalle de factura {{selectedBill.id}} - ({{selectedBill.serie}} -
          {{selectedBill.number}})
        </h4>
      </div>
      <div class="modal-body" cg-busy="loadingDetails">
        <strong>Facturado a: </strong> {{selectedBill.billTo}} <br />
        <strong>Cobrador: </strong> {{selectedBill.collector}} <br />
        <strong>Fe<PERSON> de cobro: </strong> {{selectedBill.collectionDate}} <br />
        <strong>Referencia de pago: </strong> {{selectedBill.paymentReference}}
        <br />
        <strong>Estado de cobranza: </strong>
        {{collectionStatus[selectedBill.billCollectionStatus]}} <br />
        <strong>Período: </strong> {{selectedBill.billedPeriodStart |
        date:'yyyy-MM-dd'}} / {{selectedBill.billedPeriodEnd |
        date:'yyyy-MM-dd'}}
        <table class="table">
          <thead>
            <tr>
              <th>Descripcion</th>
              <th>Cantidad</th>
              <th>Unidad</th>
              <th class="text-right">Precio unitario</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="l in lines">
              <td>{{l.itemName}}</td>
              <td>{{l.amount}}</td>
              <td>{{l.unit}}</td>
              <td align="right">$ {{l.unitPrice | number:2}}</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="4" align="right">Total {{lines.length}} items</td>
            </tr>
            <tr>
              <td colspan="4" align="right">
                $ {{selectedBill.total | number:2}}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" ng-click="close()">
          Cancelar
        </button>
      </div>
    </div>
  </div>
</div>
