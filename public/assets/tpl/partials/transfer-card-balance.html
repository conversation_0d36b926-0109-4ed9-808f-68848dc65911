<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog">
    <div class="form-group">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" ng-click="$hide()">
            &times;
          </button>
          <h4 class="modal-title">Transferir Saldo</h4>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <h4 class="p-15 grey-text">
              <button
                class="btn btn-xs btn-success pull-right"
                style="margin-right: 10px"
                ng-click="validateCardReceiver()"
              >
                Validar
              </button>
              <input
                type="text"
                class="form-control input-sm pull-right"
                style="margin-right: 10px; max-width: 35%"
                ng-model="transferBalanceFilters.cardSearch"
                placeholder="UID Receptora"
              />
            </h4>
          </div>
        </div>
        <div class="modal-body" cg-busy="loadingNewBalance || loadingCards">
          <div>
            <table class="table table-full">
              <thead>
                <tr>
                  <th style="width: 33%">Tarjeta Emisora</th>
                  <th style="width: 33%" rowspan="3" class="text-center">
                    <i class="md md-arrow-forward"></i>
                  </th>
                  <th style="width: 33%">Tarjeta Receptora</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <p><strong>UID Tarjeta</strong>: {{selectedCard.uuid}}</p>
                  </td>
                  <td></td>
                  <td>
                    <div ng-if="cardReceiver">
                      <p><strong>UID Tarjeta</strong>: {{cardReceiver.uuid}}</p>
                    </div>
                    <div ng-if="!cardReceiver">{{cardMessage}}</div>
                  </td>
                </tr>
                <tr>
                  <td>
                    <p>
                      <strong>Saldo Actual</strong>: {{selectedCard.balance}}
                    </p>
                  </td>
                  <td></td>
                  <td>
                    <div ng-if="cardReceiver">
                      <strong>Saldo Actual</strong>: {{cardReceiver.balance}}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div>
            <p>
              <strong>Unidad/Edificio</strong>:
              {{selectedCard.unit_number}}/{{selectedCard.unit_tower}} -
              {{selectedCard.building}}
            </p>
          </div>
          <div>
            <p>
              <strong>Precio Compañía / Precio Cliente</strong>:
              ${{selectedCard.priceCompany}} / ${{selectedCard.priceCustomer}}
            </p>
          </div>
          <div class="row">
            <div class="col-md-6 text-left">
              <input
                type="checkbox"
                class="form-group"
                ng-model="transaction.setCardToLost"
                id="setCardToLost"
              />
              <label for="setCardToLost">BLOQUEO POR EXTRAVÍO</label>
            </div>
          </div>
          <hr class="m-5" />
          <form>
            <fieldset>
              <div class="bs-component" ng-if="errorMessage">
                <div class="alert alert-dismissible alert-danger">
                  <button
                    type="button"
                    class="close"
                    data-dismiss="alert"
                    ng-click="clearError()"
                  >
                    ×
                  </button>
                  <h4>Error</h4>
                  {{errorMessage}}
                </div>
              </div>
              <div>
                <p>
                  <span
                    ><strong>Saldo a Transferir:</strong>
                    <input
                      type="number"
                      step="0.01"
                      class="form-control"
                      ng-model="transaction.balanceToTransfer"
                      style="font-size: 12px; width: 100px; display: inline"
                      placeholder="Saldo"
                      required
                    />
                    <strong>Saldo Restante</strong>: {{selectedCard.balance -
                    transaction.balanceToTransfer}}
                  </span>
                </p>
              </div>
              <div>
                <p>
                  <span
                    ><strong>Causa</strong>
                    <select
                      class="form-control"
                      ng-model="transaction.details"
                      ng-options="key as value for (key , value) in transferBalanceDetails"
                      style="width: 300px; display: inline"
                    ></select>
                  </span>
                </p>
              </div>
              <div>
                <input
                  type="text"
                  class="form-control"
                  ng-if="transaction.details === details.OTHER"
                  ng-model="transaction.otherDetail"
                  style="font-size: 12px"
                  placeholder="Otra Causa"
                  required
                />
              </div>
            </fieldset>
            <div class="modal-footer">
              <input
                type="submit"
                class="btn btn-success"
                ng-click="transferBalance(card)"
                value="Transferir"
              />
              <button type="button" class="btn btn-danger" ng-click="$hide()">
                Cancelar
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
