<div class="dashboard lighten-3" ng-controller="PartsController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Administración de Inventario</h4>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div
          bs-tabs
          ng-model="activeTab"
          ng-change="handleTabChange(activeTab)"
        >
          <div
            bs-pane
            title="Repuestos"
            ng-if="!account.anyRole(roles.TECHNICIAN)"
          >
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createPart()"
                  >
                    Crear Respuesto
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshParts()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.partQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingParts">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th ng-click="orderColumn('id')">
                        Id
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'id'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('name')">
                        Nombre
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'name'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('serial_number')">
                        Número de Serie
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'serial_number'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('quantity')">
                        Cantidad
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'quantity'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="p in data.parts | filter:filters.partQuickSearch |
                                            orderBy: filters.orderBy: filters.reverse"
                    >
                      <td>{{p.id}}</td>
                      <td>{{p.name}}</td>
                      <td>{{p.serial_number}}</td>
                      <td>{{p.quantity}}</td>
                      <td>
                        <button
                          class="btn btn-xs btn-info"
                          ng-click="viewPart(p)"
                        >
                          Detalles
                        </button>
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editPart(p)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deletePart(p)"
                        >
                          Borrar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            bs-pane
            title="Máquinas"
            ng-if="!account.anyRole(roles.TECHNICIAN)"
          >
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createMachine()"
                  >
                    Crear Máquina
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshMachines()"
                  >
                    Buscar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.machineQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingMachines">
              <div class="col-lg-14">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>Nombre</th>
                      <th>Numero de Serie</th>
                      <th>Modelo</th>
                      <th>Versión de Firmware</th>
                      <th>Usos Esperados</th>
                      <th>Usos Actuales</th>
                      <th>Precio Unitario</th>
                      <th>Precio de Venta UY</th>
                      <th>Tipo de Máquina</th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="m in data.machines">
                      <td>{{m.id}}</td>
                      <td>{{m.name}}</td>
                      <td>{{m.serial_number}}</td>
                      <td>
                        {{m.machine_model != null ? m.machine_model.name : ''}}
                      </td>
                      <td>{{m.firmware_version}}</td>
                      <td>{{m.expected_uses}}</td>
                      <td>{{m.current_uses}}</td>
                      <td>{{m.unit_price}}</td>
                      <td>{{m.ui_price}}</td>
                      <td>
                        {{m.machine_type == 'WASHER' ? 'Lavadora' : 'Secadora'}}
                      </td>
                      <td>
                        <button
                          class="btn btn-xs btn-info"
                          ng-click="viewMachine(m)"
                        >
                          Detalles
                        </button>
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editMachine(m)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deletePart(m)"
                        >
                          Borrar
                        </button>
                        <button
                          class="btn btn-xs indigo"
                          ng-click="showMachineHistory(m)"
                        >
                          Historial
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            class="row-fluid"
            bs-pane
            title="Tarjetas"
            cg-busy="loadingBuildingFilters"
            ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT, roles.TECHNICIAN, roles.SUPERVISOR)"
          >
            <div class="panel-body">
              <div class="col-lg-12">
                <div class="form-group">
                  <div class="input-group">
                    <span class="input-group-addon" id="basic-addon1"></span>
                    <div class="row" cg-busy="loadingBuilding">
                      <div class="col-md-3">
                        Edificio
                        <button
                          class="btn btn-xs"
                          ng-click="clearFilter('building')"
                          style="float: right"
                        >
                          <span
                            class="glyphicon glyphicon-remove"
                            title="Limpiar filtro"
                          ></span>
                        </button>
                        <select
                          class="form-control"
                          ng-model="filters.building"
                          style="width: 100%"
                          ng-options="building as building.name for building in data.buildings track by building.id"
                          ng-change="handleBuildingChange()"
                        ></select>
                      </div>
                      <div class="col-md-3">
                        Unidad
                        <button
                          class="btn btn-xs"
                          ng-click="clearFilter('unit')"
                          style="float: right"
                        >
                          <span
                            class="glyphicon glyphicon-remove"
                            title="Limpiar filtro"
                          ></span>
                        </button>
                        <select
                          class="form-control"
                          ng-model="filters.unit"
                          ng-disabled="!filters.building"
                          style="width: 100%"
                          ng-options="unit as unit.name for unit in filters.units | orderBy:'name' track by unit.id"
                        >
                          <option value="" selected>Seleccionar todas</option>
                        </select>
                      </div>
                      <div class="col-md-3">
                        UID
                        <input
                          class="form-control"
                          ng-model="filters.cardSearch"
                          style="width: 100%"
                          type="text"
                        />
                      </div>
                      <div class="panel-footer text-right">
                        <button
                          class="btn btn-xs btn-info pull-right"
                          ng-click="createCard()"
                        >
                          Crear Tarjeta
                        </button>
                        <button
                          class="btn btn-xs btn-warning pull-right"
                          style="margin-right: 10px"
                          ng-click="refreshCards()"
                        >
                          Buscar
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingCards">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>UID</th>
                      <th>Unidad / Edificio</th>
                      <th>Maestra</th>
                      <th>Estado</th>
                      <th>Tipo de Contrato</th>
                      <th>Saldo</th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr ng-repeat="card in data.cards">
                      <td>{{card.id}}</td>
                      <td>{{card.uuid}}</td>
                      <td>
                        {{card.unit_number}}/{{card.unit_tower}} -
                        {{card.building}}
                      </td>
                      <td>{{card.master ? 'SI' : 'NO'}}</td>
                      <td>{{card.state}}</td>
                      <td>{{card.contract_type}}</td>
                      <td>{{card.balance}}</td>
                      <td style="overflow: visible">
                        <div class="btn-group">
                          <button
                            style="border-radius: 15px"
                            class="btn btn-round-sm btn-link dropdown-toggle pointer withoutripple"
                            data-toggle="dropdown"
                          >
                            <i class="md md-more-vert f20"></i>
                          </button>
                          <ul class="dropdown-menu pull-right">
                            <li>
                              <a ng-click="editCard(card)">Editar</a>
                            </li>
                            <li class="divider"></li>
                            <li>
                              <a ng-click="deletePart(card)">Borrar</a>
                            </li>
                            <li class="divider"></li>
                            <li>
                              <a ng-click="showUpdateCardBalanceForm(card)"
                                >Modificar Saldo</a
                              >
                            </li>
                            <li class="divider"></li>
                            <li>
                              <a ng-click="showTransferCardBalanceForm(card)"
                                >Transferencia de Saldo</a
                              >
                            </li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            bs-pane
            title="Herramientas"
            ng-if="!account.anyRole(roles.TECHNICIAN)"
          >
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createTool()"
                  >
                    Crear Herramienta
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshTools()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.toolQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingTools">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>Nombre</th>
                      <th>Numero de Serie</th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="t in data.tools | filter:filters.toolQuickSearch"
                    >
                      <td>{{t.id}}</td>
                      <td>{{t.name}}</td>
                      <td>{{t.serial_number}}</td>
                      <td>
                        <button
                          class="btn btn-xs btn-info"
                          ng-click="viewTool(t)"
                        >
                          Detalles
                        </button>
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editTool(t)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deletePart(t)"
                        >
                          Borrar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            bs-pane
            title="Dosificadores de Jabón"
            ng-if="!account.anyRole(roles.TECHNICIAN)"
          >
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createSoapdisp()"
                  >
                    Crear Dosificador
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshDispensers()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.partQuickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingDispensers">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th ng-click="orderColumn('id')">
                        Id
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'id'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('model')">
                        N°Bidon-N°Máquina
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'model'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('building')">
                        Edificio
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'building'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th>Sort Index/Máquina</th>
                      <th ng-click="orderColumn('uses')">
                        Usos
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'uses'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('creationDate')">
                        Fecha de Creación
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'creationDate'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th ng-click="orderColumn('replenishDate')">
                        Fecha de Reposición
                        <span
                          class="sortorder"
                          ng-show="filters.orderBy === 'replenishDate'"
                          ng-class="{reverse: filters.reverse}"
                        />
                      </th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="dispenser in data.dispensers | filter:filters.partQuickSearch | orderBy: filters.orderBy: filters.reverse"
                    >
                      <td>{{dispenser.id}}</td>
                      <td>{{dispenser.model}}</td>
                      <td>{{dispenser.building}}</td>
                      <td>
                        {{ dispenser.sortIndex }} - {{dispenser.machineSerial}}
                      </td>
                      <td>{{dispenser.uses}}</td>
                      <td>{{dispenser.creationDate}}</td>
                      <td>{{dispenser.replenishDate}}</td>
                      <td>
                        <button
                          class="btn btn-xs btn-success"
                          ng-click="showReplenishSoapDispenserForm(dispenser.id)"
                        >
                          Reponer
                        </button>
                        <button
                          class="btn btn-xs btn-info"
                          ng-click="viewSoapdisp(dispenser)"
                        >
                          Detalles
                        </button>
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editSoapdisp(dispenser)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deleteDispenser(dispenser)"
                        >
                          Borrar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            bs-pane
            title="Modelos"
            ng-if="!account.anyRole(roles.TECHNICIAN)"
          >
            <div class="row">
              <div class="col-lg-12">
                <h4 class="p-15 grey-text">
                  Modelos de máquinas
                  <button
                    class="btn btn-xs btn-info pull-right"
                    ng-click="createMachineModel()"
                  >
                    Crear Nuevo
                  </button>
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    style="margin-right: 10px"
                    ng-click="refreshMachineModels()"
                  >
                    Recargar
                  </button>
                  <input
                    type="text"
                    class="form-control input-sm pull-right"
                    style="margin-right: 10px; max-width: 25%"
                    ng-model="filters.quickSearch"
                    placeholder="Búsqueda Rápida"
                  />
                </h4>
              </div>
            </div>
            <div style="height: 20px"></div>
            <div class="row" cg-busy="loadingMachineModels">
              <div class="col-lg-12">
                <table class="table table-full table-hover">
                  <thead>
                    <tr>
                      <th>Id</th>
                      <th>Nombre</th>
                      <th>MP100</th>
                      <th>MP1300</th>
                      <th>MP1200</th>
                      <th>Acción</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      ng-repeat="model in data.machineModels | filter:filters.quickSearch"
                    >
                      <td>{{model.id}}</td>
                      <td>{{model.name}}</td>
                      <td>{{model.mp100}}</td>
                      <td>{{model.mp500}}</td>
                      <td>{{model.mp1200}}</td>
                      <td style="overflow: visible">
                        <button
                          class="btn btn-xs btn-warning"
                          ng-click="editMachineModel(model)"
                        >
                          Editar
                        </button>
                        <button
                          class="btn btn-xs btn-danger"
                          ng-click="deleteMachineModel(model)"
                        >
                          Borrar
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- ========================================= PART =================================================== -->

<script type="text/ng-template" id="/part-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingPart">

              <form class="form-floating" novalidate="novalidate" ng-submit="savePart(item, 'REPLACEMENT')">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.settings.cmd == 'Edit' ? 'Editar' : 'Crear
                          nuevo
                          '}} Respuesto
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing"
                                 required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Número de Serie</label>
                          <input type="text" class="form-control" ng-model="item.serial_number"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descripción (Español)</label>
                          <textarea class="form-control" ng-model="item.description"
                                    ng-disabled="!item.editing"></textarea>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descripción (Inglés)</label>
                          <textarea class="form-control" ng-model="item.english_description"
                                    ng-disabled="!item.editing"></textarea>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio Unitario</label>
                          <input type="text" class="form-control" ng-model="item.unit_price"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio de venta en Uruguay</label>
                          <input type="text" class="form-control" ng-model="item.uy_price"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Stock mínimo</label>
                          <input type="text" class="form-control" ng-model="item.minimum_stock"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Consumo Anual</label>
                          <input type="text" class="form-control" ng-model="item.anual_consumption"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Punto de Pedido</label>
                          <input type="text" class="form-control" ng-model="item.request_point"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Cantidad</label>
                          <input type="text" class="form-control" ng-model="item.quantity"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<!-- ========================================= SOAP DISPENSER =================================================== -->

<script type="text/ng-template" id="/soapdisp-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>
      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingSoapdispUpd">
              <form class="form-floating" novalidate="novalidate" ng-submit="saveSoapdisp(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nuevo' }}
                          Dosificador de Jabón
                      </legend>
                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">N°Bidon-N°Máquina</label>
                          <input type="text" class="form-control" ng-model="item.model" ng-disabled="!item.editing"
                                 required>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Número de serie de la máquina asignada</label>
                          <input type="text" class="form-control" ng-model="item.machineSerial"
                                 ng-disabled="!item.editing">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Descripcion</label>
                          <input type="text" class="form-control" ng-model="item.description"
                                 ng-disabled="!item.editing" required>
                      </div>
                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>
                  </fieldset>
              </form>
          </div>
      </div>
  </div>
</script>

<!-- ========================================= MACHINE =================================================== -->

<script type="text/ng-template" id="/machine-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingPart">

              <form class="form-floating" novalidate="novalidate" ng-submit="savePart(item, 'MACHINE')">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nuevo'}}
                          Máquina
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing"
                                 required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Número de Serie</label>
                          <input type="text" class="form-control" ng-model="item.serial_number"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Modelo</label>
                          <select class="form-control" ng-model="item.machine_model_id" ng-disabled="!item.editing"
                                  ng-options="mm.id as mm.name for mm in data.machineModels"></select>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Referencia</label>
                          <input type="text" class="form-control" ng-model="item.reference"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Usos esperados</label>
                          <input type="number" class="form-control" ng-model="item.expected_uses"
                                 ng-disabled="!item.editing"/>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descripción (Español)</label>
                          <textarea class="form-control" ng-model="item.description"
                                    ng-disabled="!item.editing"></textarea>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descripción (Inglés)</label>
                          <textarea class="form-control" ng-model="item.english_description"
                                    ng-disabled="!item.editing"></textarea>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio Unitario</label>
                          <input type="text" class="form-control" ng-model="item.unit_price"
                                 ng-disabled="!item.editing"/>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Precio de venta en Uruguay</label>
                          <input type="text" class="form-control" ng-model="item.uy_price"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Tipo de Máquina</label>
                          <select class="form-control" ng-model="item.machine_type" ng-disabled="!item.editing">
                              <option value='WASHER'>Lavadora</option>
                              <option value='DRYER'>Secadora</option>
                          </select>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Tiempo promedio de uso (minutos)</label>
                          <input type="number" class="form-control" ng-model="item.average_use_time"
                                 ng-disabled="!item.editing" required disable-arrows>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Capacidad (Kilogramos)</label>
                          <input type="number" class="form-control" ng-model="item.capacity"
                                 ng-disabled="!item.editing" required disable-arrows>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Sort Index</label>
                          <input type="number" class="form-control" ng-model="item.sortIndex"
                                 ng-disabled="!item.editing" required min="0" disable-arrows>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Ratio</label>
                          <select class="form-control" ng-model="item.specialRateId" ng-disabled="!item.editing"
                                  ng-options="rate.id as rate.name for rate in data.rates">
                              <option value="">--** SIN RATIO **--</option>
                          </select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Edificio</label>
                          <select
                              class="form-control"
                              ng-model="item.buildingId"
                              style="width: 100%"
                              ng-options="building.id as building.name for building in data.buildings"
                          >
                              <option value="">--** SIN EDIFICIO **--</option>
                          </select>
                      </div>
                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<!-- ========================================= CARD =================================================== -->

<script type="text/ng-template" id="/card-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingPart">

              <form class="form-floating" novalidate="novalidate" ng-submit="savePart(item, 'CARD')" name="partForm">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{settings.cmd == 'Edit' ? 'Editar' : 'Crear
                          nuevo
                          '}} Tarjeta
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">UID</label>
                          <input name="uid" type="text" class="form-control" ng-model="item.uuid"
                                 ng-disabled="!item.editing" required
                                 ng-pattern="/^([a-zA-Z0-9]{8}|[a-zA-Z0-9]{10})$/"
                                 ng-pattern-err-type="eightOrTenCharacters">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Es Tarjeta Maestra</label>
                          <select ng-model="item.master" style="width:30%">
                              <option value="true" ng-selected="item.master">SI</option>
                              <option value="false" ng-selected="!item.master">NO</option>
                          </select>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Tipo</label>
                          <select ng-model="item.contract_type" style="width:30%" required>
                              <option value="POSTPAID">POSTPAID</option>
                              <option value="PREPAID">PREPAID</option>
                          </select>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Horario de Inicio de Uso</label>
                          <div class="form-group filled">
                              <input type="time" placeholder="HH:mm:ss" ng-model="item.start_time"
                                     class="form-control" mdbInput>
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Horario de Fin de Uso</label>
                          <div class="form-group filled">
                              <input type="time" placeholder="HH:mm:ss" ng-model="item.end_time" class="form-control"
                                     mdbInput>
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descuento</label>
                          <input type="number" step="0.00" min="0.00" max="0.99" class="form-control input-md"
                                 ng-model="item.discount" ng-disabled="!item.editing">
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<!-- ========================================= TOOL =================================================== -->

<script type="text/ng-template" id="/tool-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingPart">
              <form class="form-floating" novalidate="novalidate" ng-submit="savePart(item, 'TOOL')">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' : 'Crear nuevo'}}
                          Herramienta
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing"
                                 required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Número de Serie</label>
                          <input type="text" class="form-control" ng-model="item.serial_number"
                                 ng-disabled="!item.editing">
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Descripción</label>
                          <textarea class="form-control" ng-model="item.description"
                                    ng-disabled="!item.editing"></textarea>
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>
                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>

<!-- ========================================= MACHINE MODEL =================================================== -->

<script type="text/ng-template" id="/machine-model-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingMachineModel">

              <form class="form-floating" novalidate="novalidate" ng-submit="saveMachineModel(item)">
                  <fieldset>
                      <legend>
                          <span ng-bind-html="item.icon"></span>Crear nuevo Modelo de Máquina
                      </legend>

                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×
                              </button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" min="0" ng-model="item.name"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP100</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp100"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP500</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp500"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">MP1200</label>
                          <input type="number" class="form-control" min="0" ng-model="item.mp1200"
                                 ng-disabled="!item.editing" required>
                      </div>

                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
                      </div>

                  </fieldset>
              </form>

          </div>
      </div>
  </div>
</script>
