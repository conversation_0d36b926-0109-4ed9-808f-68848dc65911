<div
  class="dashboard lighten-3"
  ng-controller="ReportsController"
  ng-init="loadWithUser()"
>
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Consulta de Uso de Máquinas</h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>

              <div class="row">
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.from"
                    data-max-date="{{untilDate}}"
                    placeholder="Desde"
                    bs-datepicker
                  />
                </div>
                <div class="col-md-6">
                  <input
                    type="text"
                    class="form-control"
                    ng-model="filters.to"
                    data-min-date="{{fromDate}}"
                    placeholder="Hasta"
                    bs-datepicker
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-body" cg-busy="loadingResults">
          <table class="table">
            <thead>
              <tr>
                <th>Edificio</th>
                <th>Maquina</th>
                <th>Tipo</th>
                <th>Tarjeta / unidad</th>
                <th>Fecha</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="u in results">
                <td>{{u.machine.building.name}}</td>
                <td>{{u.machine.id}}</td>
                <td>
                  {{u.machine.machine_type == 'WASHER' ? 'Lavado' : 'Secado'}}
                </td>
                <td>{{u.card.uuid + ' / ' + u.card.unit.number}}</td>
                <td>{{u.timestamp}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
