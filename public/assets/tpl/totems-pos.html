<div ng-controller="TotemPosTerminalMappingsController">
  <h2>Mapeo Usuario Totem <-> Pos Terminal</h2>
  <div class="p-25">
    <div class="form-group" ng-repeat="user in totemUsers">
      <p class="p-5">
        <span>
          <strong>{{user.email}}</strong>
        </span>
        <span ng-if="!user.editPosTerminalCode">
          {{user.posTerminalCode}}
        </span>
        <span ng-if="user.editPosTerminalCode">
          <input
            type="text"
            class="form-control"
            ng-model="user.posTerminalCode"
            style="width: 180px; display: inline"
          />
        </span>
        <button
          ng-if="!user.editPosTerminalCode"
          class="btn btn-xs btn-info"
          ng-click="user.editPosTerminalCode = true"
        >
          CAMBIAR
        </button>
        <button
          ng-if="user.editPosTerminalCode"
          class="btn btn-xs btn-success"
          ng-click="savePosTerminalCodeMapping(user)"
        >
          GUARDAR
        </button>
        <button
          ng-if="user.editPosTerminalCode"
          class="btn btn-xs btn-danger"
          ng-click="user.editPosTerminalCode = false"
        >
          CANCELAR
        </button>
      </p>
      <hr class="m-5" />
    </div>
  </div>
</div>
