<div class="dashboard lighten-3" ng-controller="MaintenanceDetailController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15">
        Gestión de Mantenimientos Preventivos del edificio: {{building.name}}
        <input
          type="button"
          class="btn btn-primary"
          ng-click="showBuilding()"
          value="Ir al Edificio"
        />
      </h4>
      <p class="p-h-15 grey-text">{{building.address}}</p>
    </div>
  </div>
  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  <strong>Primer Uso:</strong>
                  {{building.firstUse}}
                </div>
                <div class="col-md-3">
                  <strong>Keep-alive:</strong>
                  {{building.oldestKeepAlive}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row" cg-busy="loadingResults">
    <div class="col-lg-12">
      <table
        class="table table-full table-hover table-mp preventive-details-table"
      >
        <thead>
          <tr>
            <th colspan="4" class="text-center">
              <strong>Lavarropas</strong>
            </th>
            <th colspan="4" class="text-center">
              <strong>Prom. usos diarios</strong>
            </th>
            <th colspan="4" class="text-center">
              <strong>MP500</strong>
            </th>
            <th colspan="4" class="text-center">
              <strong>MP100</strong>
            </th>
          </tr>
          <tr>
            <th ng-click="orderColumnWasher('sort')">
              Sort
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'sort'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('reference')">
              Referencia
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'reference'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('serialNumber')">
              Serial number
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'serialNumber'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('model')">
              Modelo
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'model'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('thirtyDayAverage')">
              30 días
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'thirtyDayAverage'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('sixMonthAverage')">
              6 meses
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'sixMonthAverage'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('oneYearAverage')">
              1 año
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'oneYearAverage'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('totalUses')">
              Usos Totales
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'totalUses'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp500Parameter')">
              Parámetro
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp500Parameter'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp500Maintenance')">
              Último
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp500Maintenance'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp500Technician')">
              Técnico
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp500Technician'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp500Uses')">
              Usos restantes
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp500Uses'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp100Parameter')">
              Parámetro
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp100Parameter'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp100Maintenance')">
              Último
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp100Maintenance'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp100Technician')">
              Técnico
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp100Technician'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
            <th ng-click="orderColumnWasher('mp100Uses')">
              Usos restantes
              <span
                class="sortorder"
                ng-show="filtersWasher.orderBy === 'mp100Uses'"
                ng-class="{reverse: filtersWasher.reverse}"
              />
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="m in washerResults | filter:filtersWasher.search:strict | orderBy: filtersWasher.orderBy: filtersWasher.reverse"
          >
            <td>{{m.sort}}</td>
            <td>{{m.reference}}</td>
            <td>{{m.serialNumber}}</td>
            <td>{{m.model}}</td>
            <td>{{m.average30Days}}</td>
            <td>{{m.average6Months}}</td>
            <td>{{m.average1Year}}</td>
            <td>{{m.totalUses}}</td>
            <td>{{m.parameter.mp500}}</td>
            <td>{{m.mp500Date}}</td>
            <td>{{m.mp500Tech}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp500Class && {'background-color': m.mp500Class}"
            >
              <span>{{m.parameter.mp500 - m.mp500}}</span>
            </td>
            <td>{{m.parameter.mp100}}</td>
            <td>{{m.mp100Date}}</td>
            <td>{{m.mp100Tech}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp100Class && {'background-color': m.mp100Class}"
            >
              <span>{{m.parameter.mp100 - m.mp100}}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row p-v-15" cg-busy="loadingResults">
    <div class="col-lg-12">
      <table
        class="table table-full table-hover table-mp preventive-details-table"
      >
        <thead>
          <tr>
            <th colspan="4" class="text-center">
              <strong>Secarropas</strong>
            </th>
            <th colspan="4" class="text-center">
              <strong>Prom. usos diarios</strong>
            </th>
            <th colspan="4" class="text-center">
              <strong>MP1200</strong>
            </th>
          </tr>
          <tr>
            <th ng-click="orderColumnDryer('sort')">
              Sort
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'sort'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('reference')">
              Referencia
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'reference'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('serialNumber')">
              Serial number
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'serialNumber'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('model')">
              Modelo
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'model'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('thirtyDayAverage')">
              30 días
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'thirtyDayAverage'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('sixMonthAverage')">
              6 meses
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'sixMonthAverage'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('oneYearAverage')">
              1 año
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'oneYearAverage'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('totalUses')">
              Usos Totales
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'totalUses'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('mp1200Parameter')">
              Parámetro
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'mp1200Parameter'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('mp1200Maintenance')">
              Último
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'mp1200Maintenance'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('mp1200Technician')">
              Técnico
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'mp1200Technician'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
            <th ng-click="orderColumnDryer('mp1200Uses')">
              Usos restantes
              <span
                class="sortorder"
                ng-show="filtersDryer.orderBy === 'mp1200Uses'"
                ng-class="{reverse: filtersDryer.reverse}"
              />
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="m in dryerResults | filter:filtersDryer.search:strict | orderBy: filtersDryer.orderBy: filtersDryer.reverse"
          >
            <td>{{m.sort}}</td>
            <td>{{m.reference}}</td>
            <td>{{m.serialNumber}}</td>
            <td>{{m.model}}</td>
            <td>{{m.average30Days}}</td>
            <td>{{m.average6Months}}</td>
            <td>{{m.average1Year}}</td>
            <td>{{m.totalUses}}</td>
            <td>{{m.parameter.mp1200}}</td>
            <td>{{m.mp1200Date}}</td>
            <td>{{m.mp1200Tech}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp1200Class && {'background-color': m.mp1200Class}"
            >
              <span>{{m.parameter.mp1200 - m.mp1200}}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
