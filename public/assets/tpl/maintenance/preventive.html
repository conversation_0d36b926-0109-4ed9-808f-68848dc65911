<div class="dashboard lighten-3" ng-controller="MaintenanceController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Gestión de Mantenimientos Preventivos</h4>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingFilters">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  Localidad Edificio
                  <select
                    class="form-control"
                    ng-model="filters.department"
                    style="width: 100%"
                    ng-options="department as department.text for department in departments track by department.key"
                    ng-change="selectedDepartment(filters.department)"
                  ></select>
                </div>
                <div class="col-md-9">
                  <button
                    class="btn btn-xs btn-warning pull-right"
                    ng-click="invalidateCache()"
                    ng-disabled="isLoading"
                  >
                    Invalidar cache
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid" cg-busy="loadingResults">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Indicadores</div>
        <div class="panel-body">
          <div class="row p-h-15">
            <div class="col-lg-3">
              <div class="row">
                <b>Total de Edificios:</b> {{ indicators.totalBuildings }}
              </div>
              <div class="row">
                <b>Total de Máquinas operativas:</b> {{
                indicators.totalOperativeMachines }}
              </div>
              <div class="row">
                <b>MP100 < 0 / Total Edificios:</b> {{indicators.mp100Below0 |
                number: 0}}%
              </div>
              <div class="row">
                <b>MP1200 < 0 /Total Edificios:</b> {{indicators.mp1200Below0 |
                number: 0}}%
              </div>

              <div class="row">
                <b>MP100 Promedio:</b> {{indicators.averageMp100 | number: 0}}
              </div>
              <div class="row">
                <b>MP500 Promedio:</b> {{indicators.averageMp500 | number: 0}}
              </div>
              <div class="row">
                <b>MP1200 Promedio:</b> {{indicators.averageMp1200 | number: 0}}
              </div>
            </div>
            <div class="col-lg-3">
              <h4 class="text-center">MP1200</h4>
              <table class="fullwidth">
                <tr style="background-color: #cdeef1" class="bold-black">
                  <td class="p-l-5">Entre 0 y 500</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp1200Under500 }}
                  </td>
                </tr>
                <tr style="background-color: #a7d7e8" class="bold-black">
                  <td class="p-l-5">Entre 501 y 1000</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp1200Under1000 }}
                  </td>
                </tr>
                <tr style="background-color: #48a0e2" class="bold-black">
                  <td class="p-l-5">Entre 1001 y 1500</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp1200Under1500 }}
                  </td>
                </tr>
                <tr style="background-color: #4148c5" class="bold-white">
                  <td class="p-l-5">Mas de 1500</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp1200Over1500 }}
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-lg-3">
              <h4 class="text-center">MP500</h4>
              <table class="fullwidth">
                <tr style="background-color: #cdeef1" class="bold-black">
                  <td class="p-l-5">Entre 0 y 100</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp500Under100 }}
                  </td>
                </tr>
                <tr style="background-color: #a7d7e8" class="bold-black">
                  <td class="p-l-5">Entre 101 y 500</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp500Under500 }}
                  </td>
                </tr>
                <tr style="background-color: #48a0e2" class="bold-black">
                  <td class="p-l-5">Entre 501 y 600</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp500Under600 }}
                  </td>
                </tr>
                <tr style="background-color: #4148c5" class="bold-white">
                  <td class="p-l-5">Más de 600</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp500Over600 }}
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-lg-3">
              <h4 class="text-center">MP100</h4>
              <table class="fullwidth">
                <tr style="background-color: #cdeef1" class="bold-black">
                  <td class="p-l-5">Entre 0 y 50</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp100Under50 }}
                  </td>
                </tr>
                <tr style="background-color: #a7d7e8" class="bold-black">
                  <td class="p-l-5">Entre 51 y 70</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp100Under70 }}
                  </td>
                </tr>
                <tr style="background-color: #48a0e2" class="bold-black">
                  <td class="p-l-5">Entre 71 y 100</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp100Under100 }}
                  </td>
                </tr>
                <tr style="background-color: #4148c5" class="bold-white">
                  <td class="p-l-5">Más de 100</td>
                  <td class="text-right p-r-5">
                    {{ indicators.mp100Over100 }}
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-12">
      <table class="table table-full table-hover table-mp">
        <thead class="sticky-below-navbar">
          <th class="hidden"></th>
          <th ng-click="orderColumn('buildingName')">
            Edificio
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'buildingName'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp1200Date')">
            MP1200 Fecha
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp1200Date'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp1200')">
            MP1200 Usos rest.
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp1200'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp500Date')">
            MP500 Fecha
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp500Date'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp500')">
            MP500 Usos rest.
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp500'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp100Date')">
            MP100 Fecha
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp100Date'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('mp100')">
            MP100 Usos rest.
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'mp100'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>

          <th ng-click="orderColumn('average30Days')">
            Prom x 30 dias
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'average30Days'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('average6Months')">
            Prom 6 meses
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'average6Months'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
          <th ng-click="orderColumn('average1Year')">
            Prom x 1 año
            <span
              class="sortorder"
              ng-show="filters.orderBy === 'average1Year'"
              ng-class="{reverse: filters.reverse}"
            />
          </th>
        </thead>
        <tbody>
          <tr
            ng-repeat="m in results | filter:filters.search:strict | orderBy: filters.orderBy: filters.reverse: orderByDecimal"
            ng-click="showBuilding(m.buildingId)"
          >
            <td class="hidden">{{m.department}}</td>
            <td>{{m.buildingName}}</td>
            <td>{{m.mp1200Date}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp1200Class && {'background-color': m.mp1200Class}"
            >
              {{m.mp1200}}
            </td>
            <td>{{m.mp500Date}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp500Class && {'background-color': m.mp500Class}"
            >
              {{m.mp500}}
            </td>
            <td>{{m.mp100Date}}</td>
            <td
              class="bold-black"
              ng-style="!!m.mp100Class && {'background-color': m.mp100Class}"
            >
              {{m.mp100}}
            </td>
            <td>{{m.average30Days | number: 2}}</td>
            <td>{{m.average6Months | number: 2}}</td>
            <td>{{m.average1Year | number: 2}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
