<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <section class="messages">
          <div class="page-header">
            <h1>
              <i class="md md-photo"></i>
              Messages &amp; Notifications
            </h1>
            <p class="lead">
              Messages and notifications are used to get something clear to the
              user that needs attention or just validation.
            </p>
          </div>

          <div class="row">
            <div class="col-lg-12">
              <div class="bs-component">
                <div class="alert alert-dismissible alert-warning">
                  <button type="button" class="close" data-dismiss="alert">
                    ×
                  </button>
                  <h4>Warning!</h4>
                  <p>
                    Best check yo self, you're not looking too good. Nulla vitae
                    elit libero, a pharetra augue. Praesent commodo cursus
                    magna,
                    <a href="" class="alert-link"
                      >vel scelerisque nisl consectetur et</a
                    >.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-4">
              <div class="bs-component">
                <div class="alert alert-dismissible alert-danger">
                  <button type="button" class="close" data-dismiss="alert">
                    ×
                  </button>
                  <h4>Danger!</h4>
                  <strong>Oh snap!</strong>
                  <a href="" class="alert-link">Change a few things up</a>
                  and try submitting again.
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="bs-component">
                <div class="alert alert-dismissible alert-success">
                  <button type="button" class="close" data-dismiss="alert">
                    ×
                  </button>
                  <h4>Success!</h4>
                  <strong>Well done!</strong> You successfully read
                  <a href="" class="alert-link">this important alert message</a
                  >.
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="bs-component">
                <div class="alert alert-dismissible alert-info">
                  <button type="button" class="close" data-dismiss="alert">
                    ×
                  </button>
                  <h4>Info!</h4>
                  <strong>Heads up!</strong> This
                  <a href="" class="alert-link">alert needs your attention</a>,
                  but it's not super important.
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-4">
              <div class="card bordered">
                <div class="card-header alert alert-danger">
                  <span class="card-title">Danger</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text">Dismiss</a>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card bordered">
                <div class="card-header alert alert-success">
                  <span class="card-title">Success</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text">Dismiss</a>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-4">
              <div class="card bordered">
                <div class="card-header alert alert-info">
                  <span class="card-title">Info</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text">Dismiss</a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row m-b-40">
            <div class="col-lg-4">
              <h5>Modals</h5>
              <button
                type="button"
                class="btn btn-primary btn-lg"
                data-animation="am-fade-and-slide-top"
                data-template="assets/tpl/partials/modal.html"
                bs-modal="modal"
              >
                Launch demo modal
                <br />
                <small class="f11">(using data-template)</small>
              </button>
            </div>

            <div class="col-md-6 col-lg-4">
              <h5>Popovers</h5>
              <div class="btn-group" role="group" aria-label="popovers">
                <button
                  type="button"
                  data-placement="left"
                  title="On the Left!"
                  data-content="You found me!"
                  class="btn btn-default"
                  bs-popover
                >
                  Left
                </button>
                <button
                  type="button"
                  data-placement="top"
                  title="On the Top!"
                  data-content="You found me!"
                  class="btn btn-default"
                  bs-popover
                >
                  Top
                </button>
                <button
                  type="button"
                  data-placement="bottom"
                  title="On the Bottom!"
                  data-content="You found me!"
                  class="btn btn-default"
                  bs-popover
                >
                  Bottom
                </button>
                <button
                  type="button"
                  data-placement="right"
                  title="On the Right!"
                  data-content="You found me!"
                  class="btn btn-default"
                  bs-popover
                >
                  Right
                </button>
              </div>
            </div>

            <div class="col-md-6 col-lg-4">
              <h5>Tooltips</h5>
              <div class="btn-group" role="group" aria-label="tooltips">
                <button
                  type="button"
                  class="btn btn-default"
                  data-placement="left"
                  data-title="Tooltip on left"
                  bs-tooltip
                >
                  Left
                </button>
                <button
                  type="button"
                  class="btn btn-default"
                  data-placement="top"
                  data-title="Tooltip on top"
                  bs-tooltip
                >
                  Top
                </button>
                <button
                  type="button"
                  class="btn btn-default"
                  data-placement="bottom"
                  data-title="Tooltip on bottom"
                  bs-tooltip
                >
                  Bottom
                </button>
                <button
                  type="button"
                  class="btn btn-default"
                  data-placement="right"
                  data-title="Tooltip on right"
                  bs-tooltip
                >
                  Right
                </button>
              </div>
            </div>
          </div>

          <div class="row m-b-40">
            <div class="col-md-4">
              <div class="card">
                <div class="card-content">
                  <div class="card-title">Default labels</div>

                  <span class="label label-default">Default</span>
                  <span class="label label-success">Success</span>
                  <span class="label label-warning">Warning</span>
                  <span class="label label-danger">Danger</span>
                  <span class="label label-info">Info</span>

                  <hr />
                  <h5 class="no-margin">Default badges</h5>
                  <span class="badge">12</span>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-content">
                  <div class="card-title">Color labels</div>
                  <span ng-repeat="(k,color) in theme_colors">
                    <span class="label {{color}}">{{color}} label</span>
                  </span>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-content">
                  <div class="card-title">Color badges</div>
                  <h5 class="no-margin"></h5>
                  <span ng-repeat="(k,color) in theme_colors">
                    <span class="badge {{color}}">{{color}} badge</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <h3>Progess <span class="w300">bars</span></h3>
              <p>Progress bars from bootstrap come in 5 different colors.</p>
              <br />
              <div class="p-v-10">
                <div class="progress">
                  <div class="progress-bar" style="width: 80%"></div>
                </div>
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-info"
                    style="width: 60%"
                  ></div>
                </div>
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-warning"
                    style="width: 50%"
                  ></div>
                </div>
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-danger"
                    style="width: 40%"
                  ></div>
                </div>
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-success"
                    style="width: 30%"
                  ></div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <h3>Misc <span class="w300">bars</span></h3>
              <div class="p-v-10">
                <p>
                  You can stack different colors as part of a total progress
                  bar.
                </p>
                <br />
                <div class="progress">
                  <div class="progress-bar" style="width: 20%"></div>
                  <div
                    class="progress-bar progress-bar-success"
                    style="width: 35%"
                  ></div>
                  <div
                    class="progress-bar progress-bar-warning"
                    style="width: 20%"
                  ></div>
                  <div
                    class="progress-bar progress-bar-danger"
                    style="width: 10%"
                  ></div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <h3>Animated <span class="w300">bars</span></h3>
              <div class="p-v-10">
                <p>
                  Progress bars can be animated by adding a simple active class.
                </p>
                <br />

                <div class="progress progress-striped active">
                  <div class="progress-bar" style="width: 80%"></div>
                </div>
                <div class="progress progress-striped active">
                  <div
                    class="progress-bar progress-bar-info"
                    style="width: 60%"
                  ></div>
                </div>
                <div class="progress progress-striped active">
                  <div
                    class="progress-bar progress-bar-warning"
                    style="width: 50%"
                  ></div>
                </div>
                <div class="progress progress-striped active">
                  <div
                    class="progress-bar progress-bar-danger"
                    style="width: 40%"
                  ></div>
                </div>
                <div class="progress progress-striped active">
                  <div
                    class="progress-bar progress-bar-success"
                    style="width: 30%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
