<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <section class="cards">
          <div class="page-header">
            <h1>
              <i class="md md-photo"></i>
              Cards
            </h1>
            <p class="lead">
              Material design defines cards as floating containers. There are
              several types of cards.
            </p>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="card">
                <div class="card-image">
                  <img src="assets/img/photos/1.jpg" />
                  <span class="card-title">Card Title</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text">Link 1</a>
                    <a href="" class="btn btn-link orange-text">Link 2</a>
                    <a href="" class="btn btn-link blue-text accent-2"
                      >Link 3</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-image">
                  <img src="assets/img/photos/2.jpg" />
                  <span class="card-title">Card click image</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link orange-text"
                      >More information</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card card-flip">
                <div class="card-image">
                  <img src="assets/img/photos/3.jpg" class="activator" />
                  <span class="card-title">Content reveal card</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-reveal">
                  <span class="card-title grey-text text-darken-4"
                    >Card Title <i class="md md-close pull-right btn-flip"></i
                  ></span>
                  <p>
                    Here is some more information about this product that is
                    only revealed once clicked on.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link btn-icon btn-flip"
                      ><i class="md md-more-vert"></i
                    ></a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="card">
                <div class="card-image">
                  <img src="assets/img/photos/4.jpg" />
                  <div class="card-title">Card profile icon</div>
                </div>
                <div class="card-content">
                  <div class="card-profile pull-right">
                    <img
                      src="assets/img/icons/ballicons/bill.svg"
                      alt=""
                      style="width: 75px"
                    />
                  </div>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-image">
                  <img src="assets/img/photos/6.jpg" class="p-5" />
                  <div class="card-title">Card profile icon 2</div>
                </div>
                <div class="card-content">
                  <div class="card-profile pull-right">
                    <img
                      src="assets/img/icons/ballicons/wrench.svg"
                      alt=""
                      style="width: 75px"
                    />
                  </div>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-image">
                  <img src="assets/img/photos/7.jpg" />
                  <div class="card-title">Card profile icon 3</div>
                </div>
                <div class="card-content">
                  <div class="card-profile pull-right">
                    <img
                      src="assets/img/icons/ballicons/converse.svg"
                      alt=""
                      style="width: 75px"
                    />
                  </div>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="card bordered">
                <div class="card-header">
                  <span class="card-title">Card bordered with header</span>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <a href="" class="btn btn-link btn-icon"
                    ><i class="md md-favorite red-text"></i
                  ></a>
                  <a href="" class="btn btn-link btn-icon"
                    ><i class="md md-refresh black-text"></i
                  ></a>
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text"
                      >This is a link</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card bordered">
                <div class="card-content">
                  <span class="card-title">Card bordered</span>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text"
                      >This is a link</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card">
                <div class="card-content">
                  <span class="card-title">Boring card</span>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row m-b-60">
            <div class="col-md-4">
              <div class="card bordered small">
                <div class="card-header">
                  <div class="card-title">Card small</div>
                </div>
                <div class="card-content">
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <a href="" class="btn btn-link btn-icon"
                    ><i class="md md-favorite red-text"></i
                  ></a>
                  <a href="" class="btn btn-link btn-icon"
                    ><i class="md md-refresh black-text"></i
                  ></a>
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text"
                      >This is a link</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card bordered">
                <div class="card-content">
                  <div class="card-title">Card normal</div>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
                <div class="card-action clearfix">
                  <div class="pull-right">
                    <a href="" class="btn btn-link black-text"
                      >This is a link</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card large">
                <div class="card-content">
                  <div class="card-title">Card large</div>
                  <p>
                    I am a very simple card. I am good at containing small bits
                    of information. I am convenient because I require little
                    markup to use effectively.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="row m-b-60">
            <div class="col-md-12">
              <div class="card bordered">
                <div class="card-header">
                  <span class="card-title">Data card</span>
                </div>

                <div class="table-responsive">
                  <table class="table table-full table-full-small">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Last</th>
                        <th>Summary</th>
                        <th class="text-right">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Abbey</td>
                        <td>Johnson</td>
                        <td>
                          Fuisset delicatissimi duo, qui ut animal noluisse
                          erroribus.
                        </td>
                        <td class="text-right">
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Edit row"
                          >
                            <i class="md md-mode-edit"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Copy row"
                          >
                            <i class="md md-content-copy"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Delete row"
                          >
                            <i class="md md-delete"></i>
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>Alex</td>
                        <td>Nelson</td>
                        <td>
                          Ea eum veniam audire. Dicant vituperata consequuntur.
                        </td>
                        <td class="text-right">
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Edit row"
                          >
                            <i class="md md-mode-edit"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Copy row"
                          >
                            <i class="md md-content-copy"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Delete row"
                          >
                            <i class="md md-delete"></i>
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>Mary</td>
                        <td>Peterson</td>
                        <td>
                          Per at postea mediocritatem, vim numquam aliquid eu,
                          in nam sale gubergren.
                        </td>
                        <td class="text-right">
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Edit row"
                          >
                            <i class="md md-mode-edit"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Copy row"
                          >
                            <i class="md md-content-copy"></i>
                          </button>
                          <button
                            type="button"
                            class="btn btn-link btn-round"
                            data-toggle="tooltip"
                            data-placement="top"
                            data-original-title="Delete row"
                          >
                            <i class="md md-delete"></i>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div class="card-action clearfix">
                  <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-5 col-xs-12">
                      <a href="" class="btn btn-link btn-icon"
                        ><i class="md md-favorite red-text"></i
                      ></a>
                      <a href="" class="btn btn-link btn-icon"
                        ><i class="md md-refresh black-text"></i
                      ></a>
                    </div>
                    <div
                      class="col-lg-10 col-md-8 col-sm-7 col-xs-12 text-right"
                    >
                      <ul class="pagination">
                        <li class="disabled"><a href="">«</a></li>
                        <li class="active"><a href="">1</a></li>
                        <li><a href="">2</a></li>
                        <li><a href="">3</a></li>
                        <li><a href="">4</a></li>
                        <li class="hidden-xs"><a href="">5</a></li>
                        <li><a href="">»</a></li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row m-b-60">
            <div class="col-md-3 col-md-push-9">
              <h5>Depth or floating helpers</h5>
              <p>
                Adding a class z-depth-0/4 to a container will make it float.
              </p>
            </div>
            <div class="col-md-8 col-md-pull-3">
              <div class="row">
                <div class="col-md-3">
                  <div class="card">
                    <div class="card-content">
                      <div class="card-title">z-depth-0</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card z-depth-1">
                    <div class="card-content">
                      <div class="card-title">z-depth-1</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card z-depth-2">
                    <div class="card-content">
                      <div class="card-title">z-depth-2</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card z-depth-3">
                    <div class="card-content">
                      <div class="card-title">z-depth-3</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-3 col-md-push-9">
              <h5>Colors on cards</h5>
              <p>
                Using the default material design colors you are free to style
                cards with fancy colors.
              </p>
            </div>
            <div class="col-md-8 col-md-pull-3">
              <div class="row">
                <div class="col-md-3">
                  <div class="card blue">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card blue darken-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card blue accent-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card blue accent-3">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-3">
                  <div class="card red">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card red darken-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card red accent-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card red accent-3">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-3">
                  <div class="card pink">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card pink darken-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card pink accent-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card pink accent-3">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-3">
                  <div class="card teal">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card teal lighten-1">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card teal lighten-2">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>

                <div class="col-md-3">
                  <div class="card teal lighten-3">
                    <div class="card-content">
                      <div class="card-title">Card</div>
                      <p>
                        I am a very simple card. I am good at containing small
                        bits of information.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
