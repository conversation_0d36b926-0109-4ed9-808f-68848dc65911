<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <section class="grids">
          <div class="page-header">
            <h1>
              <i class="md md-photo"></i>
              Grid examples
            </h1>
            <p class="lead">
              Because the theme is based on bootstrap 3 you can easily<br />
              use the bootstrap 3 grid system everybody loves.
            </p>
          </div>

          <h3>Example: Stacked-to-horizontal</h3>
          <p>
            Using a single set of <code>.col-md-*</code> grid classes, you can
            create a basic grid system that starts out stacked on mobile devices
            and tablet devices (the extra small to small range) before becoming
            horizontal on desktop (medium) devices. Place grid columns in any
            <code>.row</code>.
          </p>
          <div class="element-block">
            <div class="row">
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
              <div class="col-md-1">.col-md-1</div>
            </div>
            <div class="row">
              <div class="col-md-8">.col-md-8</div>
              <div class="col-md-4">.col-md-4</div>
            </div>
            <div class="row">
              <div class="col-md-4">.col-md-4</div>
              <div class="col-md-4">.col-md-4</div>
              <div class="col-md-4">.col-md-4</div>
            </div>
            <div class="row">
              <div class="col-md-6">.col-md-6</div>
              <div class="col-md-6">.col-md-6</div>
            </div>
          </div>

          <h3 id="grid-example-mixed">
            Example: Mobile and desktop<a
              class="anchorjs-link"
              href="#grid-example-mixed"
              ><span class="anchorjs-icon"></span
            ></a>
          </h3>
          <p>
            Don't want your columns to simply stack in smaller devices? Use the
            extra small and medium device grid classes by adding
            <code>.col-xs-*</code> <code>.col-md-*</code> to your columns. See
            the example below for a better idea of how it all works.
          </p>
          <div class="element-block">
            <div class="row">
              <div class="col-xs-12 col-md-8">.col-xs-12 .col-md-8</div>
              <div class="col-xs-6 col-md-4">.col-xs-6 .col-md-4</div>
            </div>
            <div class="row">
              <div class="col-xs-6 col-md-4">.col-xs-6 .col-md-4</div>
              <div class="col-xs-6 col-md-4">.col-xs-6 .col-md-4</div>
              <div class="col-xs-6 col-md-4">.col-xs-6 .col-md-4</div>
            </div>
            <div class="row">
              <div class="col-xs-6">.col-xs-6</div>
              <div class="col-xs-6">.col-xs-6</div>
            </div>
          </div>

          <h3 id="grid-example-mixed-complete">
            Example: Mobile, tablet, desktop<a
              class="anchorjs-link"
              href="#grid-example-mixed-complete"
              ><span class="anchorjs-icon"></span
            ></a>
          </h3>
          <p>
            Build on the previous example by creating even more dynamic and
            powerful layouts with tablet <code>.col-sm-*</code> classes.
          </p>
          <div class="element-block">
            <div class="row">
              <div class="col-xs-12 col-sm-6 col-md-8">
                .col-xs-12 .col-sm-6 .col-md-8
              </div>
              <div class="col-xs-6 col-md-4">.col-xs-6 .col-md-4</div>
            </div>
            <div class="row">
              <div class="col-xs-6 col-sm-4">.col-xs-6 .col-sm-4</div>
              <div class="col-xs-6 col-sm-4">.col-xs-6 .col-sm-4</div>
              <!-- Optional: clear the XS cols if their content doesn't match in height -->
              <div class="clearfix visible-xs-block"></div>
              <div class="col-xs-6 col-sm-4">.col-xs-6 .col-sm-4</div>
            </div>
          </div>

          <h3 id="grid-offsetting">
            Offsetting columns<a class="anchorjs-link" href="#grid-offsetting"
              ><span class="anchorjs-icon"></span
            ></a>
          </h3>
          <p>
            Move columns to the right using
            <code>.col-md-offset-*</code> classes. These classes increase the
            left margin of a column by <code>*</code> columns. For example,
            <code>.col-md-offset-4</code> moves <code>.col-md-4</code> over four
            columns.
          </p>
          <div class="element-block">
            <div class="row">
              <div class="col-md-4">.col-md-4</div>
              <div class="col-md-4 col-md-offset-4">
                .col-md-4 .col-md-offset-4
              </div>
            </div>
            <div class="row">
              <div class="col-md-3 col-md-offset-3">
                .col-md-3 .col-md-offset-3
              </div>
              <div class="col-md-3 col-md-offset-3">
                .col-md-3 .col-md-offset-3
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 col-md-offset-3">
                .col-md-6 .col-md-offset-3
              </div>
            </div>
          </div>

          <div class="table-responsive">
            <table class="table table-bordered table-striped white">
              <thead>
                <tr>
                  <th></th>
                  <th>
                    Extra small devices
                    <small>Phones (&lt;768px)</small>
                  </th>
                  <th>
                    Small devices
                    <small>Tablets (≥768px)</small>
                  </th>
                  <th>
                    Medium devices
                    <small>Desktops (≥992px)</small>
                  </th>
                  <th>
                    Large devices
                    <small>Desktops (≥1200px)</small>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th class="text-nowrap" scope="row">Grid behavior</th>
                  <td>Horizontal at all times</td>
                  <td colspan="3">
                    Collapsed to start, horizontal above breakpoints
                  </td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Container width</th>
                  <td>None (auto)</td>
                  <td>750px</td>
                  <td>970px</td>
                  <td>1170px</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Class prefix</th>
                  <td><code>.col-xs-</code></td>
                  <td><code>.col-sm-</code></td>
                  <td><code>.col-md-</code></td>
                  <td><code>.col-lg-</code></td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row"># of columns</th>
                  <td colspan="4">12</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Column width</th>
                  <td class="text-muted">Auto</td>
                  <td>~62px</td>
                  <td>~81px</td>
                  <td>~97px</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Gutter width</th>
                  <td colspan="4">30px (15px on each side of a column)</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Nestable</th>
                  <td colspan="4">Yes</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Offsets</th>
                  <td colspan="4">Yes</td>
                </tr>
                <tr>
                  <th class="text-nowrap" scope="row">Column ordering</th>
                  <td colspan="4">Yes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
