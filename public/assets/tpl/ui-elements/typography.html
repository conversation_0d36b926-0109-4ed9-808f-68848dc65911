<div class="modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-body">
        <section class="typography">
          <div class="page-header">
            <h1>
              <i class="md md-photo"></i>
              Typography Page Header <small>With Small Text</small>
            </h1>
            <p class="lead">Page header lead.</p>
          </div>

          <div class="row m-b-40">
            <div class="col-lg-4">
              <h1>This is an h1 heading</h1>
              <h2>This is an h2 heading</h2>
              <h3>This is an h3 heading</h3>
              <h4>This is an h4 heading</h4>
              <h5>This is an h5 heading</h5>
              <h6>This is an h6 heading</h6>
            </div>
            <div class="col-lg-4">
              <h3>Example text</h3>
              <p>
                <PERSON><PERSON><PERSON> quis risus eget <a href>urna mollis ornare</a> vel eu
                leo. Cum sociis natoque penatibus et magnis dis parturient
                montes, nascetur ridiculus mus. Nullam id dolor id nibh
                ultricies vehicula.
              </p>
              <p>
                <small
                  >This line of text is meant to be treated as fine
                  print.</small
                >
              </p>
              <p>
                The following snippet of text is
                <strong>rendered as bold text</strong>.
              </p>
              <p>
                The following snippet of text is
                <em>rendered as italicized text</em>.
              </p>
              <p>
                An abbreviation of the word attribute is
                <abbr title="attribute">attr</abbr>.
              </p>
            </div>
            <div class="col-lg-4">
              <h3>Paragraphs</h3>
              <p>
                This is an <b>ordinary paragraph</b> that is
                <i>long enough</i> to wrap to <u>multiple lines</u> so that you
                can see how the line spacing looks.
              </p>
              <p class="text-muted">Muted color paragraph.</p>
              <p class="text-warning">Warning color paragraph.</p>
              <p class="text-danger">Danger color paragraph.</p>
              <p class="text-info">Info color paragraph.</p>
              <p class="text-success">Success color paragraph.</p>
              <p>
                <small
                  >This is text in a <code>small</code> wrapper.
                  <abbr title="No Big Deal">NBD</abbr>, right?</small
                >
              </p>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="well white">
                <div class="row">
                  <div class="col-xs-6">
                    <ul>
                      <li>Normal Unordered List</li>
                      <li>
                        Can Also Work
                        <ul>
                          <li>With Nested Children</li>
                          <li>With Nested Children</li>
                          <li>With Nested Children</li>
                        </ul>
                      </li>
                      <li>Adds Bullets to Page</li>
                    </ul>
                  </div>
                  <div class="col-xs-6">
                    <ol>
                      <li>Normal Ordered List</li>
                      <li>
                        Can Also Work
                        <ol>
                          <li>With Nested Children</li>
                          <li>With Nested Children</li>
                          <li>With Nested Children</li>
                        </ol>
                      </li>
                      <li>Adds Bullets to Page</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="well white">
                <address>
                  <strong>Twitter, Inc.</strong><br />
                  795 Folsom Ave, Suite 600<br />
                  San Francisco, CA 94107<br />
                  <abbr title="Phone">P:</abbr> (*************
                </address>
                <address class="no-margin">
                  <strong>Full Name</strong><br />
                  <a href><EMAIL></a>
                </address>
              </div>
            </div>
            <div class="col-md-3">
              <div class="well white">
                <blockquote>
                  Here's what a blockquote looks like in Bootstrap.
                  <small>Use <code>small</code> to identify the source.</small>
                </blockquote>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
