<div class="dashboard lighten-3" ng-controller="BuildingsController">
  <div
    class="row sticky-below-navbar grey.lighten-5"
    style="background: #fafafa"
  >
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Administración de Edificios
        <!-- TODO uncomment -->
        <button
          class="btn btn-xs btn-success pull-right"
          style="margin-left: 10px"
          ng-click="showLoadPayment()"
        >
          Cargar Pagos
        </button>
        <!-- TODO end -->
        <button
          class="btn btn-xs btn-info pull-right"
          ng-click="createBuilding()"
        >
          Crear edificio
        </button>
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refreshBuildings()"
        >
          Recargar
        </button>
        <input
          type="text"
          class="form-control input-sm pull-right"
          style="margin-right: 10px; max-width: 25%"
          ng-model="filters.quickSearch"
          placeholder="Búsqueda Rápida"
        />
      </h4>
    </div>
  </div>

  <div class="row" cg-busy="loadingBuildings">
    <div class="col-lg-12">
      <table class="table table-full table-hover">
        <thead style="position: sticky; top: 135px; z-index: 2">
          <tr>
            <th ng-click="orderColumn('id')">
              Id
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'id'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('name')">
              Nombre
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'name'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('address')">
              Dirección
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'address'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('contact')">
              Contacto
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'contact'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('administration.name')">
              Administrador
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'administration.name'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th ng-click="orderColumn('lastBillingDate')">
              Último cierre (Día cierre)
              <span
                class="sortorder"
                ng-show="filters.orderBy === 'lastBillingDate'"
                ng-class="{reverse: filters.reverse}"
              />
            </th>
            <th>Acción</th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="building in buildings | filter:filters.quickSearch | orderBy: filters.orderBy: filters.reverse"
            ng-click="showBuilding(building)"
            ng-class="{ 'tr-danger': building.isBillingPending, 'bg-light-grey': !building.isBillable && !building.isBillingPending}"
          >
            <td>{{building.id}}</td>
            <td>{{building.name}}</td>
            <td>{{building.address}}</td>
            <td>{{building.contact}}</td>
            <td>
              {{!building.administration ? 'No especificado':
              building.administration.name}}
            </td>
            <td ng-class="{'green-text': building.billingUpToDate}">
              {{building.lastBillingDate}}
              <span
                ng-if="building.administration && building.administration.closureDay >= 1"
                >({{building.administration.closureDay}})</span
              >
            </td>
            <td>
              <button
                class="btn btn-xs btn-warning"
                no-propagate="editBuilding(building)"
                no-propagate-model="building"
              >
                Editar
              </button>
              <button
                class="btn btn-xs btn-danger"
                no-propagate="deleteBuilding(building)"
                no-propagate-model="building"
              >
                Borrar
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script type="text/ng-template" id="/building-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>
      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="savingUser">
              <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>{{item.editing ? 'Editar' :'Crear nuevo'}} Edificio</legend>
                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                              <h4>Error</h4>
                              {{errorMessage}}
                          </div>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Nombre</label>
                          <input type="text" class="form-control" ng-model="item.name" required>
                      </div>

                      <div class="form-group filled">
                          <label class="control-label">Nombre Facturación</label>
                          <div class="input-group">
                              <input type="text" class="form-control" ng-model="item.billingName"
                                     ng-disabled="syncBillingNameWithName" required>
                              <span class="input-group-addon">
                                <label class="checkbox-inline">
                                    <input type="checkbox" ng-model="syncBillingNameWithName"
                                           ng-change="billingNameSync()">
                                    Igual al Nombre del Edificio
                                </label>
                              </span>
                          </div>
                      </div>
                      <div class="form-group filled">
                        <label class="control-label">RUT</label>
                        <input type="text" class="form-control" ng-model="item.rut">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Pais</label>
                          <select class="form-control" ng-model="item.country" required>
                              <option value="Uruguay" selected>Uruguay</option>
                              <option value="Paraguay">Paraguay</option>
                          </select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Ciudad</label>
                          <input type="text" class="form-control" ng-model="item.city">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Departamento</label>
                          <input type="text" class="form-control" ng-model="item.state">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Dirección</label>
                          <input type="text" class="form-control" ng-model="item.address">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Latitud</label>
                          <input type="number" class="form-control" ng-model="item.latitude" min="-90" max="90" step="0.000001">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Logitud</label>
                          <input type="number" class="form-control" ng-model="item.longitude" min="-180" max="180" step="0.000001">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Contacto</label>
                          <input type="text" class="form-control" ng-model="item.contact">
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Administrador</label>
                          <select class="form-control" ng-model="item.administration" ng-options="a.name for a in data.administrations track by a.id"></select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Ratio</label>
                          <select class="form-control" ng-model="item.rate" ng-options="r.name for r in data.rates track by r.id"></select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Tipo</label>
                          <select class="form-control" ng-model="item.buildingType" ng-change="updateClosureTypes(item.buildingType)"
                                  ng-options="key as value for (key , value) in buildingTypes">
                          </select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Cierre</label>
                          <select class="form-control" ng-model="item.closureType"
                                  ng-options="key as value for (key , value) in filteredClosureTypes">
                          </select>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Usos Prebloqueo</label>
                          <input type="text" class="form-control" ng-model="item.preBlockedUses">
                      </div>
                      <div class="form-group">
                          <button type="submit" class="btn btn-primary">Guardar</button>
                      </div>
                  </fieldset>
              </form>
          </div>
      </div>
  </div>
</script>
<script type="text/ng-template" id="/payment-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
          <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>
      <div class="aside-dialog">
          <div class="aside-body bs-sidebar" cg-busy="uploadingPayment">
              <form class="form-floating" novalidate="novalidate" ng-submit="savePayment(item)">
                  <fieldset>
                      <legend><span ng-bind-html="item.icon"></span>Cargar archivo de Pagos</legend>
                      <div class="bs-component" ng-if="errorMessage != null">
                          <div class="alert alert-dismissible alert-danger">
                              <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                              <h4>Error</h4>
                              <span ng-bind-html='errorMessage'></span>
                          </div>
                      </div>
                      <div class="form-group filled">
                          <label class="control-label">Archivo</label>
                          <label class="btn btn-outline-primary">
                              <span>Seleccionar Archivo</span>
                              <input type="file" style="display: none" ngf-select class="form-control" required ng-model="data.file" accept=".xls,.xlsx">
                          </label>
                          <span>{{ data.file && data.file.name }}</span>
                      </div>
                      <div class="form-group">
                          <button type="submit" class="btn btn-primary" ng-click="uploadPaymentFile(data.file)">Cargar</button>
                      </div>
                  </fieldset>
              </form>
          </div>
      </div>
  </div>
</script>
