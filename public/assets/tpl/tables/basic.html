<section ng-controller="TablesBasicController">
  <div class="page-header">
    <h1>
      <i class="md md-list"></i>
      Basic tables
    </h1>
    <p class="lead">
      This is an example how to display table data and default actions you can
      use.
    </p>
  </div>

  <div class="row m-b-40">
    <div class="col-md-6">
      <div class="card no-margin">
        <div class="table-responsive white">
          <h3 class="table-title p-20">Basic table</h3>

          <table class="table table-full table-full-small">
            <colgroup>
              <col class="auto-cell-size p-r-20" />
            </colgroup>
            <thead>
              <tr>
                <th>Icon</th>
                <th>Name</th>
                <th>Last</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="(key, item) in data">
                <td ng-bind-html="item.icon" class="f20"></td>
                <td>{{ item.firstname }}</td>
                <td>{{ item.lastname }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card no-margin">
        <div class="table-responsive white">
          <h3 class="table-title p-20">Striped table</h3>

          <table class="table table-striped table-full table-full-small">
            <colgroup>
              <col class="auto-cell-size" />
            </colgroup>
            <thead>
              <tr>
                <th>Icon</th>
                <th>Name</th>
                <th>Last</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="(key, item) in data">
                <td ng-bind-html="item.icon" class="f20"></td>
                <td>{{ item.firstname }}</td>
                <td>{{ item.lastname }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="card no-margin">
        <div class="table-responsive white">
          <h3 class="table-title p-20">Hover table</h3>

          <table class="table table-hover table-full table-full-small">
            <colgroup>
              <col class="auto-cell-size" />
            </colgroup>
            <thead>
              <tr>
                <th>Icon</th>
                <th>Name</th>
                <th>Last</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="(key, item) in data">
                <td ng-bind-html="item.icon" class="f20"></td>
                <td>{{ item.firstname }}</td>
                <td>{{ item.lastname }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card no-margin">
        <div class="table-responsive white">
          <h3 class="table-title p-20">Bordered table</h3>

          <table class="table table-bordered table-full table-full-small">
            <colgroup>
              <col class="auto-cell-size" />
            </colgroup>
            <thead>
              <tr>
                <th>Icon</th>
                <th>Name</th>
                <th>Last</th>
              </tr>
            </thead>
            <tbody>
              <tr ng-repeat="(key, item) in data">
                <td ng-bind-html="item.icon" class="f20"></td>
                <td>{{ item.firstname }}</td>
                <td>{{ item.lastname }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>
