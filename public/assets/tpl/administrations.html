<div class="dashboard lighten-3" ng-controller="AdministrationsController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">
        Administraciones
        <button
          class="btn btn-xs btn-info pull-right"
          ng-click="createAdministration()"
        >
          <PERSON><PERSON><PERSON>
        </button>
        <button
          class="btn btn-xs btn-warning pull-right"
          style="margin-right: 10px"
          ng-click="refreshAdministrations()"
        >
          Recargar
        </button>
        <input
          type="text"
          class="form-control input-sm pull-right"
          style="margin-right: 10px; max-width: 25%"
          ng-model="filters.quickSearch"
          placeholder="Búsqueda Rápida"
        />
      </h4>
    </div>
  </div>
  <div class="row" cg-busy="loadingAdministrations">
    <div class="col-lg-12">
      <table class="table table-full table-hover">
        <thead>
          <tr>
            <th>Id</th>
            <th>Nombre</th>
            <th>Dirección</th>
            <th>Contacto</th>
            <th>Cobrador</th>
            <th><PERSON><PERSON></th>
            <th>Acción</th>
          </tr>
        </thead>
        <tbody>
          <tr
            ng-repeat="a in administrations | filter:filters.quickSearch"
            ng-class="{'bg-red-light' : a.mpTokenAboutToExpire}"
          >
            <td>{{a.id}}</td>
            <td>{{a.name}}</td>
            <td>{{a.address}}</td>
            <td>{{a.contact}}</td>
            <td>{{a.collector}}</td>
            <td>{{a.collectionDate}}</td>
            <td style="overflow: visible">
              <div class="btn-group">
                <button
                  style="border-radius: 15px"
                  class="btn btn-round-sm btn-link dropdown-toggle pointer withoutripple"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul class="dropdown-menu">
                  <li>
                    <a ng-click="editBillsCollector(a)">Asignar Cobrador</a>
                  </li>
                  <li class="divider"></li>
                  <li>
                    <a ng-click="sendRefreshTokenReminder(a)"
                      >Solicitar MP Token</a
                    >
                  </li>
                  <li class="divider"></li>
                  <li>
                    <a ng-click="runClosure(a)">Ejecutar Cierre</a>
                  </li>
                </ul>
              </div>
              <button
                class="btn btn-xs btn-warning"
                ng-click="editAdministration(a)"
              >
                Editar
              </button>
              <button
                class="btn btn-xs btn-danger"
                ng-click="deleteAdministration(a)"
              >
                Borrar
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <toast message="toast"></toast>
</div>

<script type="text/ng-template" id="/administration-form.html">
  <div class="aside bs-docs-aside" tabindex="-1" role="dialog">
      <div class="close">
      <div class="btn btn-round btn-info" ng-click="$hide()"><i class="md md-close"></i></div>
      </div>

      <div class="aside-dialog">
      <div class="aside-body bs-sidebar" cg-busy="savingAdministration">

          <form class="form-floating" novalidate="novalidate" ng-submit="saveItem(item)">
          <fieldset>
              <legend><span ng-bind-html="item.icon"></span>Crear nueva Administracion</legend>

              <div class="bs-component" ng-if="errorMessage != null">
                  <div class="alert alert-dismissible alert-danger">
                      <button type="button" class="close" data-dismiss="alert" ng-click="clearError()">×</button>
                      <h4>Error</h4>
                      {{errorMessage}}
                  </div>
              </div>

              <div class="form-group filled">
              <label class="control-label">Nombre</label>
              <input type="text" class="form-control" ng-model="item.name" ng-disabled="!item.editing" required>
              </div>

              <div class="form-group filled">
              <label class="control-label">Dirección</label>
              <input type="text" class="form-control" ng-model="item.address" ng-disabled="!item.editing">
              </div>

              <div class="form-group filled">
              <label class="control-label">Contacto</label>
              <input type="text" class="form-control" ng-model="item.contact" ng-disabled="!item.editing">
              </div>

              <div class="form-group filled">
              <label class="control-label">Día de Cierre</label>
              <input type="number" class="form-control" min="0" max="31" ng-model="item.closureDay" ng-disabled="!item.editing">
              </div>

              <div class="form-group">
              <button type="submit" class="btn btn-primary" ng-hide="!item.editing">Guardar</button>
              </div>

          </fieldset>
          </form>

      </div>
      </div>
  </div>
</script>
