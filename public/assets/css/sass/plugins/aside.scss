.aside {
  margin-left: $sidenav-width !important;
  margin-top: 64px;
  z-index: 1031 !important;
  padding:0px;
  overflow: visible !important;
  @include box-shadow(0px 7px 7px 0 rgba(0, 0, 0, 0.36));
  border-top: 1px #E3E3E3 solid;
  height: 100%;

  .close {
    position: absolute;
    right: -25px;
    top: 28px;
    opacity: 1;
    z-index: 10;
  }

  &-dialog {
    position:relative;
    height: 100%;

    .aside-body {
      padding: 40px !important;
      overflow: scroll;
      height: 100%;

      @media (min-width: $grid-float-breakpoint + 1) {
        min-width:500px;
      }
    }
  }

  legend {
    margin-bottom:25px;
    border: none;
  }


  &-backdrop {
    z-index: 1020 !important;
  }

  @media (max-width: $screen-xs-max) {
    right: 0px !important;

    .close {
      right:25px;
    }
  }


  @media (max-width: $grid-float-breakpoint-max) {
    margin-left: 0 !important;
  }
}
