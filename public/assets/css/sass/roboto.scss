@mixin roboto-font($type, $weight, $style: normal) {
  @font-face {
    font-family: 'Roboto';
    src: url('#{$icon-font-path}/Roboto-#{$type}.eot');
    src: url('#{$icon-font-path}/Roboto-#{$type}.eot?#iefix') format('embedded-opentype'),
         url('#{$icon-font-path}/Roboto-#{$type}.woff2') format('woff2'),
         url('#{$icon-font-path}/Roboto-#{$type}.woff') format('woff'),
         url('#{$icon-font-path}/Roboto-#{$type}.ttf') format('truetype'),
         url('#{$icon-font-path}/Roboto-#{$type}.svg#Roboto') format('svg');
    font-weight: $weight;
    font-style: $style;
  }

  @font-face {
    font-family: 'Roboto-#{$type}';
    src: url('#{$icon-font-path}/Roboto-#{$type}.eot');
    src: url('#{$icon-font-path}/Roboto-#{$type}.eot?#iefix') format('embedded-opentype'),
         url('#{$icon-font-path}/Roboto-#{$type}.woff2') format('woff2'),
         url('#{$icon-font-path}/Roboto-#{$type}.woff') format('woff'),
         url('#{$icon-font-path}/Roboto-#{$type}.ttf') format('truetype'),
         url('#{$icon-font-path}/Roboto-#{$type}.svg#Roboto') format('svg');
  }
}

@mixin roboto-font-pair($type, $weight) {
  @include roboto-font($type, $weight);
  @include roboto-font(#{$type}Italic, $weight, italic);
}

@include roboto-font-pair('Thin', 100);
@include roboto-font-pair('Light', 300);
@include roboto-font-pair('Regular', 400);
@include roboto-font-pair('Medium', 500);
@include roboto-font-pair('Bold', 700);
@include roboto-font-pair('Black', 900);
