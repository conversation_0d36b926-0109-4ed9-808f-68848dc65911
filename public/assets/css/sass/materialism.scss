// Reusable
@import "functions";
@import "variables-colors";
@import "variables";


// Font
@import "public/bower_components/roboto-fontface/css/roboto-fontface.scss";
@import "public/bower_components/font-awesome/scss/font-awesome.scss";

// Bootstrap
@import "public/bower_components/bootstrap-sass/assets/stylesheets/bootstrap";
@import "bootstrap-overrides";

// materialism theme
@import "materialism/base";
@import "materialism/sidebar";
@import "materialism/buttons";
@import "materialism/panels";
@import "materialism/tables";
@import "materialism/cards";
@import "materialism/forms";
@import "materialism/navbar";
@import "materialism/switch";
@import "materialism/scrollbar";
@import "materialism/timeline";
@import "materialism/theme";
@import "materialism/zdepth";
@import "materialism/alert";

// Apps
@import "apps/todo";

// Directives
@import "directives/todo-widget";
@import "directives/c3";

// page specific
@import "pages/dashboard";
@import "pages/colors";
@import "pages/full-map";
@import "pages/grid";
@import "pages/icons";
@import "pages/tables";
@import "pages/vector-map";
@import "pages/weather-icons";

// Standalone pages
@import "pages/standalone/all";

// Plugins
@import "plugins/jquery.nouislider";
@import "plugins/aside";
@import "plugins/fsm-sticky-header";
@import "plugins/weather-icons";

// Animations
@import "animate-scss";
@import "animations/all";

// Themes & colors
@import "materialism/colors";
@import "materialism/themes";
@import "materialism/theme-picker";
