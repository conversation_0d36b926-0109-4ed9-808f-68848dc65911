// Material Admin

//
// Variables
// --------------------------------------------------


//== Colors
//
//## Gray and brand colors for use across Bootstrap.

$gray-base:              #000 !default;
$gray-darker:            lighten($gray-base, 13.5%) !default; // #222
$gray-dark:              #212121;
$gray:                   #666;
$gray-light:             #bbb;
$gray-lighter:           lighten($gray-base, 93.5%) !default; // #eee

// theme
$theme-primary-name: 'blue';
$theme-primary: color($theme-primary-name);
$theme-primary-text: auto-text-color($theme-primary);

$theme-secondary-name: 'blue';
$theme-secondary: color($theme-secondary-name);
$theme-secondary-text: auto-text-color($theme-secondary);

$brand-success-name:    'green';
$brand-info-name:       'blue';
$brand-warning-name:    'orange';
$brand-danger-name:     'red';

$brand-primary:         $theme-primary;
$brand-secondary:       $theme-secondary;

$brand-success:         color($brand-success-name);
$brand-info:            color($brand-info-name);
$brand-warning:         color($brand-warning-name, 'accent-2');
$brand-danger:          color($brand-danger-name);

//== Chart colors
$chart-text-color:        lighten($gray-base, 60%);
$chart-line-color:        $chart-text-color;
$chart-focus-color:       lighten($chart-text-color, 30%);
$chart-grid-color:        rgba(0,0,0,0.1);
$chart-grid-dotted:       0 0;


//== Scaffolding
//
//## Settings for some of the most global styles.

//** Global text color on `<body>`.
$text-color:            $gray;

//** Global textual link color.
$link-color:            $theme-secondary;
$link-hover-color:      color($theme-secondary-name, 'lighten-2');


//** Link hover decoration.
$link-hover-decoration: none;


//== Typography
//
//## Font, line-height, and color for body text, headings, and more.

$font-family-sans-serif:  "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;

$font-size-base:          13px;

//** Unit-less `line-height` for use in components like buttons.
$line-height-base:        1.846; // 20/14

//** By default, this inherits from the `<body>`.
$headings-font-weight:    400;
$headings-line-height:    1.1;
$headings-color:          #444;


//== Iconography
//
//## Specify custom location and filename of the included Glyphicons icon font. Useful for those including Bootstrap via Bower.

//** Load fonts from this directory.
$font-path:          "../fonts";
$icon-font-path:     $font-path;
$roboto-font-path:   $font-path;
$icon-font-path:     $font-path+'/';


//== Components
//
//## Define common padding and border radius sizes and more. Values based on 14px text and 1.428 line-height (~20px to start).

$padding-base-horizontal:   16px;

$padding-large-horizontal:  30px;

$border-radius-base:        3px;
$border-radius-large:       3px;

$component-active-bg:       $theme-primary;



//== Tables
//
//## Customizes the `.table` component with basic values, each used across all table variations.
$table-border-color:            #F0F0F0;
$table-row-highlight:           #F0E68C;

//== Buttons
//
//## For each of Bootstrap's buttons, define text, background and border color.

$btn-default-color:              $text-color;
$btn-default-border:             #eee;

$btn-primary-border:             transparent;
$btn-success-border:             transparent;
$btn-info-border:                transparent;
$btn-warning-border:             transparent;
$btn-danger-border:              transparent;


//== Forms
//
//##

//** `<input>` background color
$input-bg:                       transparent;
//** `<input disabled>` background color
$input-bg-disabled:              transparent;

//** `<input>` border color
$input-border:                   transparent;

//** Placeholder text color
$input-color-placeholder:        $gray-light;

//** Background color for textual input addons
$input-group-addon-bg:           transparent;


//== Dropdowns
//
//## Dropdown menu container and contents.

//** Dropdown link text color.
$dropdown-link-color:            $text-color;
//** Hover background for dropdown links.
$dropdown-link-hover-bg:         $gray-lighter;

//** Deprecated `$dropdown-caret-color` as of v3.1.0
$dropdown-caret-color:           $gray-light;


//== Media queries breakpoints
//
//## Define the breakpoints at which your layout will change, adapting to different screen sizes.

// Medium screen / desktop
//** Deprecated `$screen-md` as of v3.0.1
$screen-md:                  992px;
$screen-md-min:              $screen-md;

// Navbar collapse
//** Point at which the navbar becomes uncollapsed.
$grid-float-breakpoint:     $screen-md-min;
//** Point at which the navbar begins collapsing.
$grid-float-breakpoint-max: ($grid-float-breakpoint - 1);


//== Navbar
//
//##

// Basics of a navbar
$navbar-height:                    64px;
$navbar-margin-bottom:             0;

$navbar-default-color:             $gray-light;
$navbar-default-bg:                #fff;
$navbar-default-border:            transparent;

// Navbar links
$navbar-default-link-color:                $gray;
$navbar-default-link-hover-color:          $gray-dark;
$navbar-default-link-active-color:         $gray-dark;
$navbar-default-link-active-bg:            transparent;

// Navbar brand label
$navbar-default-brand-hover-color:         $navbar-default-link-hover-color;

// Navbar toggle
$navbar-default-toggle-hover-bg:           transparent;
$navbar-default-toggle-icon-bar-bg:        rgba(0,0,0,0.5);
$navbar-default-toggle-border-color:       transparent;



// Inverted navbar
// Reset inverted navbar basics
$navbar-inverse-color:                      $gray-light;
$navbar-inverse-bg:                         $brand-primary;
$navbar-inverse-border:                     transparent;

// Inverted navbar links
$navbar-inverse-link-color:                 lighten($brand-primary, 30%);

// Inverted navbar toggle
$navbar-inverse-toggle-hover-bg:            transparent;
$navbar-inverse-toggle-icon-bar-bg:         rgba(0,0,0,0.5);
$navbar-inverse-toggle-border-color:        transparent;

//== Navs
//
//##

//== Tabs
$nav-tabs-border-color:                     transparent;

$nav-tabs-active-link-hover-bg:             transparent;
$nav-tabs-active-link-hover-border-color:   transparent;

$nav-tabs-justified-link-border-color:            $nav-tabs-border-color;


//== Pagination
//
//##
$pagination-active-bg:       $theme-primary;
$pagination-active-border:   $theme-primary;
$pagination-bg:              #F9F9F9;

$pagination-border:          $pagination-bg;
$pagination-hover-border:    $pagination-bg;
$pagination-disabled-border: $pagination-bg;

//== Jumbotron
//
//##
$jumbotron-bg:                   #f9f9f9;
$jumbotron-heading-color:        $headings-color;


//== Form states and alerts
//
//## Define colors for form feedback states and, by default, alerts.

$state-success-text:             $brand-success;
$state-success-bg:               rgba(0,0,0,0);
$state-success-border:           rgba(0,0,0,0);

$state-info-text:                $brand-info;
$state-info-bg:                  rgba(0,0,0,0);
$state-info-border:           rgba(0,0,0,0);

$state-warning-text:             $brand-warning;
$state-warning-bg:               rgba(0,0,0,0);
$state-warning-border:           rgba(0,0,0,0);

$state-danger-text:              $brand-danger;
$state-danger-bg:                rgba(0,0,0,0);
$state-danger-border:           rgba(0,0,0,0);


//== Tooltips
//
//##

//** Tooltip background color
$tooltip-bg:                  #727272;


//== Popovers
//
//##

//** Popover border color
$popover-border-color:                rgba(0,0,0,.2);
$popover-fallback-border-color:       $popover-border-color;

//** Popover outer arrow color
$popover-arrow-outer-color:           fadein($popover-border-color, 7.5%);


//== Modals
//
//##

//** Modal content border color
$modal-content-border-color:                   transparent;

//** Modal header border color
$modal-header-border-color:   transparent;


//== Alerts
//
//## Define alert colors, border radius, and padding.


//== Progress bars
//
//##


//== List group
//
//##
$list-group-border:             #F0F0F0;

//== Panels
//
//##

$panel-border-radius:         2px;

$panel-footer-bg:             #fff;

$panel-default-text:          $text-color;
$panel-default-heading-bg:    #fff;

$panel-primary-heading-bg:    $brand-primary;

$panel-success-text:          #fff;
$panel-success-heading-bg:    $brand-success;

$panel-info-text:             #fff;
$panel-info-heading-bg:       $brand-info;

$panel-warning-text:          #fff;
$panel-warning-heading-bg:    $brand-warning;

$panel-danger-text:           #fff;
$panel-danger-heading-bg:     $brand-danger;


//== Wells
//
//##

$well-bg:                     #f0f0f0;
$well-border:                 transparent;


//== Badges
//
//##

$badge-font-weight:           normal;


//== Close
//
//##

$close-font-weight:           normal;
$close-text-shadow:           none;


//== Cards
//
//##
$card-padding: 20px;
$card-bg-color: #fff;


//== sidenav
//
//##
$sidenav-width: 260px;