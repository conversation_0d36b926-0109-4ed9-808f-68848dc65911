// color classes
@each $color_name, $color in $colors {
  @each $color_type, $color_value in $color {

    @if $color_type == "base" {
      .#{$color_name} {
        background-color: $color_value;
        color: auto-text-color($color_value);
      }
      .#{$color_name}-text {
        color: $color_value;
      }
      .#{$color_name}-border {
        border-color: $color_value;
      }
    }
    @else {
      .#{$color_name}.#{$color_type} {
        background-color: $color_value;
        color: auto-text-color($color_value);
      }
      .#{$color_name}-text.#{$color_type} {
        color: $color_value;
      }
      .#{$color_name}-border.#{$color_type} {
        border-color: $color_value;
      }
    }
  }
}

// Shade classes
@each $color, $color_value in $shades {
  .#{$color} {
    background-color: $color_value !important;
    color: auto-text-color($color_value);
  }
  .#{$color}-text {
    color: $color_value;
  }
}