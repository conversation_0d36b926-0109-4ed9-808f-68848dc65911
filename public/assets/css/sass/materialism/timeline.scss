.timeline {
  list-style: none;
  padding: 20px 0 20px;
  position: relative;

  &:before {
    top: 10px;
    bottom: 0;
    position: absolute;
    content: " ";
    width: 1px;
    background-color: rgba(0, 0, 0, 0.03);
    left: 50%;
    margin-left: -1.5px;
  }
  > li {
    margin-bottom: 20px;
    position: relative;
    &:before {
      content: " ";
      display: table;
    }
    &:after {
      content: " ";
      display: table;
      clear: both;
    }
    &:before {
      content: " ";
      display: table;
    }
    &:after {
      content: " ";
      display: table;
      clear: both;
    }
    > {
      .timeline-panel {
        width: 46%;
        float: left;
        position: relative;

        &:after {
          position: absolute;
          top: 27px;
          right: -14px;
          display: inline-block;
          content: " ";
        }
      }
      .timeline-badge {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 1.4em;
        text-align: center;
        position: absolute;
        top: 16px;
        left: 50%;
        margin-left: -25px;
        z-index: 100;
        background: #EEE;
      }
    }
    &.timeline-inverted > .timeline-panel {
      float: right;
      &:before {
        border-left-width: 0;
        border-right-width: 15px;
        left: -15px;
        right: auto;
      }
      &:after {
        border-left-width: 0;
        border-right-width: 14px;
        left: -14px;
        right: auto;
      }
    }
  }
}

.timeline-title {
  font-size: 14px;
  margin: 0px;
  font-weight: 500;
}

.timeline-body {
  line-height: 17px;
  margin-top: 5px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.5);
  p {
    margin-bottom:5px;
  }
}


ul.timeline {
  &:before {
    left: 10px;
  }
  > li > {
    .timeline-panel {
      width: calc(100% - 40px);
    }
    .timeline-badge {
      left: -8px;
      margin-left: 0;
      top: -10px;
      text-align: center;
    }
    .timeline-panel {
      float: right;
      &:before {
        border-left-width: 0;
        border-right-width: 15px;
        left: -15px;
        right: auto;
      }
      &:after {
        border-left-width: 0;
        border-right-width: 14px;
        left: -14px;
        right: auto;
      }
    }
  }
}