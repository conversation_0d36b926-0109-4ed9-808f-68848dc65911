$dark-text-default: #000 !default;
$light-text-default: #fff !default;

// Calculeate brightness of a given color.
@function brightness($color) {
  @return ((red($color) * .9) + (green($color) * .9) + (blue($color) * .1)) / 255 * 100%;
}

// Compares contrast of a given color to the light/dark arguments and returns whichever is most "contrasty"
@function color-contrast($color, $dark: $dark-text-default, $light: $light-text-default) {
  @if $color == null {
    @return null;
  }
  @else {
    $color-brightness: brightness($color);
    $light-text-brightness: brightness($light);
    $dark-text-brightness: brightness($dark);

    @return if(abs($color-brightness - $light-text-brightness) > abs($color-brightness - $dark-text-brightness), $light, $dark);
  }
}


@function auto-text-color($color) {
  @if (lightness($color) > 77) {
    @return #666; // Lighter background, return dark color
  } @else {
    @return #ffffff; // Darker background, return light color
  }
}

@function text-lightness($color) {
  @if (lightness($color) > 77) {
    @return 0;
  } @else {
    @return 1;
  }
}

@function auto-background-color($color) {
  @if (lightness($color) > 50) {
    @return darken($color, 20%);; // Darker background, return light color
  } @else {
    @return lighten($color, 25%); // Lighter background, return dark color
  }
}


// keyframes mixin
@mixin keyframes($name) {
  @-webkit-keyframes #{$name} {
    @content;
  }
  @-moz-keyframes #{$name} {
    @content;
  }
  @-ms-keyframes #{$name} {
    @content;
  }
  @keyframes #{$name} {
    @content;
  }
}
