
// Links
a {
  @extend .theme-secondary-text;

  &:hover,
  &:focus {
    @extend .theme-secondary-text-lighten-3;
  }
}


// Page header
.page-header {
  margin: 0 0 40px 0;
  padding: 0px;
  border: none;

  h1 {
    margin-top: 0px;
    margin-bottom: 5px;
    font-size: 20px;
    @extend .theme-text;

    i {
      @extend .grey-text;
      @extend .lighten-2;
    }
  }

  .lead {
    margin: 0px;
  }

  .actions {
    @extend .pull-right;
    margin-right: -10px;
    margin-top: -10px;
  }
}

// Navbar =====================================================================
.navbar {
  border: none;
  @include box-shadow(0 0 1px rgba(0, 0, 0, 0.3));

  &-default {
    border-radius:inherit;
  }

  &.scroll {
    @include box-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
    z-index: 1028;
  }

  &-brand {
    font-size: 16px;
    color: #939393;
  }

  &-inverse {
    .form-control {
      color: #fff;
      @include placeholder($navbar-inverse-link-color);

      &[type=text] {
        @include box-shadow(inset 0 -1px 0 $navbar-inverse-link-color);

        &:focus {
          @include box-shadow(inset 0 -2px 0 #fff);
        }
      }
    }
  }

  &-nav > li > .dropdown-menu {
    margin-top: 0px;
  }

  &-fixed-top {
    margin-left: 0 !important;
    @media (min-width: $grid-float-breakpoint + 1) {
      margin-left: $sidenav-width !important;
    }
  }
}

.navbar-toggle {
  @media (min-width: $grid-float-breakpoint) {
    display: block;
  }
  @media (min-width: $grid-float-breakpoint + 1) {
    display: none;
  }
}

// Tables =====================================================================

@mixin table-row-variant($state, $background) {
  // Exact selectors below required to override `.table-striped` and prevent
  // inheritance to nested tables.
  .table > thead > tr,
  .table > tbody > tr,
  .table > tfoot > tr {
    > td.#{$state},
    > th.#{$state},
    &.#{$state} > td,
    &.#{$state} > th {
      background-color: $background;
      color: auto-text-color($background);
    }
  }

  // Hover states for `.table-hover`
  // Note: this is not available for cells or rows within `thead` or `tfoot`.
  .table-hover > tbody > tr {
    > td.#{$state}:hover,
    > th.#{$state}:hover,
    &.#{$state}:hover > td,
    &:hover > .#{$state},
    &.#{$state}:hover > th {
      background-color: darken($background, 5%);
    }
  }
}

@include table-row-variant('active', $table-bg-active);
@include table-row-variant('success', $state-success-bg);
@include table-row-variant('info', $state-info-bg);
@include table-row-variant('warning', $state-warning-bg);
@include table-row-variant('danger', $state-danger-bg);

.table-bordered {
  border: none; // :D
}

.table-striped {
  > tbody > tr:nth-of-type(even) {
    background-color: $table-bg-accent;
  }
  > tbody > tr:nth-of-type(odd) {
    background-color: transparent;
  }
}

// Forms ======================================================================

.form-group {
  position:relative;
}

label {
  font-weight: normal;
}

textarea {
  min-height: 150px;
}

textarea,
textarea.form-control,
input.form-control,
input[type=text],
input[type=password],
input[type=email],
input[type=number],
[type=text].form-control,
[type=password].form-control,
[type=email].form-control,
[type=tel].form-control,
[contenteditable].form-control {
  padding: 0;
  border: none;
  border-radius: 0;
  -webkit-appearance: none;
  @include box-shadow(inset 0 -1px 0 #ddd);
  font-size: 16px;

  &:focus {
    @extend .theme-box-inset;
  }

  &[disabled],
  &[readonly] {
    @include box-shadow(none);
    border-bottom: 1px dotted #ddd;
  }

  &.input {
    &-sm {
      font-size: $font-size-small;
    }

    &-lg {
      font-size: $font-size-large;
    }
  }
}

select,
select.form-control {
  border: 0;
  border-radius: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding-left: 0;
  padding-right: 0\9; // remove padding for < ie9 since default arrow can't be removed
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=);
  background-size: 13px;
  background-repeat: no-repeat;
  background-position: right center;
  @include box-shadow(inset 0 -1px 0 #ddd);
  font-size: 16px;
  line-height: 1.5;

  &::-ms-expand {
    display: none;
  }

  &.input {
    &-sm {
      font-size: $font-size-small;
    }

    &-lg {
      font-size: $font-size-large;
    }
  }

  &:focus {
    @extend .theme-box-inset;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEUhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISEhISF8S9ewAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=);
  }

  &[multiple] {
    background: none;
  }
}

.radio,
.radio-inline,
.checkbox,
.checkbox-inline {
  label {
    padding-left: 33px;
    margin-bottom: 5px;
    line-height: 30px;
    cursor: pointer;
  }
}


.radio-inline, .checkbox-inline {
  padding: 0 20px 0 0;
}

input[type="radio"],
.radio input[type="radio"],
.radio-inline input[type="radio"] {
  margin-top: 5px;
  margin-left: -33px;
  border: none;
  background-color: transparent;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;

  &:focus {
    outline: none;
  }

  &:before,
  &:after {
    content: "";
    display: block;
    width: 25px;
    height: 25px;
    margin-top: -5px;
    border-radius: 50%;
    @include transition(240ms);
  }

  &:before {
    position: absolute;
    left: 0;
    top: 0;
    @include scale(0);
    @extend .theme-bg;
  }

  &:after {
    border: 2px solid $gray;
  }

  &:checked:before {
    @include scale(0.5);
  }

  &:disabled:checked:before {
    background-color: $gray-light;
  }

  &:checked:after {
    @extend .theme-border;
  }

  &:disabled:after,
  &:disabled:checked:after {
    border-color: $gray-light;
  }
}


label input[type="checkbox"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  margin-left: -33px;
}

label input[type="checkbox"] {
  position:inherit;
}

input[type="checkbox"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  border: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;

  &:focus {
    outline: none;
  }

  &:after {
    content: "";
    display: block;
    width: 25px;
    height: 25px;
    margin-top: -2px;
    margin-right: 5px;
    border: 2px solid $gray;
    border-radius: 2px;
    @include transition(240ms);
  }

  &:checked:before {
    content: "";
    position: absolute;
    top: 3px;
    left: 10px;
    display: table;
    width: 6px;
    height: 12px;
    border: 2px solid #fff;
    border-top-width: 0;
    border-left-width: 0;
    @include rotate(45deg);
  }

  &:checked:after {

    @extend .theme-bg;
    @extend .theme-border;
  }

  &:disabled:after {
    border-color: $gray-light;
  }

  &:disabled:checked:after {
    background-color: $gray-light;
    border-color: transparent;
  }
}
.checkbox-inline {
  padding: 0 10px;
  label {
    margin-bottom:0px;
  }
}

.has-warning {
  input:not([type=checkbox]),
  .form-control {
    @include box-shadow(inset 0 -1px 0 $brand-warning);
  }
  input:not([type=checkbox]):focus,
  .form-control:focus {
    @include box-shadow(inset 0 -2px 0 $brand-warning);
  }
}

.has-error {
  input:not([type=checkbox]),
  .form-control {
    @include box-shadow(inset 0 -1px 0 $brand-danger);
  }

  input:not([type=checkbox]):focus,
  .form-control:focus {
    @include box-shadow(inset 0 -2px 0 $brand-danger);
  }

  .checkbox {
    padding-bottom: 30px;
    .error-msg {
      position: absolute;
      top: 25px;
      left: 0px;
    }
  }
}

.has-success {
  input:not([type=checkbox]),
  .form-control {
    @include box-shadow(inset 0 -1px 0 $brand-success);
  }
  input:not([type=checkbox]):focus,
  .form-control:focus {
    @include box-shadow(inset 0 -2px 0 $brand-success);
  }
}


.input-group-addon {
  border: none !important;
  padding: $padding-base-vertical $padding-base-horizontal+4 $padding-base-vertical $padding-base-horizontal;

  i {
    font-size:22px;
  }
}

.input-group {
  .form-control {
    /*margin-left: -$padding-base-vertical;*/
  }
}




// Navs =======================================================================

.nav-tabs {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  > li > a,
  > li > a:focus {
    margin-right: 0;
    background-color: transparent;
    border: none;
    color: $navbar-default-link-color;
    @include box-shadow(inset 0 -1px 0 #ddd);
    @include transition(all 0.2s);

    &:hover {
      background-color: transparent;
      @extend .theme-box-inset;
      @extend .theme-text;
    }
  }

  & > li.active > a,
  & > li.active > a:focus {
    border: none;
    @extend .theme-box-inset;
    @extend .theme-text;

    &:hover {
      border: none;
      @extend .theme-text;
    }
  }

  & > li.disabled > a {
    @include box-shadow(inset 0 -1px 0 #ddd);
  }

  &.nav-justified {

    & > li > a,
    & > li > a:hover,
    & > .active > a,
    & > .active > a:hover {
      border: none !important;
    }

  }

  .dropdown-menu {
    margin-top: 0;
  }
}

.dropdown-menu {
  border: none;
  border-radius: 0px;
  padding: 0px;
  margin-top: -40px !important;
  @include box-shadow(0 1px 4px rgba(0,0,0,.3));

  > li > a {
    font-size: 15px;
    padding: 5px 15px;
    position: relative;
  }

  .divider {
    margin:0px;
  }

  > div {
    opacity: 0;
    @include transition(all 0.4s);
    @include transition-delay(0.1s);
  }
}

.open .dropdown-menu > div {
  opacity: 1;
}

// Override inline style
.dropdown-menu-right {
  left: auto !important;
}

// Indicators =================================================================

.alert {
  border: none;
  color: #fff;

  &-success {
    background-color: $brand-success;
  }

  &-info {
    background-color: $brand-info;
  }

  &-warning {
    background-color: $brand-warning;
  }

  &-danger {
    background-color: $brand-danger;
  }

  a:not(.close),
  .alert-link {
    color: #fff;
    font-weight: bold;
  }

  .close {
    color: #fff;
  }
}


.progress {
  position: relative;
  z-index: 1;
  height: 2px;
  border-radius: 0;

  @include box-shadow(none);

  &-bar {
    @include box-shadow(none);

    &:last-child {
      border-radius: 0;
    }

    &:last-child {
      &:before {
        display: block;
        content: 'div::before';
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        right: 0;
        z-index: -1;
        background-color: lighten($progress-bar-bg, 35%);
      }
    }

    &-success:last-child.progress-bar:before {
      background-color: lighten($brand-success, 35%);
    }

    &-info:last-child.progress-bar:before {
      background-color: lighten($brand-info, 45%);
    }
    &-warning:last-child.progress-bar:before {
      background-color: lighten($brand-warning, 35%);
    }

    &-danger:last-child.progress-bar:before {
      background-color: lighten($brand-danger, 25%);
    }
  }
}

// badges and labels ==============================================================

.badge {
  padding: 0px 10px;
  font-size: 9px;
  line-height: 22px;
}


// breadcrums ==============================================================

.breadcrumb {
  padding: 8px 15px;
  margin: 12px 0px;
  list-style: none;
  background-color: inherit;
  border-radius: 3px;
  white-space: nowrap;
}



// Progress bars ==============================================================



// list group =================================================================

.list-group {
  margin-bottom: 0px;

  &-item {
    padding: 15px;
  }

  &-item-text {
    color: $gray-light;
  }
}




// Containers =================================================================

.close {
  font-size: 34px;
  font-weight: 300;
  line-height: 24px;
  opacity: 0.6;

  &:hover {
    opacity: 1;
  }
}


.well {
  border-radius: 0;
  @extend .z-depth-0;
}

.panel {
  border: none;
  @extend .z-depth-0;

  &-title {
    line-height: 20px;
  }

  &-heading {
    border-bottom: none;
  }

  &-footer {
    border-top: none;
  }
}

.popover {
  border: none;
  @include box-shadow(0 1px 4px rgba(0,0,0,.3));

  .popover-title {
    white-space: nowrap;
  }
}
