/*
Version: 3.1 Timestamp: Tue Aug 14 09:05:17 PDT 2012
*/
.select2-container {
    width:100%;

  display:block;
  border: 0;
  border-radius: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding-left: 0;
  padding-right: 0;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=);
  background-size: 13px;
  background-repeat: no-repeat;
  background-position: right 7px;
  @include box-shadow(inset 0 -1px 0 #DDD);
  font-size: 16px;
  line-height: 1.5;
  padding: 5px 0px;

  cursor:pointer;
}


.ui-select-choices-row {
  padding: 10px 15px;
}

.select2-container,
.select2-drop,
.select2-search,
.select2-search input{
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.

    More Info : http://www.quirksmode.org/css/box.html
  */
  -moz-box-sizing: border-box;    /* firefox */
  -ms-box-sizing: border-box;     /* ie */
  -webkit-box-sizing: border-box; /* webkit */
  -khtml-box-sizing: border-box;  /* konqueror */
  box-sizing: border-box;         /* css3 */
}

.select2-container .select2-choice {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    height: 26px;
    line-height: 26px;
    color: #444;
    text-decoration: none;
}

.select2-container.select2-drop-above .select2-choice
{

}

.select2-container .select2-choice span {
    margin-right: 26px;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    -o-text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    text-overflow: ellipsis;
}

.select2-container .select2-choice abbr {
  display: block;
  position: absolute;
  right: 26px;
  top: 8px;
  width: 12px;
  height: 12px;
  font-size: 1px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) right top no-repeat;
  cursor: pointer;
  text-decoration: none;
  border:0;
  outline: 0;
}
.select2-container .select2-choice abbr:hover {
  background-position: right -11px;
  cursor: pointer;
}

.select2-drop {
    background: #fff;
    color: #000;
    border-top: 0;
    position: absolute;
    top: 100%;

    @include box-shadow(0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15));

    z-index: 99999;
    width:100%;
    margin-top: -30px;
}
.ui-select-multiple .select2-drop {
  margin-top:0px;
}

.select2-drop.select2-drop-above {
    -webkit-border-radius: 4px 4px 0px 0px;
    -moz-border-radius: 4px 4px 0px 0px;
    border-radius: 4px 4px 0px 0px;
    margin-top:1px;
    border-top: 1px solid #aaa;
    border-bottom: 0;

    @include box-shadow(0 -4px 5px rgba(0, 0, 0, .15));
}

.select2-container .select2-choice div {

}

.select2-container .select2-choice div b {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 0 1px;
    display: block;
    width: 100%;
    height: 100%;
}

.select2-search {
  display: inline-block;
    white-space: nowrap;
    z-index: 10000;
  min-height: 26px;
  width: 100%;
  margin: 0;
  padding-left: 4px;
  padding-right: 4px;
}

.select2-search-hidden {
  display: block;
  position: absolute;
  left: -10000px;
}

.select2-search input {
    background: #fff url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, -o-linear-gradient(bottom, white 85%, #eeeeee 99%);
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, -ms-linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAMAAACelLz8AAAAJ1BMVEVmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmaP/QSjAAAADHRSTlMAAgMJC0uWpKa6wMxMdjkoAAAANUlEQVR4AeXJyQEAERAAsNl7Hf3X6xt0QL6JpZWq30pdvdadme+0PMdzvHm8YThHcT1H7K0BtOMDniZhWOgAAAAASUVORK5CYII=) no-repeat 100% -22px, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    padding: 4px 20px 4px 5px;
    outline: 0;
    border: 1px solid #aaa;
    font-family: sans-serif;
    font-size: 1em;
    width:100%;
    margin:0;
    height:auto !important;
    min-height: 26px;
    @include box-shadow(none);
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

.select2-drop.select2-drop-above .select2-search input
{
    margin-top:4px;
}

.select2-search input.select2-active {
    background: #fff url('spinner.gif') no-repeat 100%;
    background: url('spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -o-linear-gradient(bottom, white 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, -ms-linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    background: url('spinner.gif') no-repeat 100%, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
}


.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
    outline: none;
}



.select2-dropdown-open .select2-choice div {
  background: transparent;
  border-left: none;
}
.select2-dropdown-open .select2-choice div b {
  background-position: -18px 1px;
}

/* results */
.select2-results {
  margin:0px;
  padding:0px;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 250px;
}

.select2-results ul.select2-result-sub {
  margin: 0 0 0 0;
}

.select2-results ul.select2-result-sub > li .select2-result-label { padding-left: 20px }
.select2-results ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 40px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 60px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 80px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 100px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 110px }
.select2-results ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub ul.select2-result-sub > li .select2-result-label { padding-left: 120px }

.select2-results li {
  list-style: none;
  display: list-item;
}
.select2-results li:hover {
  /*background-color:rgba(0,0,0,0.05);*/
}

.select2-results li.select2-result-with-children > .select2-result-label {
  font-weight: bold;
}

.select2-results .select2-result-label {
  margin: 0;
  cursor: pointer;
}

.select2-results .select2-highlighted {
  background: #2196F3;
  color: #fff;
}
.select2-results li em {
  background: #feffde;
  font-style: normal;
}
.select2-results .select2-highlighted em {
  background: transparent;
}
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  background: #f4f4f4;
  display: list-item;
}

/*
disabled look for already selected choices in the results dropdown
.select2-results .select2-disabled.select2-highlighted {
    color: #666;
    background: #f4f4f4;
    display: list-item;
    cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
*/
.select2-results .select2-disabled {
    display: none;
}

.select2-more-results.select2-active {
    background: #f4f4f4 url('spinner.gif') no-repeat 100%;
}

.select2-more-results {
  background: #f4f4f4;
  display: list-item;
}

/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container.select2-container-disabled .select2-choice div {
    background-color: #f4f4f4;
    background-image: none;
    border-left: 0;
}


/* multiselect */

.select2-container-multi .select2-choices {
    margin: 0;
    padding: 0;
    cursor: text;
    overflow: hidden;
    height: auto !important;
    height: 1%;
    position: relative;
}

.select2-container-multi .select2-choices {
    min-height: 26px;
}

.select2-container-multi.select2-container-active .select2-choices {
    outline: none;
}
.select2-container-multi .select2-choices li {
  float: left;
  list-style: none;
}
.select2-container-multi .select2-choices .select2-search-field {
  white-space: nowrap;
  margin: 0;
  padding: 0;
}

.select2-container-multi .select2-choices .select2-search-field input {
  color: #666;
  background: transparent !important;
  font-family: sans-serif;
  font-size: 100%;
  padding: 5px;
  margin: 1px 0;
  outline: 0;
  border: 0;
  @include box-shadow(none);
}

.select2-container-multi .select2-choices .select2-search-field input.select2-active {
    background: #fff url('spinner.gif') no-repeat 100% !important;
}

.select2-default {
  color: #999 !important;
}

.select2-container-multi .select2-choices .select2-search-choice {
  -webkit-border-radius: 3px;
  -moz-border-radius   : 3px;
  border-radius        : 3px;
  -moz-background-clip   : padding;
  -webkit-background-clip: padding-box;
  background-clip        : padding-box;
  background-color: #F9F9F9;
  line-height: 13px;
  padding: 10px 20px 10px 35px;
  margin: 0 0 5px 5px;
  position: relative;
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice span {
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #d4d4d4;
}

.select2-search-choice-close {
  display: block;
  position: absolute;
  right: 3px;
  top: 4px;
  outline: none;
  font: normal normal normal 25px/1 'Material Design Iconic Font';
}
.select2-search-choice-close:after {
  content: "\f29a";
}

.select2-container-multi .select2-search-choice-close {
  left: 3px;
}


.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
  background-position: right -11px;
}

/* disabled styles */

.select2-container-multi.select2-container-disabled .select2-choices{
    background-color: #f4f4f4;
    background-image: none;
    border: 1px solid #ddd;
    cursor: default;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
    background-image: none;
    background-color: #f4f4f4;
    border: 1px solid #ddd;
    padding: 3px 5px 3px 5px;
}

.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
    display: none;
}
/* end multiselect */

.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-result-selectable .select2-match { text-decoration: underline; }
.select2-result-unselectable .select2-match { text-decoration: none; }

.select2-offscreen { position: absolute; left: -10000px; }


.select2-display-none {
  display: none;
}
