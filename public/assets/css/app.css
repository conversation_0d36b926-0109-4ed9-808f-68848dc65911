:root { }

details.collapsible-item summary {
  border-bottom: 1px solid ;
  font-weight: bold;
  width: 100%;
}

details.collapsible-item summary span:before {
  content: '\25b6 ';
  display: inline-block;
  margin-right: 0.5rem;
  position: relative;

  transition: transform .3s ease-in-out;
}

details.collapsible-item[open] summary span:before {
  transform: rotate(90deg);
}

details.collapsible-item .response {
  max-height: 25vh;
  overflow: scroll;
  width: 100%;
  white-space: pre-wrap;
}

.dev-utilities .card .card-content .item--lg p {
  font-size: 2rem;
}
.dev-utilities .card .card-content .item--lg .md,
.dev-utilities .card .card-content .item--md .md,
.dev-utilities .card .card-content .item--lg .wi,
.dev-utilities .card .card-content .item--md .wi {
  font-size: 5rem;
}
.dev-utilities .card .card-content .item--md p {
  font-size: 1.5rem;
}

/* Toast */
#lm-toast {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  position: fixed;
  z-index: 1;
  left: 50%;
  bottom: 30px;
}
#lm-toast.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {bottom: 0; opacity: 0;}
  to {bottom: 30px; opacity: 1;}
}

@keyframes fadein {
  from {bottom: 0; opacity: 0;}
  to {bottom: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
  from {bottom: 30px; opacity: 1;}
  to {bottom: 0; opacity: 0;}
}

@keyframes fadeout {
  from {bottom: 30px; opacity: 1;}
  to {bottom: 0; opacity: 0;}
}
