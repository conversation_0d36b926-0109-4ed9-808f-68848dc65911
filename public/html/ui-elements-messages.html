<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Materialism Angular Admin Theme" />
    <meta name="author" content="Theme Guys - The Netherlands" />
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="assets/img/favicon/favicon.ico" />
    <title>Messages &amp; Notifications - Materialism</title>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body
    scroll-spy=""
    id="top"
    class="theme-template-dark theme-pink alert-open alert-with-mat-grow-top-right"
  >
    <main>
      <aside class="sidebar fixed" style="width: 260px; left: 0px">
        <div class="brand-logo">
          <div id="logo">
            <div class="foot1"></div>
            <div class="foot2"></div>
            <div class="foot3"></div>
            <div class="foot4"></div>
          </div>
          Materialism
        </div>
        <div class="user-logged-in">
          <div class="content">
            <div class="user-name">
              Katsumoto <span class="text-muted f9">admin</span>
            </div>
            <div class="user-email"><EMAIL></div>
            <div class="user-actions">
              <a class="m-r-5" href="#">settings</a> <a href="#">logout</a>
            </div>
          </div>
        </div>
        <ul class="menu-links">
          <li icon="md md-blur-on">
            <a href="index.html"
              ><i class="md md-blur-on"></i>&nbsp;<span>Dashboard</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#APPS"
              aria-expanded="false"
              aria-controls="APPS"
              class="collapsible-header waves-effect"
              ><i class="md md-camera"></i>&nbsp;APPS</a
            >
            <ul id="APPS" class="collapse">
              <li name="Todo">
                <a href="apps-todo.html">
                  <span id="todosCount" class="pull-right badge z-depth-0"
                    >2</span
                  ><span> Todo </span></a
                >
              </li>
              <li name="Crud">
                <a href="apps-crud.html">
                  <span class="pull-right badge theme-primary-bg z-depth-0"
                    >9</span
                  ><span> Crud </span></a
                >
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#UIelements"
              aria-expanded="false"
              aria-controls="UIelements"
              class="collapsible-header waves-effect active"
              ><i class="md md-photo"></i>&nbsp;UI elements</a
            >
            <ul id="UIelements" class="collapse in">
              <li>
                <a href="ui-elements-cards.html"><span>Cards</span></a>
              </li>
              <li>
                <a href="ui-elements-colors.html"><span>Color</span></a>
              </li>
              <li>
                <a href="ui-elements-grid.html"><span>Grid</span></a>
              </li>
              <li>
                <a href="ui-elements-icons.html"
                  ><span>Icons material design</span></a
                >
              </li>
              <li>
                <a href="ui-elements-weather-icons.html"
                  ><span>Icons weather</span></a
                >
              </li>
              <li>
                <a href="ui-elements-lists.html"><span>Lists</span></a>
              </li>
              <li>
                <a href="ui-elements-typography.html"
                  ><span>Typography</span></a
                >
              </li>
              <li>
                <a href="ui-elements-messages.html" class="active"
                  ><span>Messages &amp; Notifications</span></a
                >
              </li>
              <li>
                <a href="ui-elements-buttons.html"><span>Buttons</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Forms"
              aria-expanded="false"
              aria-controls="Forms"
              class="collapsible-header waves-effect"
              ><i class="md md-input"></i>&nbsp;Forms</a
            >
            <ul id="Forms" class="collapse">
              <li>
                <a href="forms-basic.html"><span>Basic forms</span></a>
              </li>
              <li>
                <a href="forms-advanced.html"><span>Advanced elements</span></a>
              </li>
              <li>
                <a href="forms-validation.html"><span>Validation</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Tables"
              aria-expanded="false"
              aria-controls="Tables"
              class="collapsible-header waves-effect"
              ><i class="md md-list"></i>&nbsp;Tables</a
            >
            <ul id="Tables" class="collapse">
              <li>
                <a href="tables-basic.html"><span>Basic tables</span></a>
              </li>
              <li>
                <a href="tables-data.html"><span>Data tables</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Maps"
              aria-expanded="false"
              aria-controls="Maps"
              class="collapsible-header waves-effect"
              ><i class="md md-place"></i>&nbsp;Maps</a
            >
            <ul id="Maps" class="collapse">
              <li>
                <a href="maps-full-map.html"><span>Full map</span></a>
              </li>
              <li>
                <a href="maps-map-widgets.html"><span>Map widgets</span></a>
              </li>
              <li>
                <a href="maps-vector-map.html"><span>Vector map</span></a>
              </li>
            </ul>
          </li>
          <li icon="md md-insert-chart">
            <a href="charts.html"
              ><i class="md md-insert-chart"></i>&nbsp;<span>Charts</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Extrapages"
              aria-expanded="false"
              aria-controls="Extrapages"
              class="collapsible-header waves-effect"
              ><i class="md md-favorite-outline"></i>&nbsp;Extra pages</a
            >
            <ul id="Extrapages" class="collapse">
              <a target="_blank" href="pages-login.html">Login</a>
              <a target="_blank" href="pages-404.html">404</a>
              <a target="_blank" href="pages-500.html">500</a>
              <a target="_blank" href="pages-material-bird.html">Easter Egg</a>
            </ul>
          </li>
        </ul>
      </aside>
      <div class="main-container">
        <nav class="navbar navbar-default navbar-fixed-top">
          <div class="container-fluid">
            <div class="navbar-header pull-left">
              <button
                type="button"
                class="navbar-toggle pull-left m-15"
                data-activates=".sidebar"
              >
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span> <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              <ul class="breadcrumb">
                <li><a href="#/">Materialism</a></li>
                <li class="active">Messages &amp; Notifications</li>
              </ul>
            </div>
            <ul class="nav navbar-nav navbar-right navbar-right-no-collapse">
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/dropdown-navbar.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="dropdownListExample"
                >
                  <li role="presentation" class="dropdown-header">
                    <i class="md md-desktop-mac"></i> Welcome
                  </li>
                  <li role="presentation">
                    <a role="menuitem" href="#"
                      ><i class="md md-help"></i> Show introduction</a
                    >
                  </li>
                  <div class="p-10">
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">55</div>
                      </div>
                      New customers
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-info"
                        style="width: 60%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">34</div>
                      </div>
                      Messages
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-danger"
                        style="width: 70%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">21</div>
                      </div>
                      Revenue
                    </div>
                    <div class="progress m-b-5">
                      <div
                        class="progress-bar progress-bar-warning"
                        style="width: 40%"
                      ></div>
                    </div>
                  </div>
                </ul>
              </li>
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/theme-picker.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-settings f20"></i>
                </button>
                <div
                  class="dropdown-menu dropdown-menu-right theme-picker mat-grow-top-right"
                >
                  <div class="container-fluid m-v-15">
                    <div class="pull-right m-r-10">
                      <button type="button" class="close" onclick="$hide()">
                        ×
                      </button>
                    </div>
                    <h4 class="no-margin p-t-5">
                      <i class="md md-filter"></i> Theming options
                    </h4>
                    <div class="row m-t-20">
                      <div class="col-md-6">
                        <div class="w300">Template themes</div>
                        <div
                          class="theme-item theme-template-default"
                          onclick="changeTemplateTheme('theme-template-default');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-dark"
                          onclick="changeTemplateTheme('theme-template-dark');"
                        >
                          <div class="icon"><i class="md md-check"></i></div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-light"
                          onclick="changeTemplateTheme('theme-template-light');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-green"
                          onclick="changeTemplateTheme('theme-template-green');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-blue"
                          onclick="changeTemplateTheme('theme-template-blue');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="w300">Color themes</div>
                        <div class="row gutter-10">
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-pink"
                              onclick="changeColorTheme('theme-pink');"
                            >
                              <div class="icon">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-red"
                              onclick="changeColorTheme('theme-red');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-purple"
                              onclick="changeColorTheme('theme-purple');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-indigo"
                              onclick="changeColorTheme('theme-indigo');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-blue"
                              onclick="changeColorTheme('theme-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-blue"
                              onclick="changeColorTheme('theme-light-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-cyan"
                              onclick="changeColorTheme('theme-cyan');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-teal"
                              onclick="changeColorTheme('theme-teal');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-green"
                              onclick="changeColorTheme('theme-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-green"
                              onclick="changeColorTheme('theme-light-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-lime"
                              onclick="changeColorTheme('theme-lime');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-yellow"
                              onclick="changeColorTheme('theme-yellow');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-amber"
                              onclick="changeColorTheme('theme-amber');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-orange"
                              onclick="changeColorTheme('theme-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-deep-orange"
                              onclick="changeColorTheme('theme-deep-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li navbar-search="" class="pull-right">
                <div>
                  <div class="mat-slide-right pull-right">
                    <form class="search-form form-inline pull-left">
                      <div class="form-group">
                        <label class="sr-only" for="search-input">Search</label>
                        <input
                          type="text"
                          class="form-control"
                          id="search-input"
                          placeholder="Search"
                          autofocus=""
                        />
                      </div>
                    </form>
                  </div>
                  <div class="pull-right">
                    <button class="btn btn-sm btn-link pull-left withoutripple">
                      <i class="md md-search f20"></i>
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <div
          class="main-content"
          autoscroll="true"
          bs-affix-target=""
          init-ripples=""
        >
          <section class="messages">
            <div class="page-header">
              <h1><i class="md md-photo"></i> Messages &amp; Notifications</h1>
              <p class="lead">
                Messages and notifications are used to get something clear to
                the user that needs attention or just validation.
              </p>
            </div>
            <div class="row">
              <div class="col-lg-12">
                <div class="bs-component">
                  <div class="alert alert-dismissible alert-warning">
                    <button type="button" class="close" data-dismiss="alert">
                      ×
                    </button>
                    <h4>Warning!</h4>
                    <p>
                      Best check yo self, you're not looking too good. Nulla
                      vitae elit libero, a pharetra augue. Praesent commodo
                      cursus magna,
                      <a href="#" class="alert-link"
                        >vel scelerisque nisl consectetur et</a
                      >.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-4">
                <div class="bs-component">
                  <div class="alert alert-dismissible alert-danger">
                    <button type="button" class="close" data-dismiss="alert">
                      ×
                    </button>
                    <h4>Danger!</h4>
                    <strong>Oh snap!</strong>
                    <a href="#" class="alert-link">Change a few things up</a>
                    and try submitting again.
                  </div>
                </div>
              </div>
              <div class="col-lg-4">
                <div class="bs-component">
                  <div class="alert alert-dismissible alert-success">
                    <button type="button" class="close" data-dismiss="alert">
                      ×
                    </button>
                    <h4>Success!</h4>
                    <strong>Well done!</strong> You successfully read
                    <a href="#" class="alert-link"
                      >this important alert message</a
                    >.
                  </div>
                </div>
              </div>
              <div class="col-lg-4">
                <div class="bs-component">
                  <div class="alert alert-dismissible alert-info">
                    <button type="button" class="close" data-dismiss="alert">
                      ×
                    </button>
                    <h4>Info!</h4>
                    <strong>Heads up!</strong> This
                    <a href="#" class="alert-link">alert needs your attention</a
                    >, but it's not super important.
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-4">
                <div class="card bordered">
                  <div class="card-header alert alert-danger">
                    <span class="card-title">Danger</span>
                  </div>
                  <div class="card-content">
                    <p>
                      I am a very simple card. I am good at containing small
                      bits of information. I am convenient because I require
                      little markup to use effectively.
                    </p>
                  </div>
                  <div class="card-action clearfix">
                    <div class="pull-right">
                      <a href="#" class="btn btn-link black-text">Dismiss</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-4">
                <div class="card bordered">
                  <div class="card-header alert alert-success">
                    <span class="card-title">Success</span>
                  </div>
                  <div class="card-content">
                    <p>
                      I am a very simple card. I am good at containing small
                      bits of information. I am convenient because I require
                      little markup to use effectively.
                    </p>
                  </div>
                  <div class="card-action clearfix">
                    <div class="pull-right">
                      <a href="#" class="btn btn-link black-text">Dismiss</a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-lg-4">
                <div class="card bordered">
                  <div class="card-header alert alert-info">
                    <span class="card-title">Info</span>
                  </div>
                  <div class="card-content">
                    <p>
                      I am a very simple card. I am good at containing small
                      bits of information. I am convenient because I require
                      little markup to use effectively.
                    </p>
                  </div>
                  <div class="card-action clearfix">
                    <div class="pull-right">
                      <a href="#" class="btn btn-link black-text">Dismiss</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row m-b-40">
              <div class="col-lg-4">
                <h5>Modals</h5>
                <button
                  type="button"
                  class="btn btn-primary btn-lg"
                  data-template="assets/tpl/partials/modal.html"
                  bs-modal="modal"
                  data-toggle="dropdown"
                >
                  Launch demo modal
                  <br />
                  <small class="f11">(using data-template)</small>
                </button>
                <div class="modal" tabindex="-1" role="dialog">
                  <div class="modal-dialog">
                    <div class="modal-content">
                      <div class="modal-header">
                        <button type="button" class="close">×</button>
                        <h4 class="modal-title">Test modal</h4>
                      </div>
                      <div class="modal-body">
                        <h4>Text in a modal</h4>
                        <p ng-bind-html="content"></p>
                        <pre>2 + 3 = {{ 2 + 3 }}</pre>
                        <h4>Popover in a modal</h4>
                        <p>
                          This
                          <button
                            role="button"
                            class="btn btn-default popover-test"
                            data-title="A Title"
                            data-content="And here's some amazing content. It's very engaging. right?"
                            data-toggle="popover"
                          >
                            button
                          </button>
                          should trigger a popover on click.
                        </p>
                        <h4>Tooltips in a modal</h4>
                        <p>
                          <a
                            href="#"
                            class="tooltip-test"
                            data-title="Tooltip"
                            data-toggle="tooltip"
                            >This link</a
                          >
                          and
                          <a
                            href="#"
                            class="tooltip-test"
                            data-title="Tooltip"
                            data-toggle="tooltip"
                            >that link</a
                          >
                          should have tooltips on hover.
                        </p>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-default">
                          Close
                        </button>
                        <button type="button" class="btn btn-primary">
                          Save changes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6 col-lg-4">
                <h5>Popovers</h5>
                <div class="btn-group" role="group" aria-label="popovers">
                  <button
                    type="button"
                    data-placement="left"
                    title="On the Left!"
                    data-content="You found me!"
                    class="btn btn-default"
                    data-toggle="popover"
                  >
                    Left
                  </button>
                  <button
                    type="button"
                    data-placement="top"
                    title="On the Top!"
                    data-content="You found me!"
                    class="btn btn-default"
                    data-toggle="popover"
                  >
                    Top
                  </button>
                  <button
                    type="button"
                    data-placement="bottom"
                    title="On the Bottom!"
                    data-content="You found me!"
                    class="btn btn-default"
                    data-toggle="popover"
                  >
                    Bottom
                  </button>
                  <button
                    type="button"
                    data-placement="right"
                    title="On the Right!"
                    data-content="You found me!"
                    class="btn btn-default"
                    data-toggle="popover"
                  >
                    Right
                  </button>
                </div>
              </div>
              <div class="col-md-6 col-lg-4">
                <h5>Tooltips</h5>
                <div class="btn-group" role="group" aria-label="tooltips">
                  <button
                    type="button"
                    class="btn btn-default"
                    data-placement="left"
                    data-title="Tooltip on left"
                    data-toggle="tooltip"
                  >
                    Left
                  </button>
                  <button
                    type="button"
                    class="btn btn-default"
                    data-placement="top"
                    data-title="Tooltip on top"
                    data-toggle="tooltip"
                  >
                    Top
                  </button>
                  <button
                    type="button"
                    class="btn btn-default"
                    data-placement="bottom"
                    data-title="Tooltip on bottom"
                    data-toggle="tooltip"
                  >
                    Bottom
                  </button>
                  <button
                    type="button"
                    class="btn btn-default"
                    data-placement="right"
                    data-title="Tooltip on right"
                    data-toggle="tooltip"
                  >
                    Right
                  </button>
                </div>
              </div>
            </div>
            <div class="row m-b-40">
              <div class="col-md-4">
                <div class="card">
                  <div class="card-content">
                    <div class="card-title">Default labels</div>
                    <span class="label label-default">Default</span>
                    <span class="label label-success">Success</span>
                    <span class="label label-warning">Warning</span>
                    <span class="label label-danger">Danger</span>
                    <span class="label label-info">Info</span>
                    <hr />
                    <h5 class="no-margin">Default badges</h5>
                    <span class="badge">12</span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-content">
                    <div class="card-title">Color labels</div>
                    <span> <span class="label pink">pink label</span> </span
                    ><span> <span class="label red">red label</span> </span
                    ><span>
                      <span class="label purple">purple label</span> </span
                    ><span>
                      <span class="label indigo">indigo label</span> </span
                    ><span> <span class="label blue">blue label</span> </span
                    ><span>
                      <span class="label light-blue"
                        >light-blue label</span
                      > </span
                    ><span> <span class="label cyan">cyan label</span> </span
                    ><span> <span class="label teal">teal label</span> </span
                    ><span> <span class="label green">green label</span> </span
                    ><span>
                      <span class="label light-green"
                        >light-green label</span
                      > </span
                    ><span> <span class="label lime">lime label</span> </span
                    ><span>
                      <span class="label yellow">yellow label</span> </span
                    ><span> <span class="label amber">amber label</span> </span
                    ><span>
                      <span class="label orange">orange label</span> </span
                    ><span>
                      <span class="label deep-orange">deep-orange label</span>
                    </span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="card">
                  <div class="card-content">
                    <div class="card-title">Color badges</div>
                    <h5 class="no-margin"></h5>
                    <span> <span class="badge pink">pink badge</span> </span
                    ><span> <span class="badge red">red badge</span> </span
                    ><span>
                      <span class="badge purple">purple badge</span> </span
                    ><span>
                      <span class="badge indigo">indigo badge</span> </span
                    ><span> <span class="badge blue">blue badge</span> </span
                    ><span>
                      <span class="badge light-blue"
                        >light-blue badge</span
                      > </span
                    ><span> <span class="badge cyan">cyan badge</span> </span
                    ><span> <span class="badge teal">teal badge</span> </span
                    ><span> <span class="badge green">green badge</span> </span
                    ><span>
                      <span class="badge light-green"
                        >light-green badge</span
                      > </span
                    ><span> <span class="badge lime">lime badge</span> </span
                    ><span>
                      <span class="badge yellow">yellow badge</span> </span
                    ><span> <span class="badge amber">amber badge</span> </span
                    ><span>
                      <span class="badge orange">orange badge</span> </span
                    ><span>
                      <span class="badge deep-orange">deep-orange badge</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-4">
                <h3>Progess <span class="w300">bars</span></h3>
                <p>Progress bars from bootstrap come in 5 different colors.</p>
                <br />
                <div class="p-v-10">
                  <div class="progress">
                    <div class="progress-bar" style="width: 80%"></div>
                  </div>
                  <div class="progress">
                    <div
                      class="progress-bar progress-bar-info"
                      style="width: 60%"
                    ></div>
                  </div>
                  <div class="progress">
                    <div
                      class="progress-bar progress-bar-warning"
                      style="width: 50%"
                    ></div>
                  </div>
                  <div class="progress">
                    <div
                      class="progress-bar progress-bar-danger"
                      style="width: 40%"
                    ></div>
                  </div>
                  <div class="progress">
                    <div
                      class="progress-bar progress-bar-success"
                      style="width: 30%"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <h3>Misc <span class="w300">bars</span></h3>
                <div class="p-v-10">
                  <p>
                    You can stack different colors as part of a total progress
                    bar.
                  </p>
                  <br />
                  <div class="progress">
                    <div class="progress-bar" style="width: 20%"></div>
                    <div
                      class="progress-bar progress-bar-success"
                      style="width: 35%"
                    ></div>
                    <div
                      class="progress-bar progress-bar-warning"
                      style="width: 20%"
                    ></div>
                    <div
                      class="progress-bar progress-bar-danger"
                      style="width: 10%"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <h3>Animated <span class="w300">bars</span></h3>
                <div class="p-v-10">
                  <p>
                    Progress bars can be animated by adding a simple active
                    class.
                  </p>
                  <br />
                  <div class="progress progress-striped active">
                    <div class="progress-bar" style="width: 80%"></div>
                  </div>
                  <div class="progress progress-striped active">
                    <div
                      class="progress-bar progress-bar-info"
                      style="width: 60%"
                    ></div>
                  </div>
                  <div class="progress progress-striped active">
                    <div
                      class="progress-bar progress-bar-warning"
                      style="width: 50%"
                    ></div>
                  </div>
                  <div class="progress progress-striped active">
                    <div
                      class="progress-bar progress-bar-danger"
                      style="width: 40%"
                    ></div>
                  </div>
                  <div class="progress progress-striped active">
                    <div
                      class="progress-bar progress-bar-success"
                      style="width: 30%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
