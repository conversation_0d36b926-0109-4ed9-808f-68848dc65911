<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Materialism Angular Admin Theme" />
    <meta name="author" content="Theme Guys - The Netherlands" />
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="assets/img/favicon/favicon.ico" />
    <title>Basic forms - Materialism</title>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body
    scroll-spy=""
    id="top"
    class="theme-template-dark theme-pink alert-open alert-with-mat-grow-top-right"
  >
    <main>
      <aside class="sidebar fixed" style="width: 260px; left: 0px">
        <div class="brand-logo">
          <div id="logo">
            <div class="foot1"></div>
            <div class="foot2"></div>
            <div class="foot3"></div>
            <div class="foot4"></div>
          </div>
          Materialism
        </div>
        <div class="user-logged-in">
          <div class="content">
            <div class="user-name">
              Katsumoto <span class="text-muted f9">admin</span>
            </div>
            <div class="user-email"><EMAIL></div>
            <div class="user-actions">
              <a class="m-r-5" href="#">settings</a> <a href="#">logout</a>
            </div>
          </div>
        </div>
        <ul class="menu-links">
          <li icon="md md-blur-on">
            <a href="index.html"
              ><i class="md md-blur-on"></i>&nbsp;<span>Dashboard</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#APPS"
              aria-expanded="false"
              aria-controls="APPS"
              class="collapsible-header waves-effect"
              ><i class="md md-camera"></i>&nbsp;APPS</a
            >
            <ul id="APPS" class="collapse">
              <li name="Todo">
                <a href="apps-todo.html">
                  <span id="todosCount" class="pull-right badge z-depth-0"
                    >2</span
                  ><span> Todo </span></a
                >
              </li>
              <li name="Crud">
                <a href="apps-crud.html">
                  <span class="pull-right badge theme-primary-bg z-depth-0"
                    >9</span
                  ><span> Crud </span></a
                >
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#UIelements"
              aria-expanded="false"
              aria-controls="UIelements"
              class="collapsible-header waves-effect"
              ><i class="md md-photo"></i>&nbsp;UI elements</a
            >
            <ul id="UIelements" class="collapse">
              <li>
                <a href="ui-elements-cards.html"><span>Cards</span></a>
              </li>
              <li>
                <a href="ui-elements-colors.html"><span>Color</span></a>
              </li>
              <li>
                <a href="ui-elements-grid.html"><span>Grid</span></a>
              </li>
              <li>
                <a href="ui-elements-icons.html"
                  ><span>Icons material design</span></a
                >
              </li>
              <li>
                <a href="ui-elements-weather-icons.html"
                  ><span>Icons weather</span></a
                >
              </li>
              <li>
                <a href="ui-elements-lists.html"><span>Lists</span></a>
              </li>
              <li>
                <a href="ui-elements-typography.html"
                  ><span>Typography</span></a
                >
              </li>
              <li>
                <a href="ui-elements-messages.html"
                  ><span>Messages &amp; Notifications</span></a
                >
              </li>
              <li>
                <a href="ui-elements-buttons.html"><span>Buttons</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Forms"
              aria-expanded="false"
              aria-controls="Forms"
              class="collapsible-header waves-effect active"
              ><i class="md md-input"></i>&nbsp;Forms</a
            >
            <ul id="Forms" class="collapse in">
              <li>
                <a href="forms-basic.html" class="active"
                  ><span>Basic forms</span></a
                >
              </li>
              <li>
                <a href="forms-advanced.html"><span>Advanced elements</span></a>
              </li>
              <li>
                <a href="forms-validation.html"><span>Validation</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Tables"
              aria-expanded="false"
              aria-controls="Tables"
              class="collapsible-header waves-effect"
              ><i class="md md-list"></i>&nbsp;Tables</a
            >
            <ul id="Tables" class="collapse">
              <li>
                <a href="tables-basic.html"><span>Basic tables</span></a>
              </li>
              <li>
                <a href="tables-data.html"><span>Data tables</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Maps"
              aria-expanded="false"
              aria-controls="Maps"
              class="collapsible-header waves-effect"
              ><i class="md md-place"></i>&nbsp;Maps</a
            >
            <ul id="Maps" class="collapse">
              <li>
                <a href="maps-full-map.html"><span>Full map</span></a>
              </li>
              <li>
                <a href="maps-map-widgets.html"><span>Map widgets</span></a>
              </li>
              <li>
                <a href="maps-vector-map.html"><span>Vector map</span></a>
              </li>
            </ul>
          </li>
          <li icon="md md-insert-chart">
            <a href="charts.html"
              ><i class="md md-insert-chart"></i>&nbsp;<span>Charts</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Extrapages"
              aria-expanded="false"
              aria-controls="Extrapages"
              class="collapsible-header waves-effect"
              ><i class="md md-favorite-outline"></i>&nbsp;Extra pages</a
            >
            <ul id="Extrapages" class="collapse">
              <a target="_blank" href="pages-login.html">Login</a>
              <a target="_blank" href="pages-404.html">404</a>
              <a target="_blank" href="pages-500.html">500</a>
              <a target="_blank" href="pages-material-bird.html">Easter Egg</a>
            </ul>
          </li>
        </ul>
      </aside>
      <div class="main-container">
        <nav class="navbar navbar-default navbar-fixed-top">
          <div class="container-fluid">
            <div class="navbar-header pull-left">
              <button
                type="button"
                class="navbar-toggle pull-left m-15"
                data-activates=".sidebar"
              >
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span> <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              <ul class="breadcrumb">
                <li><a href="#/">Materialism</a></li>
                <li class="active">Basic forms</li>
              </ul>
            </div>
            <ul class="nav navbar-nav navbar-right navbar-right-no-collapse">
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/dropdown-navbar.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="dropdownListExample"
                >
                  <li role="presentation" class="dropdown-header">
                    <i class="md md-desktop-mac"></i> Welcome
                  </li>
                  <li role="presentation">
                    <a role="menuitem" href="#"
                      ><i class="md md-help"></i> Show introduction</a
                    >
                  </li>
                  <div class="p-10">
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">55</div>
                      </div>
                      New customers
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-info"
                        style="width: 60%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">34</div>
                      </div>
                      Messages
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-danger"
                        style="width: 70%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">21</div>
                      </div>
                      Revenue
                    </div>
                    <div class="progress m-b-5">
                      <div
                        class="progress-bar progress-bar-warning"
                        style="width: 40%"
                      ></div>
                    </div>
                  </div>
                </ul>
              </li>
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/theme-picker.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-settings f20"></i>
                </button>
                <div
                  class="dropdown-menu dropdown-menu-right theme-picker mat-grow-top-right"
                >
                  <div class="container-fluid m-v-15">
                    <div class="pull-right m-r-10">
                      <button type="button" class="close" onclick="$hide()">
                        ×
                      </button>
                    </div>
                    <h4 class="no-margin p-t-5">
                      <i class="md md-filter"></i> Theming options
                    </h4>
                    <div class="row m-t-20">
                      <div class="col-md-6">
                        <div class="w300">Template themes</div>
                        <div
                          class="theme-item theme-template-default"
                          onclick="changeTemplateTheme('theme-template-default');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-dark"
                          onclick="changeTemplateTheme('theme-template-dark');"
                        >
                          <div class="icon"><i class="md md-check"></i></div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-light"
                          onclick="changeTemplateTheme('theme-template-light');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-green"
                          onclick="changeTemplateTheme('theme-template-green');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-blue"
                          onclick="changeTemplateTheme('theme-template-blue');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="w300">Color themes</div>
                        <div class="row gutter-10">
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-pink"
                              onclick="changeColorTheme('theme-pink');"
                            >
                              <div class="icon">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-red"
                              onclick="changeColorTheme('theme-red');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-purple"
                              onclick="changeColorTheme('theme-purple');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-indigo"
                              onclick="changeColorTheme('theme-indigo');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-blue"
                              onclick="changeColorTheme('theme-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-blue"
                              onclick="changeColorTheme('theme-light-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-cyan"
                              onclick="changeColorTheme('theme-cyan');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-teal"
                              onclick="changeColorTheme('theme-teal');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-green"
                              onclick="changeColorTheme('theme-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-green"
                              onclick="changeColorTheme('theme-light-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-lime"
                              onclick="changeColorTheme('theme-lime');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-yellow"
                              onclick="changeColorTheme('theme-yellow');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-amber"
                              onclick="changeColorTheme('theme-amber');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-orange"
                              onclick="changeColorTheme('theme-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-deep-orange"
                              onclick="changeColorTheme('theme-deep-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li navbar-search="" class="pull-right">
                <div>
                  <div class="mat-slide-right pull-right">
                    <form class="search-form form-inline pull-left">
                      <div class="form-group">
                        <label class="sr-only" for="search-input">Search</label>
                        <input
                          type="text"
                          class="form-control"
                          id="search-input"
                          placeholder="Search"
                          autofocus=""
                        />
                      </div>
                    </form>
                  </div>
                  <div class="pull-right">
                    <button class="btn btn-sm btn-link pull-left withoutripple">
                      <i class="md md-search f20"></i>
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <div
          class="main-content"
          autoscroll="true"
          bs-affix-target=""
          init-ripples=""
        >
          <section class="forms-basic">
            <div class="page-header">
              <h1><i class="md md-input"></i> Basic Forms</h1>
              <p class="lead">
                An overview of basic form styles in containers and elements.
              </p>
            </div>
            <div class="row m-b-40">
              <div class="col-md-3 col-md-push-9">
                <h5>Floating material design form</h5>
                <p>
                  Form styling is possible with various bootstrap containers
                  like wells and panels. In this example it's a white well.
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form-floating">
                    <fieldset>
                      <legend>Floating label form</legend>
                      <span class="help-block"
                        >Please fill out the following form below.</span
                      >
                      <div class="form-group">
                        <label for="inputEmail" class="control-label"
                          >Email</label
                        >
                        <input type="text" class="form-control" />
                      </div>
                      <div class="form-group">
                        <label for="inputPassword" class="control-label"
                          >Password</label
                        >
                        <input
                          type="password"
                          class="form-control"
                          id="inputPassword"
                        />
                      </div>
                      <div class="form-group">
                        <div class="checkbox">
                          <label>
                            <input type="checkbox" /> Are you a winner?
                          </label>
                        </div>
                      </div>
                      <div class="form-group">
                        <label for="textArea" class="control-label"
                          >Textarea</label
                        >
                        <textarea
                          class="form-control vertical"
                          rows="3"
                          id="textArea"
                        ></textarea>
                        <span class="help-block"
                          >A longer block of help text that breaks onto a new
                          line and may extend beyond one line.</span
                        >
                      </div>
                      <div class="form-group">
                        <label class="control-label normal"
                          >Radio buttons</label
                        >
                        <div class="radio">
                          <label>
                            <input
                              type="radio"
                              name="optionsRadios"
                              id="optionsRadios1"
                              value="option1"
                              checked=""
                            />
                            Option one is this
                          </label>
                        </div>
                        <div class="radio">
                          <label>
                            <input
                              type="radio"
                              name="optionsRadios"
                              id="optionsRadios2"
                              value="option2"
                            />
                            Option two can be something else
                          </label>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="control-label normal">Switches</label>
                        <div class="switch">
                          <label>
                            Off <input type="checkbox" />
                            <span class="lever"></span> On
                          </label>
                        </div>
                      </div>
                      <div class="form-group">
                        <select class="select2 form-control"></select>
                      </div>
                      <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                          Submit
                        </button>
                        <button type="reset" class="btn btn-default">
                          Cancel
                        </button>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40">
              <div class="col-md-3 col-md-push-9">
                <h5>Floating material design form prefilled</h5>
                <p>
                  Form styling is possible with various bootstrap containers
                  like wells and panels. In this example it's a default well
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well">
                  <form class="form-horizontal placeholder-form">
                    <fieldset>
                      <legend>Horizontal form</legend>
                      <span class="help-block m-b-20"
                        >Please fill out the following form below.</span
                      >
                      <div class="form-group">
                        <label for="inputEmail" class="col-lg-2 control-label"
                          >Email</label
                        >
                        <div class="col-lg-10 filled">
                          <input
                            type="text"
                            class="form-control"
                            id="inputEmail"
                            placeholder="Email"
                            value="Massa enim nec dui nunc mattis enim ut tellus elementum sagittis."
                          />
                        </div>
                      </div>
                      <div class="form-group">
                        <label
                          for="inputPassword"
                          class="col-lg-2 control-label"
                          >Password</label
                        >
                        <div class="col-lg-10 filled">
                          <input
                            type="password"
                            class="form-control"
                            id="inputPassword"
                            placeholder="Password"
                            value="nakama?"
                          />
                        </div>
                      </div>
                      <div class="form-group">
                        <label
                          for="inputPassword"
                          class="col-lg-2 control-label"
                          >Checkbox</label
                        >
                        <div class="col-lg-10">
                          <div class="checkbox">
                            <label class="filled">
                              <input type="checkbox" checked="checked" /> Are
                              you a winner?
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label for="textArea" class="col-lg-2 control-label"
                          >Textarea</label
                        >
                        <div class="col-lg-10 filled">
                          <textarea
                            class="form-control vertical"
                            rows="3"
                            id="textArea"
                            placeholder="Why not?"
                          >
Risus nullam eget felis eget nunc lobortis mattis aliquam faucibus. Faucibus pulvinar elementum integer enim neque, volutpat ac tincidunt vitae, semper quis lectus nulla at. Sodales ut eu sem integer vitae justo eget magna fermentum iaculis eu non diam. Tellus elementum sagittis vitae et leo duis ut diam quam nulla.</textarea
                          >
                          <span class="help-block"
                            >A longer block of help text that breaks onto a new
                            line and may extend beyond one line.</span
                          >
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="col-lg-2 control-label">Radios</label>
                        <div class="col-lg-10">
                          <div class="radio">
                            <label class="filled">
                              <input
                                type="radio"
                                name="optionsRadios"
                                id="optionsRadios1"
                                value="option1"
                                checked=""
                              />
                              Option one is this
                            </label>
                          </div>
                          <div class="radio">
                            <label class="filled">
                              <input
                                type="radio"
                                name="optionsRadios"
                                id="optionsRadios2"
                                value="option2"
                              />
                              Option two can be something else
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="col-lg-2 control-label">Switches</label>
                        <div class="col-lg-10">
                          <div class="switch">
                            <label class="filled">
                              Off <input type="checkbox" checked="checked" />
                              <span class="lever"></span> On
                            </label>
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="col-lg-2 control-label">Select</label>
                        <div class="col-lg-10">
                          <select class="select2 form-control"></select>
                        </div>
                      </div>
                      <div class="form-group">
                        <div class="col-lg-10 col-lg-offset-2">
                          <button type="reset" class="btn btn-default">
                            Cancel
                          </button>
                          <button type="submit" class="btn btn-primary">
                            Submit
                          </button>
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="m-b-20">
              <h5>Misc forms and form layouts</h5>
              <p>Here are more variations and elements you can use in forms</p>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="well white">
                  <form class="form-floating">
                    <fieldset>
                      <legend>Form sizes and controls</legend>
                      <span class="help-block mw400"
                        >When using validation on your form you want the user to
                        get a visual representation of what actions are
                        needed.</span
                      >
                      <div class="form-group">
                        <label class="control-label" for="inputSmall"
                          >Small input</label
                        >
                        <input
                          class="form-control input-sm"
                          type="text"
                          id="inputSmall"
                        />
                      </div>
                      <div class="form-group">
                        <input
                          class="form-control"
                          id="focusedInput"
                          type="text"
                        />
                        <label class="control-label" for="focusedInput"
                          >Default input</label
                        >
                      </div>
                      <div class="form-group">
                        <label class="control-label" for="inputSmall"
                          >Small large</label
                        >
                        <input
                          class="form-control input-lg"
                          type="text"
                          id="inputSmall"
                        />
                      </div>
                      <div class="form-group filled">
                        <label class="control-label" for="focusedInput"
                          >Focused input</label
                        >
                        <input
                          class="form-control"
                          id="focusedInput"
                          type="text"
                          value="This is focused... *removed for demo, just add autofocus as attribute*"
                        />
                      </div>
                      <div class="form-group">
                        <label class="control-label">Disabled input</label>
                        <input class="form-control" type="text" disabled="" />
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
              <div class="col-md-6">
                <div class="well white">
                  <form class="form-floating">
                    <fieldset>
                      <legend>Input colors</legend>
                      <span class="help-block mw400"
                        >When using validation on your form you want the user to
                        get a visual representation of what actions are
                        needed.</span
                      >
                      <div class="form-group">
                        <label class="control-label" for="inputWarning"
                          >Input normal</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="inputWarning"
                        />
                      </div>
                      <div class="form-group has-warning">
                        <label class="control-label" for="inputWarning"
                          >Input warning</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="inputWarning"
                        />
                      </div>
                      <div class="form-group has-error">
                        <label class="control-label" for="inputError"
                          >Input error</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="inputError"
                        />
                      </div>
                      <div class="form-group has-success">
                        <label class="control-label" for="inputSuccess"
                          >Input success</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="inputSuccess"
                        />
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="well white">
                  <form class="form-floating">
                    <fieldset>
                      <legend>Form input addons</legend>
                      <span class="help-block"
                        >Due to some layout spacing you need to add padding to
                        an element.</span
                      >
                      <div class="row p-b-15">
                        <div class="col-lg-6">
                          <div class="input-group">
                            <div class="input-group-btn p-r-10">
                              <button
                                type="button"
                                class="btn btn-default dropdown-toggle"
                                data-toggle="dropdown"
                                aria-expanded="false"
                              >
                                Action <span class="caret"></span>
                              </button>
                              <ul class="dropdown-menu" role="menu">
                                <li><a href="#">Action</a></li>
                                <li><a href="#">Another action</a></li>
                                <li><a href="#">Something else here</a></li>
                                <li class="divider"></li>
                                <li><a href="#">Separated link</a></li>
                              </ul>
                            </div>
                            <!-- /btn-group -->
                            <input
                              type="text"
                              class="form-control"
                              aria-label="..."
                            />
                          </div>
                          <!-- /input-group -->
                        </div>
                        <!-- /.col-lg-6 -->
                        <div class="col-lg-6">
                          <div class="input-group">
                            <input
                              type="text"
                              class="form-control"
                              aria-label="..."
                            />
                            <div class="input-group-btn p-l-10">
                              <button
                                type="button"
                                class="btn btn-default dropdown-toggle"
                                data-toggle="dropdown"
                                aria-expanded="false"
                              >
                                Action <span class="caret"></span>
                              </button>
                              <ul
                                class="dropdown-menu dropdown-menu-right"
                                role="menu"
                              >
                                <li><a href="#">Action</a></li>
                                <li><a href="#">Another action</a></li>
                                <li><a href="#">Something else here</a></li>
                                <li class="divider"></li>
                                <li><a href="#">Separated link</a></li>
                              </ul>
                            </div>
                            <!-- /btn-group -->
                          </div>
                          <!-- /input-group -->
                        </div>
                        <!-- /.col-lg-6 -->
                      </div>
                      <!-- /.row -->
                      <div class="form-group">
                        <label class="control-label normal"
                          >Input addon label</label
                        >
                        <div class="input-group">
                          <span class="input-group-addon"
                            ><i class="md md-apps"></i
                          ></span>
                          <input type="text" class="form-control" />
                          <span class="input-group-btn p-l-10">
                            <button class="btn btn-default" type="button">
                              Send
                            </button>
                          </span>
                        </div>
                      </div>
                      <div class="form-group">
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="exampleInputAmount"
                            placeholder="Question"
                          />
                          <div class="input-group-addon">
                            <i class="md md-question-answer"></i>
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="sr-only" for="exampleInputAmount"
                          >Amount (in dollars)</label
                        >
                        <div class="input-group">
                          <div class="input-group-addon">$</div>
                          <input
                            type="text"
                            class="form-control"
                            id="exampleInputAmount"
                            placeholder="Amount"
                          />
                          <div class="input-group-addon">.00</div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="sr-only" for="exampleInputAmount"
                          >Amount (in dollars)</label
                        >
                        <div class="input-group">
                          <input
                            type="text"
                            class="form-control"
                            id="exampleInputAmount"
                            placeholder="My name"
                          />
                          <div class="input-group-addon">@mydomain.com</div>
                        </div>
                      </div>
                      <div class="form-group">
                        <div class="input-group">
                          <span class="input-group-addon" id="basic-addon1"
                            >@</span
                          >
                          <input
                            type="text"
                            class="form-control"
                            placeholder="Username"
                            aria-describedby="basic-addon1"
                          />
                        </div>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
              <div class="col-md-6">
                <div class="well white">
                  <form class="form-inline">
                    <fieldset>
                      <legend>Inline form</legend>
                      <br />
                      <div class="form-group">
                        <label class="sr-only" for="exampleInputEmail3"
                          >Email address</label
                        >
                        <input
                          type="email"
                          class="form-control"
                          id="exampleInputEmail3"
                          placeholder="Enter email"
                        />
                      </div>
                      <div class="checkbox-inline">
                        <label>
                          <input type="checkbox" /> Are you a winner?
                        </label>
                      </div>
                      <button type="submit" class="btn btn-primary">
                        Sign in <i class="md md-send"></i>
                      </button>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
