<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="" />
    <meta name="author" content="" />
    <title>Materialism</title>
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="../assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="../assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="../assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="../assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="../assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="../assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="../assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="../assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="../assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="../assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="../assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="../assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="../assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="../assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="../assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="../assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="../assets/img/favicon/favicon.ico" />
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="//oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="//oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body class="page-login" init-ripples="">
    <div class="center">
      <div
        class="card bordered z-depth-2"
        style="margin: 0% auto; max-width: 400px"
      >
        <div class="card-header">
          <div class="brand-logo">
            <div id="logo">
              <div class="foot1"></div>
              <div class="foot2"></div>
              <div class="foot3"></div>
              <div class="foot4"></div>
            </div>
            Materialism
          </div>
        </div>
        <div class="card-content">
          <div class="m-b-30">
            <div class="card-title strong pink-text">Login</div>
            <p class="card-title-desc">
              Welcome to Materialism! The admin template for material design
              lovers.
            </p>
          </div>
          <form class="form-floating">
            <div class="form-group">
              <label for="inputEmail" class="control-label">Email</label>
              <input type="text" class="form-control" />
            </div>
            <div class="form-group">
              <label for="inputPassword" class="control-label">Password</label>
              <input type="password" class="form-control" id="inputPassword" />
            </div>
            <div class="form-group">
              <div class="checkbox">
                <label> <input type="checkbox" /> Remember me </label>
              </div>
            </div>
          </form>
        </div>
        <div class="card-action clearfix">
          <div class="pull-right">
            <button type="button" class="btn btn-link black-text">
              Forgot password
            </button>
            <button type="button" class="btn btn-link black-text">Login</button>
          </div>
        </div>
      </div>
    </div>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
