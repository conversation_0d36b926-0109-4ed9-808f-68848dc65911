<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Materialism Angular Admin Theme" />
    <meta name="author" content="Theme Guys - The Netherlands" />
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="assets/img/favicon/favicon.ico" />
    <title>Typography - Materialism</title>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body
    scroll-spy=""
    id="top"
    class="theme-template-dark theme-pink alert-open alert-with-mat-grow-top-right"
  >
    <main>
      <aside class="sidebar fixed" style="width: 260px; left: 0px">
        <div class="brand-logo">
          <div id="logo">
            <div class="foot1"></div>
            <div class="foot2"></div>
            <div class="foot3"></div>
            <div class="foot4"></div>
          </div>
          Materialism
        </div>
        <div class="user-logged-in">
          <div class="content">
            <div class="user-name">
              Katsumoto <span class="text-muted f9">admin</span>
            </div>
            <div class="user-email"><EMAIL></div>
            <div class="user-actions">
              <a class="m-r-5" href="#">settings</a> <a href="#">logout</a>
            </div>
          </div>
        </div>
        <ul class="menu-links">
          <li icon="md md-blur-on">
            <a href="index.html"
              ><i class="md md-blur-on"></i>&nbsp;<span>Dashboard</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#APPS"
              aria-expanded="false"
              aria-controls="APPS"
              class="collapsible-header waves-effect"
              ><i class="md md-camera"></i>&nbsp;APPS</a
            >
            <ul id="APPS" class="collapse">
              <li name="Todo">
                <a href="apps-todo.html">
                  <span id="todosCount" class="pull-right badge z-depth-0"
                    >2</span
                  ><span> Todo </span></a
                >
              </li>
              <li name="Crud">
                <a href="apps-crud.html">
                  <span class="pull-right badge theme-primary-bg z-depth-0"
                    >9</span
                  ><span> Crud </span></a
                >
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#UIelements"
              aria-expanded="false"
              aria-controls="UIelements"
              class="collapsible-header waves-effect active"
              ><i class="md md-photo"></i>&nbsp;UI elements</a
            >
            <ul id="UIelements" class="collapse in">
              <li>
                <a href="ui-elements-cards.html"><span>Cards</span></a>
              </li>
              <li>
                <a href="ui-elements-colors.html"><span>Color</span></a>
              </li>
              <li>
                <a href="ui-elements-grid.html"><span>Grid</span></a>
              </li>
              <li>
                <a href="ui-elements-icons.html"
                  ><span>Icons material design</span></a
                >
              </li>
              <li>
                <a href="ui-elements-weather-icons.html"
                  ><span>Icons weather</span></a
                >
              </li>
              <li>
                <a href="ui-elements-lists.html"><span>Lists</span></a>
              </li>
              <li>
                <a href="ui-elements-typography.html" class="active"
                  ><span>Typography</span></a
                >
              </li>
              <li>
                <a href="ui-elements-messages.html"
                  ><span>Messages &amp; Notifications</span></a
                >
              </li>
              <li>
                <a href="ui-elements-buttons.html"><span>Buttons</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Forms"
              aria-expanded="false"
              aria-controls="Forms"
              class="collapsible-header waves-effect"
              ><i class="md md-input"></i>&nbsp;Forms</a
            >
            <ul id="Forms" class="collapse">
              <li>
                <a href="forms-basic.html"><span>Basic forms</span></a>
              </li>
              <li>
                <a href="forms-advanced.html"><span>Advanced elements</span></a>
              </li>
              <li>
                <a href="forms-validation.html"><span>Validation</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Tables"
              aria-expanded="false"
              aria-controls="Tables"
              class="collapsible-header waves-effect"
              ><i class="md md-list"></i>&nbsp;Tables</a
            >
            <ul id="Tables" class="collapse">
              <li>
                <a href="tables-basic.html"><span>Basic tables</span></a>
              </li>
              <li>
                <a href="tables-data.html"><span>Data tables</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Maps"
              aria-expanded="false"
              aria-controls="Maps"
              class="collapsible-header waves-effect"
              ><i class="md md-place"></i>&nbsp;Maps</a
            >
            <ul id="Maps" class="collapse">
              <li>
                <a href="maps-full-map.html"><span>Full map</span></a>
              </li>
              <li>
                <a href="maps-map-widgets.html"><span>Map widgets</span></a>
              </li>
              <li>
                <a href="maps-vector-map.html"><span>Vector map</span></a>
              </li>
            </ul>
          </li>
          <li icon="md md-insert-chart">
            <a href="charts.html"
              ><i class="md md-insert-chart"></i>&nbsp;<span>Charts</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Extrapages"
              aria-expanded="false"
              aria-controls="Extrapages"
              class="collapsible-header waves-effect"
              ><i class="md md-favorite-outline"></i>&nbsp;Extra pages</a
            >
            <ul id="Extrapages" class="collapse">
              <a target="_blank" href="pages-login.html">Login</a>
              <a target="_blank" href="pages-404.html">404</a>
              <a target="_blank" href="pages-500.html">500</a>
              <a target="_blank" href="pages-material-bird.html">Easter Egg</a>
            </ul>
          </li>
        </ul>
      </aside>
      <div class="main-container">
        <nav class="navbar navbar-default navbar-fixed-top">
          <div class="container-fluid">
            <div class="navbar-header pull-left">
              <button
                type="button"
                class="navbar-toggle pull-left m-15"
                data-activates=".sidebar"
              >
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span> <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              <ul class="breadcrumb">
                <li><a href="#/">Materialism</a></li>
                <li class="active">Typography</li>
              </ul>
            </div>
            <ul class="nav navbar-nav navbar-right navbar-right-no-collapse">
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/dropdown-navbar.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="dropdownListExample"
                >
                  <li role="presentation" class="dropdown-header">
                    <i class="md md-desktop-mac"></i> Welcome
                  </li>
                  <li role="presentation">
                    <a role="menuitem" href="#"
                      ><i class="md md-help"></i> Show introduction</a
                    >
                  </li>
                  <div class="p-10">
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">55</div>
                      </div>
                      New customers
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-info"
                        style="width: 60%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">34</div>
                      </div>
                      Messages
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-danger"
                        style="width: 70%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">21</div>
                      </div>
                      Revenue
                    </div>
                    <div class="progress m-b-5">
                      <div
                        class="progress-bar progress-bar-warning"
                        style="width: 40%"
                      ></div>
                    </div>
                  </div>
                </ul>
              </li>
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/theme-picker.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-settings f20"></i>
                </button>
                <div
                  class="dropdown-menu dropdown-menu-right theme-picker mat-grow-top-right"
                >
                  <div class="container-fluid m-v-15">
                    <div class="pull-right m-r-10">
                      <button type="button" class="close" onclick="$hide()">
                        ×
                      </button>
                    </div>
                    <h4 class="no-margin p-t-5">
                      <i class="md md-filter"></i> Theming options
                    </h4>
                    <div class="row m-t-20">
                      <div class="col-md-6">
                        <div class="w300">Template themes</div>
                        <div
                          class="theme-item theme-template-default"
                          onclick="changeTemplateTheme('theme-template-default');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-dark"
                          onclick="changeTemplateTheme('theme-template-dark');"
                        >
                          <div class="icon"><i class="md md-check"></i></div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-light"
                          onclick="changeTemplateTheme('theme-template-light');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-green"
                          onclick="changeTemplateTheme('theme-template-green');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-blue"
                          onclick="changeTemplateTheme('theme-template-blue');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="w300">Color themes</div>
                        <div class="row gutter-10">
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-pink"
                              onclick="changeColorTheme('theme-pink');"
                            >
                              <div class="icon">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-red"
                              onclick="changeColorTheme('theme-red');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-purple"
                              onclick="changeColorTheme('theme-purple');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-indigo"
                              onclick="changeColorTheme('theme-indigo');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-blue"
                              onclick="changeColorTheme('theme-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-blue"
                              onclick="changeColorTheme('theme-light-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-cyan"
                              onclick="changeColorTheme('theme-cyan');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-teal"
                              onclick="changeColorTheme('theme-teal');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-green"
                              onclick="changeColorTheme('theme-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-green"
                              onclick="changeColorTheme('theme-light-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-lime"
                              onclick="changeColorTheme('theme-lime');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-yellow"
                              onclick="changeColorTheme('theme-yellow');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-amber"
                              onclick="changeColorTheme('theme-amber');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-orange"
                              onclick="changeColorTheme('theme-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-deep-orange"
                              onclick="changeColorTheme('theme-deep-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li navbar-search="" class="pull-right">
                <div>
                  <div class="mat-slide-right pull-right">
                    <form class="search-form form-inline pull-left">
                      <div class="form-group">
                        <label class="sr-only" for="search-input">Search</label>
                        <input
                          type="text"
                          class="form-control"
                          id="search-input"
                          placeholder="Search"
                          autofocus=""
                        />
                      </div>
                    </form>
                  </div>
                  <div class="pull-right">
                    <button class="btn btn-sm btn-link pull-left withoutripple">
                      <i class="md md-search f20"></i>
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <div
          class="main-content"
          autoscroll="true"
          bs-affix-target=""
          init-ripples=""
        >
          <section class="typography">
            <div class="page-header">
              <h1>
                <i class="md md-photo"></i> Typography Page Header
                <small>With Small Text</small>
              </h1>
              <p class="lead">Page header lead.</p>
            </div>
            <div class="row m-b-40">
              <div class="col-lg-4">
                <h1>This is an h1 heading</h1>
                <h2>This is an h2 heading</h2>
                <h3>This is an h3 heading</h3>
                <h4>This is an h4 heading</h4>
                <h5>This is an h5 heading</h5>
                <h6>This is an h6 heading</h6>
              </div>
              <div class="col-lg-4">
                <h3>Example text</h3>
                <p>
                  Nullam quis risus eget <a href="#">urna mollis ornare</a> vel
                  eu leo. Cum sociis natoque penatibus et magnis dis parturient
                  montes, nascetur ridiculus mus. Nullam id dolor id nibh
                  ultricies vehicula.
                </p>
                <p>
                  <small
                    >This line of text is meant to be treated as fine
                    print.</small
                  >
                </p>
                <p>
                  The following snippet of text is
                  <strong>rendered as bold text</strong>.
                </p>
                <p>
                  The following snippet of text is
                  <em>rendered as italicized text</em>.
                </p>
                <p>
                  An abbreviation of the word attribute is
                  <abbr title="attribute">attr</abbr>.
                </p>
              </div>
              <div class="col-lg-4">
                <h3>Paragraphs</h3>
                <p>
                  This is an <b>ordinary paragraph</b> that is
                  <i>long enough</i> to wrap to <u>multiple lines</u> so that
                  you can see how the line spacing looks.
                </p>
                <p class="text-muted">Muted color paragraph.</p>
                <p class="text-warning">Warning color paragraph.</p>
                <p class="text-danger">Danger color paragraph.</p>
                <p class="text-info">Info color paragraph.</p>
                <p class="text-success">Success color paragraph.</p>
                <p>
                  <small
                    >This is text in a <code>small</code> wrapper.
                    <abbr title="No Big Deal">NBD</abbr>, right?</small
                  >
                </p>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="well white">
                  <div class="row">
                    <div class="col-xs-6">
                      <ul>
                        <li>Normal Unordered List</li>
                        <li>
                          Can Also Work
                          <ul>
                            <li>With Nested Children</li>
                            <li>With Nested Children</li>
                            <li>With Nested Children</li>
                          </ul>
                        </li>
                        <li>Adds Bullets to Page</li>
                      </ul>
                    </div>
                    <div class="col-xs-6">
                      <ol>
                        <li>Normal Ordered List</li>
                        <li>
                          Can Also Work
                          <ol>
                            <li>With Nested Children</li>
                            <li>With Nested Children</li>
                            <li>With Nested Children</li>
                          </ol>
                        </li>
                        <li>Adds Bullets to Page</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="well white">
                  <address>
                    <strong>Twitter, Inc.</strong><br />
                    795 Folsom Ave, Suite 600<br />
                    San Francisco, CA 94107<br />
                    <abbr title="Phone">P:</abbr> (*************
                  </address>
                  <address class="no-margin">
                    <strong>Full Name</strong><br />
                    <a href="#"><EMAIL></a>
                  </address>
                </div>
              </div>
              <div class="col-md-3">
                <div class="well white">
                  <blockquote>
                    Here's what a blockquote looks like in Bootstrap.
                    <small
                      >Use <code>small</code> to identify the source.</small
                    >
                  </blockquote>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
