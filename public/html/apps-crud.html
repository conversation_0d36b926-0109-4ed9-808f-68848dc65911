<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Materialism Angular Admin Theme" />
    <meta name="author" content="Theme Guys - The Netherlands" />
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="assets/img/favicon/favicon.ico" />
    <title>Crud - Materialism</title>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body
    scroll-spy=""
    id="top"
    class="theme-template-dark theme-pink alert-open alert-with-mat-grow-top-right"
  >
    <main>
      <aside class="sidebar fixed" style="width: 260px; left: 0px">
        <div class="brand-logo">
          <div id="logo">
            <div class="foot1"></div>
            <div class="foot2"></div>
            <div class="foot3"></div>
            <div class="foot4"></div>
          </div>
          Materialism
        </div>
        <div class="user-logged-in">
          <div class="content">
            <div class="user-name">
              Katsumoto <span class="text-muted f9">admin</span>
            </div>
            <div class="user-email"><EMAIL></div>
            <div class="user-actions">
              <a class="m-r-5" href="#">settings</a> <a href="#">logout</a>
            </div>
          </div>
        </div>
        <ul class="menu-links">
          <li icon="md md-blur-on">
            <a href="index.html"
              ><i class="md md-blur-on"></i>&nbsp;<span>Dashboard</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#APPS"
              aria-expanded="false"
              aria-controls="APPS"
              class="collapsible-header waves-effect active"
              ><i class="md md-camera"></i>&nbsp;APPS</a
            >
            <ul id="APPS" class="collapse in">
              <li name="Todo">
                <a href="apps-todo.html">
                  <span id="todosCount" class="pull-right badge z-depth-0"
                    >2</span
                  ><span> Todo </span></a
                >
              </li>
              <li name="Crud">
                <a href="apps-crud.html" class="active">
                  <span class="pull-right badge theme-primary-bg z-depth-0"
                    >9</span
                  ><span> Crud </span></a
                >
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#UIelements"
              aria-expanded="false"
              aria-controls="UIelements"
              class="collapsible-header waves-effect"
              ><i class="md md-photo"></i>&nbsp;UI elements</a
            >
            <ul id="UIelements" class="collapse">
              <li>
                <a href="ui-elements-cards.html"><span>Cards</span></a>
              </li>
              <li>
                <a href="ui-elements-colors.html"><span>Color</span></a>
              </li>
              <li>
                <a href="ui-elements-grid.html"><span>Grid</span></a>
              </li>
              <li>
                <a href="ui-elements-icons.html"
                  ><span>Icons material design</span></a
                >
              </li>
              <li>
                <a href="ui-elements-weather-icons.html"
                  ><span>Icons weather</span></a
                >
              </li>
              <li>
                <a href="ui-elements-lists.html"><span>Lists</span></a>
              </li>
              <li>
                <a href="ui-elements-typography.html"
                  ><span>Typography</span></a
                >
              </li>
              <li>
                <a href="ui-elements-messages.html"
                  ><span>Messages &amp; Notifications</span></a
                >
              </li>
              <li>
                <a href="ui-elements-buttons.html"><span>Buttons</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Forms"
              aria-expanded="false"
              aria-controls="Forms"
              class="collapsible-header waves-effect"
              ><i class="md md-input"></i>&nbsp;Forms</a
            >
            <ul id="Forms" class="collapse">
              <li>
                <a href="forms-basic.html"><span>Basic forms</span></a>
              </li>
              <li>
                <a href="forms-advanced.html"><span>Advanced elements</span></a>
              </li>
              <li>
                <a href="forms-validation.html"><span>Validation</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Tables"
              aria-expanded="false"
              aria-controls="Tables"
              class="collapsible-header waves-effect"
              ><i class="md md-list"></i>&nbsp;Tables</a
            >
            <ul id="Tables" class="collapse">
              <li>
                <a href="tables-basic.html"><span>Basic tables</span></a>
              </li>
              <li>
                <a href="tables-data.html"><span>Data tables</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Maps"
              aria-expanded="false"
              aria-controls="Maps"
              class="collapsible-header waves-effect"
              ><i class="md md-place"></i>&nbsp;Maps</a
            >
            <ul id="Maps" class="collapse">
              <li>
                <a href="maps-full-map.html"><span>Full map</span></a>
              </li>
              <li>
                <a href="maps-map-widgets.html"><span>Map widgets</span></a>
              </li>
              <li>
                <a href="maps-vector-map.html"><span>Vector map</span></a>
              </li>
            </ul>
          </li>
          <li icon="md md-insert-chart">
            <a href="charts.html"
              ><i class="md md-insert-chart"></i>&nbsp;<span>Charts</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Extrapages"
              aria-expanded="false"
              aria-controls="Extrapages"
              class="collapsible-header waves-effect"
              ><i class="md md-favorite-outline"></i>&nbsp;Extra pages</a
            >
            <ul id="Extrapages" class="collapse">
              <a target="_blank" href="pages-login.html">Login</a>
              <a target="_blank" href="pages-404.html">404</a>
              <a target="_blank" href="pages-500.html">500</a>
              <a target="_blank" href="pages-material-bird.html">Easter Egg</a>
            </ul>
          </li>
        </ul>
      </aside>
      <div class="main-container">
        <nav class="navbar navbar-default navbar-fixed-top">
          <div class="container-fluid">
            <div class="navbar-header pull-left">
              <button
                type="button"
                class="navbar-toggle pull-left m-15"
                data-activates=".sidebar"
              >
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span> <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              <ul class="breadcrumb">
                <li><a href="#/">Materialism</a></li>
                <li class="active">Crud</li>
              </ul>
            </div>
            <ul class="nav navbar-nav navbar-right navbar-right-no-collapse">
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/dropdown-navbar.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="dropdownListExample"
                >
                  <li role="presentation" class="dropdown-header">
                    <i class="md md-desktop-mac"></i> Welcome
                  </li>
                  <li role="presentation">
                    <a role="menuitem" href="#"
                      ><i class="md md-help"></i> Show introduction</a
                    >
                  </li>
                  <div class="p-10">
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">55</div>
                      </div>
                      New customers
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-info"
                        style="width: 60%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">34</div>
                      </div>
                      Messages
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-danger"
                        style="width: 70%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">21</div>
                      </div>
                      Revenue
                    </div>
                    <div class="progress m-b-5">
                      <div
                        class="progress-bar progress-bar-warning"
                        style="width: 40%"
                      ></div>
                    </div>
                  </div>
                </ul>
              </li>
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/theme-picker.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-settings f20"></i>
                </button>
                <div
                  class="dropdown-menu dropdown-menu-right theme-picker mat-grow-top-right"
                >
                  <div class="container-fluid m-v-15">
                    <div class="pull-right m-r-10">
                      <button type="button" class="close" onclick="$hide()">
                        ×
                      </button>
                    </div>
                    <h4 class="no-margin p-t-5">
                      <i class="md md-filter"></i> Theming options
                    </h4>
                    <div class="row m-t-20">
                      <div class="col-md-6">
                        <div class="w300">Template themes</div>
                        <div
                          class="theme-item theme-template-default"
                          onclick="changeTemplateTheme('theme-template-default');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-dark"
                          onclick="changeTemplateTheme('theme-template-dark');"
                        >
                          <div class="icon"><i class="md md-check"></i></div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-light"
                          onclick="changeTemplateTheme('theme-template-light');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-green"
                          onclick="changeTemplateTheme('theme-template-green');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-blue"
                          onclick="changeTemplateTheme('theme-template-blue');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="w300">Color themes</div>
                        <div class="row gutter-10">
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-pink"
                              onclick="changeColorTheme('theme-pink');"
                            >
                              <div class="icon">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-red"
                              onclick="changeColorTheme('theme-red');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-purple"
                              onclick="changeColorTheme('theme-purple');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-indigo"
                              onclick="changeColorTheme('theme-indigo');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-blue"
                              onclick="changeColorTheme('theme-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-blue"
                              onclick="changeColorTheme('theme-light-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-cyan"
                              onclick="changeColorTheme('theme-cyan');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-teal"
                              onclick="changeColorTheme('theme-teal');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-green"
                              onclick="changeColorTheme('theme-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-green"
                              onclick="changeColorTheme('theme-light-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-lime"
                              onclick="changeColorTheme('theme-lime');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-yellow"
                              onclick="changeColorTheme('theme-yellow');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-amber"
                              onclick="changeColorTheme('theme-amber');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-orange"
                              onclick="changeColorTheme('theme-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-deep-orange"
                              onclick="changeColorTheme('theme-deep-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li navbar-search="" class="pull-right">
                <div>
                  <div class="mat-slide-right pull-right">
                    <form class="search-form form-inline pull-left">
                      <div class="form-group">
                        <label class="sr-only" for="search-input">Search</label>
                        <input
                          type="text"
                          class="form-control"
                          id="search-input"
                          placeholder="Search"
                          autofocus=""
                        />
                      </div>
                    </form>
                  </div>
                  <div class="pull-right">
                    <button class="btn btn-sm btn-link pull-left withoutripple">
                      <i class="md md-search f20"></i>
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <div
          class="main-content"
          autoscroll="true"
          bs-affix-target=""
          init-ripples=""
        >
          <section>
            <div class="page-header">
              <div class="actions">
                <button
                  type="button"
                  class="btn btn-link btn-round-sm theme-secondary-text"
                >
                  <span class="md md-info-outline"></span>
                </button>
                <button
                  type="button"
                  class="btn btn-link btn-round-sm theme-secondary-text"
                >
                  <span class="md md-search"></span>
                </button>
              </div>
              <h1><i class="md md-camera"></i> Crud application</h1>
              <p class="lead">
                In most applications you need basic table listings and editing
                capabilities. With this app you can create simple admin
                functionality based on a json web service.
                <u>Exclusively on Materialism</u>.
              </p>
            </div>
          </section>
          <div>
            <div class="table-responsive well no-padding white no-margin">
              <h3 class="table-title">90 Items available</h3>
              <table
                class="table table-full m-b-60"
                id="table-area-1"
                fsm-big-data="data of data take 30"
              >
                <thead>
                  <tr
                    fsm-sticky-header=""
                    scroll-body="'#table-area-1'"
                    scroll-stop="64"
                  >
                    <th>
                      <input type="checkbox" class="relative" />
                    </th>
                    <th>Icon</th>
                    <th fsm-sort="firstname" style="cursor: pointer">
                      Name&nbsp;<i class="md md-sort"></i>
                    </th>
                    <th fsm-sort="lastname" style="cursor: pointer">
                      Last&nbsp;<i class="md md-sort"></i>
                    </th>
                    <th>Summary</th>
                    <th class="text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-flip-to-front cyan lighten-1 icon-color"
                      ></i>
                    </td>
                    <td>Kayley</td>
                    <td>Kshlerin</td>
                    <td>
                      Diam in arcu cursus euismod quis viverra nibh cras
                      pulvinar mattis nunc, sed.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-gps-fixed light-green lighten-2 icon-color"
                      ></i>
                    </td>
                    <td>Sven</td>
                    <td>Stokes</td>
                    <td>
                      Eget mi proin sed libero enim, sed faucibus turpis in eu
                      mi bibendum neque egestas congue quisque egestas diam.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-report red darken-1 icon-color"></i>
                    </td>
                    <td>Felicity</td>
                    <td>Grimes</td>
                    <td>
                      Vitae tempus quam pellentesque nec nam aliquam sem et
                      tortor consequat id porta nibh venenatis cras sed felis
                      eget.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-loyalty lime lighten-2 icon-color"></i>
                    </td>
                    <td>Frieda</td>
                    <td>Runte</td>
                    <td>
                      Et pharetra pharetra, massa massa ultricies mi, quis
                      hendrerit dolor magna eget est lorem ipsum dolor sit.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-local-drink indigo darken-1 icon-color"
                      ></i>
                    </td>
                    <td>Kaylee</td>
                    <td>Watsica</td>
                    <td>
                      Mattis vulputate enim nulla aliquet porttitor lacus,
                      luctus accumsan tortor posuere ac ut consequat semper
                      viverra nam libero justo.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-apps blue darken-1 icon-color"></i>
                    </td>
                    <td>Elisha</td>
                    <td>Keebler</td>
                    <td>
                      Consequat semper viverra nam libero justo, laoreet sit
                      amet cursus sit.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-print lime darken-2 icon-color"></i>
                    </td>
                    <td>Emilie</td>
                    <td>Ankunding</td>
                    <td>
                      Faucibus interdum posuere lorem ipsum dolor sit amet,
                      consectetur adipiscing elit duis tristique sollicitudin.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-looks-6 purple darken-1 icon-color"></i>
                    </td>
                    <td>Nyasia</td>
                    <td>Romaguera</td>
                    <td>
                      Faucibus vitae aliquet nec, ullamcorper sit amet risus
                      nullam eget felis eget nunc lobortis mattis aliquam
                      faucibus purus.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-perm-identity orange darken-2 icon-color"
                      ></i>
                    </td>
                    <td>Cleo</td>
                    <td>Kuhic</td>
                    <td>Tincidunt ornare massa, eget egestas.</td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-local-parking light-green darken-2 icon-color"
                      ></i>
                    </td>
                    <td>Selmer</td>
                    <td>Torphy</td>
                    <td>Pretium, nibh ipsum consequat nisl, vel.</td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-filter-9 red darken-1 icon-color"></i>
                    </td>
                    <td>Jake</td>
                    <td>Marks</td>
                    <td>Viverra justo, nec ultrices dui sapien eget.</td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-web teal lighten-2 icon-color"></i>
                    </td>
                    <td>Desmond</td>
                    <td>Davis</td>
                    <td>
                      Gravida cum sociis natoque penatibus et magnis dis
                      parturient montes, nascetur ridiculus mus mauris.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-alarm-on blue darken-1 icon-color"></i>
                    </td>
                    <td>Fae</td>
                    <td>Hickle</td>
                    <td>
                      Odio morbi quis commodo odio aenean sed adipiscing diam
                      donec adipiscing tristique risus nec feugiat in fermentum
                      posuere urna.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-notifications-paused yellow darken-1 icon-color"
                      ></i>
                    </td>
                    <td>Kristin</td>
                    <td>Bradtke</td>
                    <td>
                      Risus sed vulputate odio ut enim blandit volutpat maecenas
                      volutpat.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-airplanemode-off cyan darken-2 icon-color"
                      ></i>
                    </td>
                    <td>Juvenal</td>
                    <td>Kautzer</td>
                    <td>
                      Habitasse platea dictumst quisque sagittis, purus sit amet
                      volutpat.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-signal-cellular-connected-no-internet-2-bar lime darken-2 icon-color"
                      ></i>
                    </td>
                    <td>Webster</td>
                    <td>Hayes</td>
                    <td>
                      Dolor magna eget est lorem ipsum dolor sit amet,
                      consectetur adipiscing.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-timer-3 yellow lighten-2 icon-color"></i>
                    </td>
                    <td>Nyah</td>
                    <td>Koch</td>
                    <td>
                      At lectus urna duis convallis convallis tellus, id
                      interdum velit laoreet id donec ultrices tincidunt arcu,
                      non sodales neque sodales.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-format-clear pink lighten-1 icon-color"
                      ></i>
                    </td>
                    <td>Shawna</td>
                    <td>Kessler</td>
                    <td>
                      Etiam non quam lacus suspendisse faucibus interdum posuere
                      lorem ipsum.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-crop green lighten-2 icon-color"></i>
                    </td>
                    <td>Pete</td>
                    <td>Heathcote</td>
                    <td>
                      Commodo viverra maecenas accumsan, lacus vel facilisis
                      volutpat, est velit egestas dui, id ornare arcu odio ut
                      sem nulla.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-swap-vert-circle light-green darken-1 icon-color"
                      ></i>
                    </td>
                    <td>Chet</td>
                    <td>Murray</td>
                    <td>
                      Ut enim blandit volutpat maecenas volutpat blandit
                      aliquam.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-keyboard-backspace blue lighten-2 icon-color"
                      ></i>
                    </td>
                    <td>Shanie</td>
                    <td>Oberbrunner</td>
                    <td>
                      Volutpat maecenas volutpat blandit aliquam etiam erat
                      velit, scelerisque in dictum non, consectetur a erat nam
                      at lectus urna duis.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-center-focus-weak lime lighten-1 icon-color"
                      ></i>
                    </td>
                    <td>Ruben</td>
                    <td>Kuhic</td>
                    <td>
                      Et netus et malesuada fames ac turpis egestas integer eget
                      aliquet.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-quick-contacts-mail light-blue darken-1 icon-color"
                      ></i>
                    </td>
                    <td>Samson</td>
                    <td>Moen</td>
                    <td>
                      Pellentesque sit amet, porttitor eget dolor morbi non arcu
                      risus, quis varius quam quisque id diam vel.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-format-color-text deep-orange lighten-2 icon-color"
                      ></i>
                    </td>
                    <td>Paris</td>
                    <td>Heller</td>
                    <td>Pretium fusce id velit ut tortor.</td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-format-color-fill pink lighten-1 icon-color"
                      ></i>
                    </td>
                    <td>Josue</td>
                    <td>Ankunding</td>
                    <td>
                      Nisi, scelerisque eu ultrices vitae, auctor eu augue ut
                      lectus arcu, bibendum at varius vel, pharetra.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i
                        class="md md-collections amber darken-1 icon-color"
                      ></i>
                    </td>
                    <td>Yvette</td>
                    <td>Kulas</td>
                    <td>
                      Facilisi morbi tempus iaculis urna, id volutpat lacus
                      laoreet non curabitur gravida arcu ac.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-compare yellow darken-2 icon-color"></i>
                    </td>
                    <td>Leonora</td>
                    <td>Ortiz</td>
                    <td>
                      Arcu dui vivamus arcu felis, bibendum ut tristique et,
                      egestas.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-rotate-left red lighten-1 icon-color"></i>
                    </td>
                    <td>Russ</td>
                    <td>O'Reilly</td>
                    <td>
                      Risus nullam eget felis eget nunc lobortis mattis aliquam
                      faucibus purus in massa tempor nec feugiat nisl.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-filter-4 blue lighten-1 icon-color"></i>
                    </td>
                    <td>Hillary</td>
                    <td>Friesen</td>
                    <td>
                      Urna duis convallis convallis tellus, id interdum velit
                      laoreet id donec ultrices tincidunt arcu, non.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <input type="checkbox" class="relative" />
                    </td>
                    <td ng-bind-html="item.icon" class="f20">
                      <i class="md md-wrap-text pink darken-1 icon-color"></i>
                    </td>
                    <td>Brenden</td>
                    <td>Nolan</td>
                    <td>
                      Phasellus vestibulum lorem sed risus ultricies tristique
                      nulla aliquet enim tortor, at auctor urna nunc id.
                    </td>
                    <td class="text-right">
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Edit item"
                      >
                        <i class="md md-edit"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="View item"
                      >
                        <i class="md md-search"></i>
                      </button>
                      <button
                        type="button"
                        class="btn btn-link btn-round"
                        data-title="Delete item"
                      >
                        <i class="md md-delete"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="footer-buttons">
              <div
                class="btn btn-primary btn-round btn-lg m-r-10 btn-footer"
                data-title="Remove 0 item(s)"
                data-toggle="tooltip"
              >
                <i class="md md-delete"></i>
              </div>
              <div
                class="btn btn-default btn-round btn-lg m-r-10 btn-footer"
                scroll-to="top"
                data-title="Scroll to top"
                data-toggle="tooltip"
              >
                <i class="md md-arrow-drop-up"></i>
              </div>
              <div
                class="btn btn-primary btn-round btn-lg"
                data-title="New Item"
                data-toggle="tooltip"
              >
                <i class="md md-add"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
