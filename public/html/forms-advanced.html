<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Materialism Angular Admin Theme" />
    <meta name="author" content="Theme Guys - The Netherlands" />
    <meta name="msapplication-TileColor" content="#9f00a7" />
    <meta
      name="msapplication-TileImage"
      content="assets/img/favicon/mstile-144x144.png"
    />
    <meta
      name="msapplication-config"
      content="assets/img/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#ffffff" />
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="assets/img/favicon/apple-touch-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="assets/img/favicon/apple-touch-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="assets/img/favicon/apple-touch-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="assets/img/favicon/apple-touch-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="assets/img/favicon/apple-touch-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="assets/img/favicon/apple-touch-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="assets/img/favicon/apple-touch-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="assets/img/favicon/apple-touch-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="assets/img/favicon/apple-touch-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-32x32.png"
      sizes="32x32"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/android-chrome-192x192.png"
      sizes="192x192"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-96x96.png"
      sizes="96x96"
    />
    <link
      rel="icon"
      type="image/png"
      href="assets/img/favicon/favicon-16x16.png"
      sizes="16x16"
    />
    <link rel="manifest" href="assets/img/favicon/manifest.json" />
    <link rel="shortcut icon" href="assets/img/favicon/favicon.ico" />
    <title>Advanced elements - Materialism</title>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link href="assets/css/vendors.min.css" rel="stylesheet" />
    <link href="assets/css/styles.min.css" rel="stylesheet" />
    <script
      charset="utf-8"
      src="//maps.google.com/maps/api/js?sensor=true"
    ></script>
  </head>

  <body
    scroll-spy=""
    id="top"
    class="theme-template-dark theme-pink alert-open alert-with-mat-grow-top-right"
  >
    <main>
      <aside class="sidebar fixed">
        <div class="brand-logo">
          <div id="logo">
            <div class="foot1"></div>
            <div class="foot2"></div>
            <div class="foot3"></div>
            <div class="foot4"></div>
          </div>
          Materialism
        </div>
        <div class="user-logged-in">
          <div class="content">
            <div class="user-name">
              Katsumoto <span class="text-muted f9">admin</span>
            </div>
            <div class="user-email"><EMAIL></div>
            <div class="user-actions">
              <a class="m-r-5" href="#">settings</a> <a href="#">logout</a>
            </div>
          </div>
        </div>
        <ul class="menu-links">
          <li icon="md md-blur-on">
            <a href="index.html"
              ><i class="md md-blur-on"></i>&nbsp;<span>Dashboard</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#APPS"
              aria-expanded="false"
              aria-controls="APPS"
              class="collapsible-header waves-effect"
              ><i class="md md-camera"></i>&nbsp;APPS</a
            >
            <ul id="APPS" class="collapse">
              <li name="Todo">
                <a href="apps-todo.html">
                  <span id="todosCount" class="pull-right badge z-depth-0"
                    >2</span
                  ><span> Todo </span></a
                >
              </li>
              <li name="Crud">
                <a href="apps-crud.html">
                  <span class="pull-right badge theme-primary-bg z-depth-0"
                    >9</span
                  ><span> Crud </span></a
                >
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#UIelements"
              aria-expanded="false"
              aria-controls="UIelements"
              class="collapsible-header waves-effect"
              ><i class="md md-photo"></i>&nbsp;UI elements</a
            >
            <ul id="UIelements" class="collapse">
              <li>
                <a href="ui-elements-cards.html"><span>Cards</span></a>
              </li>
              <li>
                <a href="ui-elements-colors.html"><span>Color</span></a>
              </li>
              <li>
                <a href="ui-elements-grid.html"><span>Grid</span></a>
              </li>
              <li>
                <a href="ui-elements-icons.html"
                  ><span>Icons material design</span></a
                >
              </li>
              <li>
                <a href="ui-elements-weather-icons.html"
                  ><span>Icons weather</span></a
                >
              </li>
              <li>
                <a href="ui-elements-lists.html"><span>Lists</span></a>
              </li>
              <li>
                <a href="ui-elements-typography.html"
                  ><span>Typography</span></a
                >
              </li>
              <li>
                <a href="ui-elements-messages.html"
                  ><span>Messages &amp; Notifications</span></a
                >
              </li>
              <li>
                <a href="ui-elements-buttons.html"><span>Buttons</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Forms"
              aria-expanded="false"
              aria-controls="Forms"
              class="collapsible-header waves-effect active"
              ><i class="md md-input"></i>&nbsp;Forms</a
            >
            <ul id="Forms" class="collapse in">
              <li>
                <a href="forms-basic.html"><span>Basic forms</span></a>
              </li>
              <li>
                <a href="forms-advanced.html" class="active"
                  ><span>Advanced elements</span></a
                >
              </li>
              <li>
                <a href="forms-validation.html"><span>Validation</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Tables"
              aria-expanded="false"
              aria-controls="Tables"
              class="collapsible-header waves-effect"
              ><i class="md md-list"></i>&nbsp;Tables</a
            >
            <ul id="Tables" class="collapse">
              <li>
                <a href="tables-basic.html"><span>Basic tables</span></a>
              </li>
              <li>
                <a href="tables-data.html"><span>Data tables</span></a>
              </li>
            </ul>
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Maps"
              aria-expanded="false"
              aria-controls="Maps"
              class="collapsible-header waves-effect"
              ><i class="md md-place"></i>&nbsp;Maps</a
            >
            <ul id="Maps" class="collapse">
              <li>
                <a href="maps-full-map.html"><span>Full map</span></a>
              </li>
              <li>
                <a href="maps-map-widgets.html"><span>Map widgets</span></a>
              </li>
              <li>
                <a href="maps-vector-map.html"><span>Vector map</span></a>
              </li>
            </ul>
          </li>
          <li icon="md md-insert-chart">
            <a href="charts.html"
              ><i class="md md-insert-chart"></i>&nbsp;<span>Charts</span></a
            >
          </li>
          <li>
            <a
              href="#"
              data-toggle="collapse"
              data-target="#Extrapages"
              aria-expanded="false"
              aria-controls="Extrapages"
              class="collapsible-header waves-effect"
              ><i class="md md-favorite-outline"></i>&nbsp;Extra pages</a
            >
            <ul id="Extrapages" class="collapse">
              <a target="_blank" href="pages-login.html">Login</a>
              <a target="_blank" href="pages-404.html">404</a>
              <a target="_blank" href="pages-500.html">500</a>
              <a target="_blank" href="pages-material-bird.html">Easter Egg</a>
            </ul>
          </li>
        </ul>
      </aside>
      <div class="main-container">
        <nav class="navbar navbar-default navbar-fixed-top">
          <div class="container-fluid">
            <div class="navbar-header pull-left">
              <button
                type="button"
                class="navbar-toggle pull-left m-15"
                data-activates=".sidebar"
              >
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span> <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>
              <ul class="breadcrumb">
                <li><a href="#/">Materialism</a></li>
                <li class="active">Advanced elements</li>
              </ul>
            </div>
            <ul class="nav navbar-nav navbar-right navbar-right-no-collapse">
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/dropdown-navbar.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-more-vert f20"></i>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-right"
                  role="menu"
                  aria-labelledby="dropdownListExample"
                >
                  <li role="presentation" class="dropdown-header">
                    <i class="md md-desktop-mac"></i> Welcome
                  </li>
                  <li role="presentation">
                    <a role="menuitem" href="#"
                      ><i class="md md-help"></i> Show introduction</a
                    >
                  </li>
                  <div class="p-10">
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">55</div>
                      </div>
                      New customers
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-info"
                        style="width: 60%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">34</div>
                      </div>
                      Messages
                    </div>
                    <div class="progress m-b-10">
                      <div
                        class="progress-bar progress-bar-danger"
                        style="width: 70%"
                      ></div>
                    </div>
                    <div class="w300">
                      <div class="pull-right">
                        <div class="f9 grey-text m-r-5 p-t-5">21</div>
                      </div>
                      Revenue
                    </div>
                    <div class="progress m-b-5">
                      <div
                        class="progress-bar progress-bar-warning"
                        style="width: 40%"
                      ></div>
                    </div>
                  </div>
                </ul>
              </li>
              <li class="dropdown pull-right">
                <button
                  class="dropdown-toggle pointer btn btn-round-sm btn-link withoutripple"
                  data-template="assets/tpl/partials/theme-picker.html"
                  data-toggle="dropdown"
                >
                  <i class="md md-settings f20"></i>
                </button>
                <div
                  class="dropdown-menu dropdown-menu-right theme-picker mat-grow-top-right"
                >
                  <div class="container-fluid m-v-15">
                    <div class="pull-right m-r-10">
                      <button type="button" class="close" onclick="$hide()">
                        ×
                      </button>
                    </div>
                    <h4 class="no-margin p-t-5">
                      <i class="md md-filter"></i> Theming options
                    </h4>
                    <div class="row m-t-20">
                      <div class="col-md-6">
                        <div class="w300">Template themes</div>
                        <div
                          class="theme-item theme-template-default"
                          onclick="changeTemplateTheme('theme-template-default');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-dark"
                          onclick="changeTemplateTheme('theme-template-dark');"
                        >
                          <div class="icon"><i class="md md-check"></i></div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-light"
                          onclick="changeTemplateTheme('theme-template-light');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-green"
                          onclick="changeTemplateTheme('theme-template-green');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                        <div
                          class="theme-item theme-template-blue"
                          onclick="changeTemplateTheme('theme-template-blue');"
                        >
                          <div class="icon hide">
                            <i class="md md-check"></i>
                          </div>
                          <div class="theme-sidenav"></div>
                          <div class="theme-header"></div>
                          <div class="theme-body"></div>
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="w300">Color themes</div>
                        <div class="row gutter-10">
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-pink"
                              onclick="changeColorTheme('theme-pink');"
                            >
                              <div class="icon">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-red"
                              onclick="changeColorTheme('theme-red');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-purple"
                              onclick="changeColorTheme('theme-purple');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-indigo"
                              onclick="changeColorTheme('theme-indigo');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-blue"
                              onclick="changeColorTheme('theme-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-blue"
                              onclick="changeColorTheme('theme-light-blue');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-cyan"
                              onclick="changeColorTheme('theme-cyan');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-teal"
                              onclick="changeColorTheme('theme-teal');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-green"
                              onclick="changeColorTheme('theme-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-light-green"
                              onclick="changeColorTheme('theme-light-green');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-lime"
                              onclick="changeColorTheme('theme-lime');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-yellow"
                              onclick="changeColorTheme('theme-yellow');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-amber"
                              onclick="changeColorTheme('theme-amber');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-orange"
                              onclick="changeColorTheme('theme-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                          <div
                            class="col-xs-2 col-sm-2 col-md-4 theme-colors ng-scope"
                          >
                            <div
                              class="theme-item theme-deep-orange"
                              onclick="changeColorTheme('theme-deep-orange');"
                            >
                              <div class="icon hide">
                                <i class="md md-check"></i>
                              </div>
                              <div class="theme-color-1"></div>
                              <div class="theme-color-2"></div>
                              <div class="theme-color-3"></div>
                              <div class="theme-color-4"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li navbar-search="" class="pull-right">
                <div>
                  <div class="mat-slide-right pull-right">
                    <form class="search-form form-inline pull-left">
                      <div class="form-group">
                        <label class="sr-only" for="search-input">Search</label>
                        <input
                          type="text"
                          class="form-control"
                          id="search-input"
                          placeholder="Search"
                          autofocus=""
                        />
                      </div>
                    </form>
                  </div>
                  <div class="pull-right">
                    <button class="btn btn-sm btn-link pull-left withoutripple">
                      <i class="md md-search f20"></i>
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </nav>
        <div
          class="main-content"
          autoscroll="true"
          bs-affix-target=""
          init-ripples=""
        >
          <section class="forms-advanced">
            <div class="page-header">
              <h1><i class="md md-input"></i> Advanced elements</h1>
              <p class="lead">
                All form elements we created for you which you can use for your
                successful web projects!
              </p>
            </div>
            <div class="row m-b-40">
              <div class="col-md-3 col-md-push-9" id="general-elements-intro">
                <h5>General form elements used in basic forms</h5>
                <p>
                  All the elements are just default bootstrap markup in
                  execption of the selection box which is
                  <a target="_blank" href="https://select2.github.io/"
                    >Select2</a
                  >.
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form-floating placeholder-form">
                    <fieldset>
                      <legend>General elements</legend>
                      <div class="form-group filled">
                        <label for="inputEmail" class="control-label"
                          >Text input</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          value="Vulputate odio ut enim blandit volutpat maecenas volutpat blandit aliquam etiam erat velit, scelerisque in dictum non, consectetur."
                        />
                      </div>
                      <div class="form-group filled">
                        <label for="inputPassword" class="control-label"
                          >Password</label
                        >
                        <input
                          type="password"
                          class="form-control"
                          id="inputPassword"
                          value="nakama?"
                        />
                      </div>
                      <div class="form-group">
                        <div class="checkbox">
                          <label class="filled">
                            <input type="checkbox" checked="checked" /> Checkbox
                          </label>
                        </div>
                      </div>
                      <div class="form-group filled">
                        <label for="textArea" class="control-label"
                          >Textarea with autogrow</label
                        >
                        <textarea
                          class="form-control vertical"
                          rows="3"
                          id="textArea"
                        >
Diam maecenas ultricies mi eget mauris pharetra et ultrices neque ornare aenean euismod elementum. Sit amet nisl purus, in. Tellus rutrum tellus pellentesque eu tincidunt tortor aliquam nulla facilisi cras. Urna neque viverra justo, nec ultrices dui sapien eget mi proin sed libero.</textarea
                        >
                        <span class="help-block">Help.</span>
                      </div>
                      <div class="form-group">
                        <div class="radio-inline">
                          <label class="filled">
                            <input
                              type="radio"
                              name="optionsRadios"
                              id="optionsRadios1"
                              value="option1"
                              checked=""
                            />
                            Radio option
                          </label>
                        </div>
                        <div class="radio-inline">
                          <label class="filled">
                            <input
                              type="radio"
                              name="optionsRadios"
                              id="optionsRadios1"
                              value="option1"
                              checked=""
                            />
                            Radio option 2
                          </label>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="control-label normal">Switch</label>
                        <div class="switch">
                          <label class="filled">
                            Off <input type="checkbox" checked="checked" />
                            <span class="lever"></span> On
                          </label>
                        </div>
                      </div>
                      <div class="form-group filled">
                        <label class="control-label">Select</label>
                        <select class="form-control">
                          <option value="Monkey D. Luffy">
                            Monkey D. Luffy
                          </option>
                          <option value="Roronoa Zoro">Roronoa Zoro</option>
                          <option value="Tony Tony Chopper">
                            Tony Tony Chopper
                          </option>
                          <option value="Nico Robin">Nico Robin</option>
                          <option value="Bon Clay">Bon Clay</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <select class="select2 form-control"></select>
                      </div>
                      <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                          Submit
                        </button>
                        <button type="reset" class="btn btn-default">
                          Cancel
                        </button>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40" id="datepickers">
              <div class="col-md-3 col-md-push-9">
                <h5>Datepickers</h5>
                <p>
                  The
                  <a
                    target="_blank"
                    href="http://eonasdan.github.io/bootstrap-datetimepicker/"
                    >bootstrap datepickers</a
                  >
                  are easy to use and configure.
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form-floating placeholder-form">
                    <fieldset>
                      <legend>Date pickers</legend>
                      <div class="form-group">
                        <label class="control-label">Date</label>
                        <input type="text" class="form-control datepicker" />
                      </div>
                      <div class="form-group">
                        <label class="control-label">Time</label>
                        <input type="text" class="form-control timepicker" />
                      </div>
                      <div class="form-group">
                        <div class="input-group">
                          <span class="input-group-addon" id="basic-addon1"
                            ><i class="md md-event-available"></i
                          ></span>
                          <div class="row">
                            <div class="col-md-5">
                              <input
                                type="text"
                                class="form-control datepicker"
                              />
                            </div>
                            <div class="col-md-2">
                              <input
                                type="text"
                                class="form-control timepicker"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <div class="input-group">
                          <span class="input-group-addon" id="basic-addon1"
                            ><i class="md md-event-available"></i
                          ></span>
                          <div class="row">
                            <div class="col-md-6">
                              <input
                                type="text"
                                class="form-control datepicker-from"
                              />
                            </div>
                            <div class="col-md-6">
                              <input
                                type="text"
                                class="form-control datepicker-until"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <br />
                      <div class="form-group filled-static">
                        <label for="inputEmail" class="control-label"
                          >HTML date picker</label
                        >
                        <input type="date" class="form-control" />
                      </div>
                      <div class="form-group filled-static">
                        <label for="inputEmail" class="control-label"
                          >HTML month picker</label
                        >
                        <input type="month" class="form-control" />
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40" id="select2-and-typeahead">
              <div class="col-md-3 col-md-push-9">
                <h5>Select2 and typeahead variations</h5>
                <p>
                  <a target="_blank" href="https://select2.github.io/"
                    >Select2</a
                  >
                  is an powerful component to use relation data in your admin or
                  application.
                </p>
                <p>
                  <a
                    target="_blank"
                    href="https://github.com/bassjobsen/Bootstrap-3-Typeahead"
                    >Typeahead</a
                  >
                  is a lightweight select replacement
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form">
                    <fieldset>
                      <legend>Select</legend>
                      <div class="form-group">
                        <label class="control-label">Select2</label>
                        <select class="select2 form-control"></select>
                      </div>
                      <div class="form-group">
                        <label class="control-label">Select2 multiple</label>
                        <select
                          class="select2-tags form-control"
                          multiple=""
                        ></select>
                      </div>
                      <br />
                      <div class="form-group">
                        <label><i class="md md-keyboard"></i> Typeahead</label>
                        <input
                          type="text"
                          class="form-control typeahead"
                          placeholder="Enter state"
                          data-provide="typeahead"
                          autocomplete="off"
                        />
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40" id="fileupload">
              <div class="col-md-3 col-md-push-9">
                <h5>File upload</h5>
                <p>
                  Lightweight
                  <a
                    target="_blank"
                    href="https://blueimp.github.io/jQuery-File-Upload/"
                    >jQuery File Upload</a
                  >
                  to upload files.
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form">
                    <fieldset>
                      <legend class="m-b-10">File upload</legend>
                      <div>
                        <div class="form-group">
                          <span class="btn btn-info fileinput-button">
                            <span>Upload with button</span>
                            <input
                              type="file"
                              name="files[]"
                              multiple=""
                              class="fileupload"
                            />
                          </span>
                        </div>
                        <div class="form-group">
                          <label class="control-label"
                            >Upload with form element</label
                          >
                          <input type="file" name="file" accept="image/*" />
                        </div>
                        <div class="form-group">
                          <label class="control-label"
                            >Upload with drag drop</label
                          >
                          <div class="drop-box">Select or Drop Images here</div>
                        </div>
                        <ul
                          style="clear: both"
                          class="response list-unstyled"
                        ></ul>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40" id="wysiwyg">
              <div class="col-md-3 col-md-push-9">
                <h5>Text editor</h5>
                <p>
                  <a
                    target="_blank"
                    href="https://github.com/summernote/summernote"
                    >summernote</a
                  >
                  is a super cool WYSIWYG Text Editor for Bootstrap.
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form">
                    <fieldset>
                      <legend>Text editor</legend>
                      <div class="wysiwyg"></div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
            <div class="row m-b-40" id="noui-slider">
              <div class="col-md-3 col-md-push-9">
                <h5>Sliders</h5>
                <p>
                  <a target="_blank" href="http://refreshless.com/nouislider/"
                    >noUiSlider</a
                  >
                  is a range slider without bloat. It offers a ton off features,
                  and it is as small, lightweight and minimal as possible, which
                  is great for mobile use on the many supported devices,
                  including iPhone, iPad, Android devices &amp; Windows (Phone)
                  8 desktops, tablets and all-in-ones. It works on desktops too,
                  of course!
                </p>
              </div>
              <div class="col-md-8 col-md-pull-3">
                <div class="well white">
                  <form class="form">
                    <fieldset>
                      <legend>Sliders</legend>
                      <div class="form-group">
                        <label class="control-label">Standard slider</label>
                        <div class="slider-1"></div>
                      </div>
                      <div class="form-group">
                        <label class="control-label">With bind</label>
                        <div class="slider-2 noUi-range"></div>
                        <span class="help-block"
                          >Value from slider: <span class="sliderval"></span> /
                          <span class="sliderval2"></span
                        ></span>
                      </div>
                      <div class="form-group">
                        <label class="control-label">With indicator</label>
                        <div class="slider-3"></div>
                      </div>
                    </fieldset>
                  </form>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>
    <script charset="utf-8" src="assets/js/vendors.min.js"></script>
    <script charset="utf-8" src="assets/js/app.min.js"></script>
  </body>
</html>
