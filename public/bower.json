{"name": "materialism", "version": "1.1.2", "authors": ["Theme Guys"], "license": "Envato REGULAR LICENSE", "homepage": "http://themeforest.net/item/materialism-angular-bootstrap-admin-template/11322821", "private": true, "keywords": ["AngularJS", "Bootstrap", "Admin", "Dashboard", "Panel", "App", "Charts", "Responsive"], "dependencies": {"angular": "1.3.15", "angular-animate": "1.3.15", "angular-auto-validate": "1.18.6", "angular-cookies": "1.3.15", "angular-elastic": "2.4.2", "ng-file-upload": "~9.1.2", "angular-google-maps": "2.0.19", "angular-loading-bar": "0.7.1", "angular-local-storage": "0.1.5", "angular-motion": "0.3.4", "angular-route": "1.3.15", "angular-sanitize": "1.3.15", "angulartics": "0.17.2", "ngSmoothScroll": "1.7.1", "angular-strap": "2.3.5", "animate-scss": "1.2.1", "bootstrap-additions": "0.2.3", "bootstrap-css-only": "3.3.4", "bootstrap-sass": "3.3.4", "bower-jvectormap": "1.2.2", "c3": "0.4.10", "c3-angular": "^0.6.0", "font-awesome": "4.3.0", "hammerjs": "2.0.4", "jquery": "2.1.3", "jquery-hammerjs": "2.0.0", "lodash": "2.4.1", "material-design-iconic-font": "1.1.1", "ng-table": "0.5.4", "nouislider": "7.0.10", "rangy": "1.2.3", "roboto-fontface": "0.4.0", "select2": "4.0.0", "textAngular": "1.3.11", "angular-ui-select": "0.11.2", "velocity": "1.2.2", "weather-icons": "1.3.2", "bootstrap3-typeahead": "3.1.1", "summernote": "^0.6.16", "eonasdan-bootstrap-datetimepicker": "4.14.30", "jquery-file-upload": "9.10.1", "gmaps": "0.4.18", "datatables": "^1.10.7", "bootstrap-validator": "^0.9.0", "angular-busy": "~4.1.3", "angular-chroma": "*", "angular-trix": "^1.0.2", "angularjs-dropdown-multiselect": "1.11.8"}, "resolutions": {"angular": "1.3.15"}}