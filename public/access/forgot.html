<div class="page-login" ng-controller="LoginController">
  <div class="center">
    <div
      class="card bordered z-depth-2"
      style="margin: 0% auto; max-width: 400px"
    >
      <div class="card-header">
        <div class="brand-logo">
          <div style="display: inline">
            <img src="/assets/img/new-logo.png" width="200" />
          </div>
        </div>
      </div>
      <form
        class="form-floating"
        novalidate
        name="resetPasswordForm"
        ng-submit="forgot(resetPasswordForm)"
      >
        <div class="card-content">
          <div class="m-b-30">
            <h3 class="strong text-center">Resetear contraseña</h3>
          </div>
          <div class="form-group" ng-if="!successMessage">
            <label for="emailAddress" class="control-label">Email</label>
            <input
              type="email"
              name="emailAddress"
              id="emailAddress"
              class="form-control"
              ng-model="user.emailAddress"
              ng-required="true"
            />
          </div>
          <div
            class="bs-component"
            ng-if="errorMessage != null || successMessage != null"
          >
            <div class="alert alert-success" ng-if="successMessage">
              <h4>Éxito</h4>
              {{successMessage}}
            </div>
            <div
              class="alert alert-dismissible alert-danger"
              ng-if="errorMessage"
            >
              <button
                type="button"
                class="close"
                data-dismiss="alert"
                ng-click="clearError()"
              >
                ×
              </button>
              <h4>Error</h4>
              {{errorMessage}}
            </div>
          </div>
        </div>
        <div class="card-action clearfix">
          <div class="pull-left">
            <a class="btn btn-link black-text" href="#/access/login">Login</a>
          </div>
          <div class="pull-right" ng-if="!successMessage">
            <button type="submit" class="btn btn-primary btn-lavomat">
              Enviar
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
