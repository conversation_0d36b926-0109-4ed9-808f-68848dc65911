# This file defines all application routes for totem client
# path: /totem/*
# doc: /doc/totem_v1.apib
# ~~~~

## Booking
POST    /v1/booking         domains.totem.controller.v1.BookingController.create()
GET     /v1/booking/:id     domains.totem.controller.v1.BookingController.info(id:String)
DELETE  /v1/booking/:id     domains.totem.controller.v1.BookingController.cancel(id:String)

## Building
GET    /v1/building         domains.totem.controller.v1.BuildingController.info()

