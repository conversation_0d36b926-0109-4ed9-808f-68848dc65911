# This file defines all application routes for assistant domain
# path: /asst/*
# doc: /doc/asst_v1.apib
# ~~~~

POST    /v1/signin                      domains.auth.controllers.v1.AuthenticationController.signInWithRecaptcha()

GET     /v1/buildings/laundromats       domains.assistant.controllers.v1.BuildingsController.laundromats()
GET     /v1/buildings/:slug             domains.assistant.controllers.v1.BuildingsController.info(slug:String)
GET     /v1/buildings/:slug/machines    domains.assistant.controllers.v1.buildings.MachinesController.list(slug:String)
GET     /v1/buildings/:slug/pricing     domains.assistant.controllers.v1.BuildingsController.pricing(slug:String)

# POST    /v1/simple-solution/users

GET     /v1/cards/:uid                  domains.assistant.controllers.v1.CardsController.info(uid:String)
GET     /v1/cards/:uid/activity         domains.assistant.controllers.v1.CardsController.activity(uid:String)

GET     /v1/users/cards                 domains.assistant.controllers.v1.users.CardsController.list()
POST    /v1/users/cards                 domains.assistant.controllers.v1.users.CardsController.assign()
DELETE  /v1/users/cards/:uid            domains.assistant.controllers.v1.users.CardsController.unassign(uid:String)

POST    /v1/activate                    domains.assistant.controllers.v1.ActivationController.activate()
