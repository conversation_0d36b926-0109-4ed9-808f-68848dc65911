<?xml version='1.0' encoding='UTF-8'?><definitions xmlns:xsd1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/wsDeclaVtas" xmlns:tns="http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas">
  <types>
<schema xmlns:xsd1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/wsDeclaVtas" xmlns:tns1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/wsDeclaVtas" xmlns:tns="http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/wsDeclaVtas">
  <complexType name="entrada">
    <sequence>
      <element maxOccurs="unbounded" minOccurs="1" name="wsDeclaVtas">
        <complexType>
          <all>
            <element name="General">
              <complexType>
                <sequence>
                  <element name="Cab">
                    <complexType>
                      <all>
                        <element name="NumeroRUT">
                          <annotation>
                            <documentation>Número de RUT sin puntos ni guiones. Dominio: CHAR(20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="CodigoShopping">
                          <annotation>
                            <documentation>Codigo de Shopping para el cual declara. Dominio: CHAR(15)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="15"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="NumeroContrato" type="int">
                          <annotation>
                            <documentation>Número de contrato para el cual declara. Dominio: INTEGER</documentation>
                          </annotation>
                        </element>
                        <element name="CodigoCanal">
                          <annotation>
                            <documentation>Código de Canal para el cual declara. Dominio: CHAR(15)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="15"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element minOccurs="0" name="Caja">
                          <annotation>
                            <documentation>Caja donde se emite el CFE. Opcional. Dominio: CHAR(20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="CodigoCFE" type="int">
                          <annotation>
                            <documentation>Código de Documento CFE según definición de DGI. Dominio: INTEGER</documentation>
                          </annotation>
                        </element>
                        <element name="NumeroCFE" type="int">
                          <annotation>
                            <documentation>Número de CFE.Dominio: INTEGER</documentation>
                          </annotation>
                        </element>
                        <element name="SerieCFE">
                          <annotation>
                            <documentation>Serie de CFE. Dominio: CHAR (20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element minOccurs="0" name="DocIdCFE">
                          <annotation>
                            <documentation>Documento de identificación del comprador. Opcional. Dominio: CHAR(20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="MonedaCFE">
                          <annotation>
                            <documentation>Moneda de emisión del CFE según DGI. Dominio: CHAR(3)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="3"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="FechaEmisionCFE">
                          <annotation>
                            <documentation>Fecha de Emisión del CFE. formato YYYY-MM-DD HH:MM. Dominio: CHAR (20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="TotalMOCIVA" type="double">
                          <annotation>
                            <documentation>Total en moneda del CFE, con IVA. Dominio: DECIMAL (14,2)</documentation>
                          </annotation>
                        </element>
                        <element name="TotalMNSIVA" type="double">
                          <annotation>
                            <documentation>Total en Pesos, sin IVA. Dominio: DECIMAL (14,2)</documentation>
                          </annotation>
                        </element>
                        <element minOccurs="0" name="ObsCab1">
                          <annotation>
                            <documentation>Observación 1 - cabezal. Opcional. Dominio: CHAR(50)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="50"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="TipodeCambio" type="double">
                          <annotation>
                            <documentation>Tipo de Cambio a Pesos. Dominio: DECIMAL(12,6)</documentation>
                          </annotation>
                        </element>
                        <element minOccurs="0" name="ObsCab2">
                          <annotation>
                            <documentation>Observación 2 - cabezal. Opcional. Dominio: CHAR(50)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="50"/>
                            </restriction>
                          </simpleType>
                        </element>
                      </all>
                    </complexType>
                  </element>
                  <element maxOccurs="unbounded" name="Det">
                    <complexType>
                      <all>
                        <element name="CodRubro">
                          <annotation>
                            <documentation>Código de Rubro de Venta. Dominio: CHAR(15)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="15"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="ContadoMNSIVA" type="double">
                          <annotation>
                            <documentation>Importe Contado en pesos sin impuestos. Dominio: DECIMAL (14,2)</documentation>
                          </annotation>
                        </element>
                        <element name="CreditoMNSIVA" type="double">
                          <annotation>
                            <documentation>Importe Crédito en pesos sin impuestos. Dominio: DECIMAL (14,2)</documentation>
                          </annotation>
                        </element>
                        <element name="DebitoMNSIVA" type="double">
                          <annotation>
                            <documentation>Importe Débito en pesos sin impuestos. Dominio: DECIMAL (14,2)</documentation>
                          </annotation>
                        </element>
                        <element name="IncluirenPromo">
                          <annotation>
                            <documentation>Incluir en Promo. Dominio: CHAR(1)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="1"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element minOccurs="0" name="ObsLin1">
                          <annotation>
                            <documentation>Obseración 1 - línea. Opcional. Dominio: CHAR(50)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="50"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element minOccurs="0" name="ObsLin2">
                          <annotation>
                            <documentation>Obseración 2 - línea. Opcional. Dominio: CHAR(50)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="50"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element minOccurs="0" name="ValorVariableNumerico" type="double">
                          <annotation>
                            <documentation>Valor Variable Numérico. Dominio: DECIMAL(14,2)</documentation>
                          </annotation>
                        </element>
                      </all>
                    </complexType>
                  </element>
                </sequence>
              </complexType>
            </element>
          </all>
        </complexType>
      </element>
    </sequence>
  </complexType>
  <complexType name="salida">
    <sequence>
      <element maxOccurs="unbounded" minOccurs="1" name="Resultado">
        <complexType>
          <all>
            <element name="estado" type="int">
              <annotation>
                <documentation>0  - Grabado correctamente, 1 - Pre-grabado correctamente, 2 - Error al grabar, 3 - Error al procesar archivo</documentation>
              </annotation>
            </element>
            <element name="mensaje" type="string">
              <annotation>
                <documentation>Mensaje de error</documentation>
              </annotation>
            </element>
            <element name="identificador" type="int">
              <annotation>
                <documentation>Identificador de Mensaje</documentation>
              </annotation>
            </element>
          </all>
        </complexType>
      </element>
    </sequence>
  </complexType>
  <element name="procesarAlta" type="tns1:entrada"/>
  <element name="procesarAltaReturn" type="tns1:salida"/>
  <element name="procesarAltaDesatendido" type="tns1:entrada"/>
  <element name="procesarAltaDesatendidoReturn" type="tns1:salida"/>
</schema>
  </types>
  <message name="procesarAltaInput">
    <part element="xsd1:procesarAlta" name="body">
    </part>
  </message>
  <message name="procesarAltaDesatendidoInput">
    <part element="xsd1:procesarAltaDesatendido" name="body">
    </part>
  </message>
  <message name="procesarAltaDesatendidoOutput">
    <part element="xsd1:procesarAltaDesatendidoReturn" name="body">
    </part>
  </message>
  <message name="procesarAltaOutput">
    <part element="xsd1:procesarAltaReturn" name="body">
    </part>
  </message>
  <portType name="wsDeclaVtasPortType">
    <operation name="procesarAlta">
      <input message="tns:procesarAltaInput" name="procesarAltaRequest">
    </input>
      <output message="tns:procesarAltaOutput" name="procesarAltaResponse">
    </output>
    </operation>
    <operation name="procesarAltaDesatendido">
      <input message="tns:procesarAltaDesatendidoInput" name="procesarAltaDesatendidoRequest">
    </input>
      <output message="tns:procesarAltaDesatendidoOutput" name="procesarAltaDesatendidoResponse">
    </output>
    </operation>
  </portType>
  <binding name="wsDeclaVtasSoapBinding" type="tns:wsDeclaVtasPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="procesarAlta">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="procesarAltaDesatendido">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="wsDeclaVtasService">
    <port binding="tns:wsDeclaVtasSoapBinding" name="wsDeclaVtasPort">
      <soap:address location="https://portal.plazaitaliashopping.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas"/>
    </port>
  </service>
</definitions>