<configuration>
  <property resource="application.conf" />

  <conversionRule conversionWord="coloredLevel" converterClass="play.api.Logger$ColoredLevel" />

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date - %thread - %level - %logger - %message%n%xException{5}</pattern>
    </encoder>
  </appender>

  <appender name="PAPERTRAIL" class="ch.qos.logback.classic.net.SyslogAppender">
    <syslogHost>${papertrail.logger.host}</syslogHost>
    <port>${papertrail.logger.port}</port>
    <facility>USER</facility>
    <suffixPattern>lm_app-${papertrail.logger.env}: %level - %logger{15} - %message - %replace(%xException{5}){'\n',''}%nopex%n</suffixPattern>
  </appender>

  <!--
    The logger name is typically the Java/Scala package name.
    This configures the log level to log at for a package and its children packages.
  -->
  <logger name="play" level="INFO" />
  <logger name="application" level="DEBUG" />

  <root level="ERROR">
    <appender-ref ref="STDOUT" />
    <appender-ref ref="PAPERTRAIL" />
  </root>

</configuration>
