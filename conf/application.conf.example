 # This is the main configuration file for the application.
# ~~~~~


# Secret key
# ~~~~~
# The secret key is used to secure cryptographics functions.
#
# This must be changed for production, but we recommend not changing it in this file.
#
# See http://www.playframework.com/documentation/latest/ApplicationSecret for more details.
play.crypto.secret = "changeme"

# The application languages
# ~~~~~
play.i18n.langs = [ "en", "es" ]

# Router
# ~~~~~
# Define the Router object to use for this application.
# This router will be looked up first when the application is starting up,
# so make sure this is the entry point.
# Furthermore, it's assumed your route file is named properly.
# So for an application router like `my.application.Router`,
# you may need to define a router file `conf/my.application.routes`.
# Default to Routes in the root package (and conf/routes)
# play.http.router = my.application.Routes

# Database configuration
# ~~~~~
# You can declare as many datasources as you want.
# By convention, the default datasource is named `default`
#

#HikariCP
dbplugin=disabled
db.default.driver=com.mysql.jdbc.Driver
db.default.url="******************************"
db.default.username=lavomat
db.default.password="lavomat"
db.default.jndiName=DefaultDS

db.default.maximumPoolSize=25
db.default.leakDetectionThreshold=15000

jpa.default=defaultPersistenceUnit

# Evolutions
# ~~~~~
# You can disable evolutions if needed
play.evolutions.enabled=false

# You can disable evolutions for a specific datasource if necessary
# play.evolutions.db.default.enabled=false


# Endpoints that don't require security checks
security.exceptions = "[]"

# Play modules
# ~~~~~
# Modules are used to extend the functionality of Play applications.
play.modules.enabled += "global.ApplicationModule"
play.modules.enabled += "playconfig.ConfigModule"

# MQTT AWS BROKER IP
mqtt.broker.ip = "tcp://************:1883"

## payment getaways ##
payment_getaways.transactions.disabled = false

# MERCADO PAGO SETTINGS - produccion mercado pagos -
mercadopago.access.token = "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-408269362"
mercadopago.client.id = "7573715325607961"
mercadopago.client.secret = "Ogw0eT9laCzuGFZUTNmm1CXgZEwI3t3H"

# Bancard settings - sandbox
bancard.keys.public = "********************************"
bancard.keys.private = "S3qsykbTvjhEW)H.alYLGLmf9e9jSf3bvV+PSYYH"
bancard.webPayment.url = "https://vpos.infonet.com.py:8888"
bancard.keys.secret = "ybA9QZjrtREy2V4EeyKGVBfpaU4d8aUsfRRF"

# Transact settings - Sandbox
transact = {
    lavomat = {
        keys.empcode = "LAVOM1"
        keys.termcode = "T00001"
        keys.emphash = "42424242424242423333333333333333"
        emulacion=true
    }
    lavamar = {
        keys.empcode = "LAVAMAR1"
        keys.termcode = "T00000"
        keys.emphash = "FFFFFF777777777FFFFFF7777FFFF77F"
        emulacion=true
    }
}

# BambooPayment settings - sandbox
bambooPayment.physicalAgent.keyword = "NMIDGYTSIWW="
bambooPayment.physicalAgent.username = "Test@2022Lavomat"
bambooPayment.physicalAgent.customer.id = "6110"
bambooPayment.physicalAgent.customer.key = "CpagahZYH16wigSBviWaykyDqUdX8yno"
bambooPayment.webPayments.customer.key = "CpagahZYH16wigSBviWaykyDqUdX8yno"
bambooPayment.webPayments.customer.id = "6110"
bambooPayment.webPayments.url = "https://testing.pagosweb.com.uy/v3.4/requestprocessor.aspx"

play.filters.cors {
  # allow all paths
  # allow all origins
  # allow all headers
}

# Environment
api.env = "LOCAL"
api.baseUrl="http://localhost:9000"

# Logger - Papertrail
papertrail.logger.env=development
papertrail.logger.host=logs5.papertrailapp.com
papertrail.logger.port=16352

# Assistant portal
assistant.url="http://localhost:8080"

# Coliving confirmation token
confirmation.token.url="/complete-signup?token="

# Public site base url
publicSite.baseUrl="http://localhost:3000"

# Email
email.smtp.user="a-user"
email.smtp.password="a-password"
email.smtp.port=1234
email.hostname="hostname"
email.debug.enabled=true
email.debug.email="<EMAIL>"

email.lavomat.admin="<EMAIL>"
email.lavomat.ceo="<EMAIL>"
email.lavomat.cto="<EMAIL>"
email.lavomat.asst="<EMAIL>"

# Google reCaptcha v3
google.reCaptcha.secret="6LcrEQskAAAAAGuwJWmuOC2gdFFKWKxfX5tsxrRd"
google.reCaptcha.url="https://www.google.com/recaptcha/api/siteverify"

# Lavomat
lavomat.rut="*********012"

# Plaza Italia SOAP
plazaItalia.enableNotification=false
plazaItalia.soap.notifySaleUrl=""
plazaItalia.soap.notifyRefundUrl=""
plazaItalia.soap.user=""
plazaItalia.soap.password=""

# Totem
totem.booking.enabled=true

play.modules.disabled += "play.api.cache.EhCacheModule"
play.modules.enabled += "services.cache.redis.RedisModule"

# Redis cache
play.modules.disabled += "play.api.cache.EhCacheModule"
play.modules.enabled += "services.cache.redis.RedisModule"

redis.host="localhost"
redis.port=6379
redis.enabled="true"

# Sicfe
sicfe = {
    lavomat = {
        url="https://your.billing-provider.com"
        env="test"
        user="user"
        pass="passs"
        tenant="test"
        ruc="*********012"
        companyName="TEST"
        commercialName="TEST S.R.L."
        phone="*********"
        email="<EMAIL>"
    }
    lavamar = {
        url="https://your-other.billing-provider.com"
        env="test"
        user="user"
        pass="passs"
        tenant="lavomat"
        ruc="*********012"
        companyName="TEST 2"
        commercialName="TEST SAS"
        phone="*********"
        email="<EMAIL>"
    }
}
