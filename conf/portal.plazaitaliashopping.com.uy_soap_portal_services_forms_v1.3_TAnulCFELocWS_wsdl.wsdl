<?xml version='1.0' encoding='UTF-8'?><definitions xmlns:xsd1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/TAnulCFELocWS" xmlns:tns="http://nodum.com.uy/soap/portal/services/forms/v1.3/TAnulCFELocWS" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://nodum.com.uy/soap/portal/services/forms/v1.3/TAnulCFELocWS">
  <types>
<schema xmlns:xsd1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/TAnulCFELocWS" xmlns:tns1="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/TAnulCFELocWS" xmlns:tns="http://nodum.com.uy/soap/portal/services/forms/v1.3/TAnulCFELocWS" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://nodum.com.uy/soap/portal/schemas/forms/v1.3/TAnulCFELocWS">
  <complexType name="entrada">
    <sequence>
      <element maxOccurs="unbounded" minOccurs="1" name="TAnulCFELocWS">
        <complexType>
          <all>
            <element name="General">
              <complexType>
                <sequence>
                  <element name="G1">
                    <complexType>
                      <all>
                        <element name="NumeroRUT">
                          <annotation>
                            <documentation>Número de RUT sin puntos ni guiones. Dominio: CHAR(20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="20"/>
                            </restriction>
                          </simpleType>
                        </element>
                        <element name="CodigoCFE" type="int">
                          <annotation>
                            <documentation>Código de Documento CFE según definición de DGI. Dominio: INTEGER</documentation>
                          </annotation>
                        </element>
                        <element name="NumeroCFE" type="int">
                          <annotation>
                            <documentation>Número de CFE.Dominio: INTEGER</documentation>
                          </annotation>
                        </element>
                        <element name="SerieCFE">
                          <annotation>
                            <documentation>Serie de CFE. Dominio: CHAR (20)</documentation>
                          </annotation>
                          <simpleType>
                            <restriction base="string">
                              <maxLength value="4"/>
                            </restriction>
                          </simpleType>
                        </element>
                      </all>
                    </complexType>
                  </element>
                </sequence>
              </complexType>
            </element>
          </all>
        </complexType>
      </element>
    </sequence>
  </complexType>
  <complexType name="salida">
    <sequence>
      <element maxOccurs="unbounded" minOccurs="1" name="Resultado">
        <complexType>
          <all>
            <element name="estado" type="int">
              <annotation>
                <documentation>0  - Grabado correctamente, 1 - Pre-grabado correctamente, 2 - Error al grabar, 3 - Error al procesar archivo</documentation>
              </annotation>
            </element>
            <element name="mensaje" type="string">
              <annotation>
                <documentation>Mensaje de error</documentation>
              </annotation>
            </element>
            <element name="identificador" type="int">
              <annotation>
                <documentation>Identificador de Mensaje</documentation>
              </annotation>
            </element>
          </all>
        </complexType>
      </element>
    </sequence>
  </complexType>
  <element name="procesarAlta" type="tns1:entrada"/>
  <element name="procesarAltaReturn" type="tns1:salida"/>
  <element name="procesarAltaDesatendido" type="tns1:entrada"/>
  <element name="procesarAltaDesatendidoReturn" type="tns1:salida"/>
</schema>
  </types>
  <message name="procesarAltaInput">
    <part element="xsd1:procesarAlta" name="body">
    </part>
  </message>
  <message name="procesarAltaDesatendidoInput">
    <part element="xsd1:procesarAltaDesatendido" name="body">
    </part>
  </message>
  <message name="procesarAltaDesatendidoOutput">
    <part element="xsd1:procesarAltaDesatendidoReturn" name="body">
    </part>
  </message>
  <message name="procesarAltaOutput">
    <part element="xsd1:procesarAltaReturn" name="body">
    </part>
  </message>
  <portType name="TAnulCFELocWSPortType">
    <operation name="procesarAlta">
      <input message="tns:procesarAltaInput" name="procesarAltaRequest">
    </input>
      <output message="tns:procesarAltaOutput" name="procesarAltaResponse">
    </output>
    </operation>
    <operation name="procesarAltaDesatendido">
      <input message="tns:procesarAltaDesatendidoInput" name="procesarAltaDesatendidoRequest">
    </input>
      <output message="tns:procesarAltaDesatendidoOutput" name="procesarAltaDesatendidoResponse">
    </output>
    </operation>
  </portType>
  <binding name="TAnulCFELocWSSoapBinding" type="tns:TAnulCFELocWSPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="procesarAlta">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="procesarAltaDesatendido">
      <soap:operation soapAction=""/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="TAnulCFELocWSService">
    <port binding="tns:TAnulCFELocWSSoapBinding" name="TAnulCFELocWSPort">
      <soap:address location="https://portal.plazaitaliashopping.com.uy/soap/portal/services/forms/v1.3/TAnulCFELocWS"/>
    </port>
  </service>
</definitions>