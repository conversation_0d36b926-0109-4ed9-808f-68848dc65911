# This file defines all application routes for auth domain
# doc: /doc/auth.apib
# ~~~~
POST    /api/v1/signin                             domains.auth.controllers.v1.AuthenticationController.signIn()
POST    /api/v1/signup                             domains.auth.controllers.v1.AuthenticationController.signUp()

POST	/api/v1/resetPassword				       domains.auth.controllers.v1.PasswordController.requestResetPassword()

GET     /api/v1/validateAccount			           domains.auth.controllers.v1.AccountsController.validateAccount(param1:String,param2:String)
GET     /api/v1/account                            domains.auth.controllers.v1.AccountsController.getAccount()
POST    /api/v1/resendValidationEmail/:userId      domains.auth.controllers.v1.AccountsController.resendValidationEmail(userId:Int)

POST    /api/v1/signout                            domains.auth.controllers.v1.AuthenticationController.signOut()

# Deprecated
GET	    /api/v1/resetPassword				       domains.auth.controllers.v1.PasswordController.requestReset(param1:String)
