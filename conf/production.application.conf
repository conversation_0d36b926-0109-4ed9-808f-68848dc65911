# This is the main configuration file for the application.
# ~~~~~

# Secret key
# ~~~~~
# The secret key is used to secure cryptographics functions.
#
# This must be changed for production, but we recommend not changing it in this file.
#
# See http://www.playframework.com/documentation/latest/ApplicationSecret for more details.
play.crypto.secret = "changeme"

# The application languages
# ~~~~~
play.i18n.langs = [ "en", "es" ]

# Router
# ~~~~~
# Define the Router object to use for this application.
# This router will be looked up first when the application is starting up,
# so make sure this is the entry point.
# Furthermore, it's assumed your route file is named properly.
# So for an application router like `my.application.Router`,
# you may need to define a router file `conf/my.application.routes`.
# Default to Routes in the root package (and conf/routes)
# play.http.router = my.application.Routes

# Database configuration
# ~~~~~
# You can declare as many datasources as you want.
# By convention, the default datasource is named `default`
#
db.default.url=${?JDBC_DATABASE_URL}
db.default.username=${?JDBC_DATABASE_USERNAME}
db.default.password=${?JDBC_DATABASE_PASSWORD}
db.default.jndiName=DefaultDS

#HikariCP
dbplugin=disabled
db.default.dataSourceClassName=com.mysql.jdbc.jdbc2.optional.MysqlDataSource
db.default.dataSource.user=${?JDBC_DATABASE_USERNAME}
db.default.dataSource.password=${?JDBC_DATABASE_PASSWORD}
db.default.dataSource.databaseName=${?JDBC_DATABASE_NAME}
db.default.dataSource.serverName=${?JDBC_DATABASE_SERVER}

db.default.maximumPoolSize=25
db.default.leakDetectionThreshold=15000

jpa.default=defaultPersistenceUnit

# Evolutions
# ~~~~~
# You can disable evolutions if needed
play.evolutions.enabled=false

# You can disable evolutions for a specific datasource if necessary
# play.evolutions.db.default.enabled=false


# Endpoints that don't require security checks
security.exceptions = "[]"

# Long form support
play.http.parser.maxDiskBuffer=100MB
parsers.anyContent.maxLength=100MB
play.http.parser.maxMemoryBuffer=20MB

# Play modules
# ~~~~~
# Modules are used to extend the functionality of Play applications.
play.modules.enabled += "global.ApplicationModule"
play.modules.enabled += "playconfig.ConfigModule"

#MQTT AWS BROKER IP
mqtt.broker.ip = "tcp://************:1883"

## payment getaways ##
payment_getaways.transactions.disabled = false

#MERCADO PAGO SETTINGS - produccion mercado pagos -
mercadopago.access.token = "APP_USR-7573715325607961-052710-aec2275fa11431841eba59ededcd1568-408269362"
mercadopago.client.id = "7573715325607961"
mercadopago.client.secret = "6csCXH9Ll9DljaXXmm00Vww9CNpelPbY"

# Bancard settings
bancard.keys.public = "4414GttxQgYJ1mIFMOjgugWixJnFSNDU"
bancard.keys.private = "InU$8ovGY.a8zp4Qc87ubZVjEX8MqJI5xxkEegdr"
bancard.webPayment.url = "https://vpos.infonet.com.py"
bancard.keys.secret = "xcTvLqAq3TCWCeJDeoGavs7UdBFY6NjkqZg9"

# Transact settings
transact = {
    lavomat = {
        keys.empcode = "LAVOM1"
        keys.termcode = "T00001"
        keys.emphash = "9A0929EF0430E2B075D8CD6A2422DA75"
        emulacion=false
    }
    lavamar = {
        keys.empcode = "FLYCA1"
        keys.termcode = "T00001"
        keys.emphash = "F25F887003B1CBFA297708359DC9B50F"
        emulacion=false
    }
}

# BambooPayment settings
bambooPayment.physicalAgent.keyword = "NMIDGYTSIWW="
bambooPayment.physicalAgent.username = "Lavomat_22WSProd"
bambooPayment.physicalAgent.customer.id = "6110"
bambooPayment.physicalAgent.customer.key = "2eXo3hZ3LQ1E+7kZWTKi5xJ9UJc6uYUm"
bambooPayment.webPayments.customer.key = "2eXo3hZ3LQ1E+7kZWTKi5xJ9UJc6uYUm"
bambooPayment.webPayments.customer.id = "6110"
bambooPayment.webPayments.url = "https://service.pagosweb.com.uy/v3.4/requestprocessor.aspx"

play.filters.cors {
  # allow all paths
  pathPrefixes = [
    "/api/",
    "/asst/",
    "/public-site/",
    "/totem/"
  ]
  # allow all origins (You can specify if you want)
  allowedOrigins = [
    "https://www.lavomat.com.uy",
    "https://lavomat.com.uy",
    "https://totem.lavomat.com.uy",
    "https://lavamar-totem.lavomat.com.uy",
    "https://assistant.lavomat.com.uy",
    "https://service.pagosweb.com.uy"
  ]
  allowedHttpMethods = ["GET", "POST", "PUT", "OPTIONS", "DELETE"]
  # allow all headers
  allowedHttpHeaders = null
 }

# Enviroment
api.env = "PROD"
api.baseUrl="https://app.lavomat.com.uy"

# Logger - Papertrail
papertrail.logger.env=production
papertrail.logger.host=logs6.papertrailapp.com
papertrail.logger.port=51618

# Assistant portal
assistant.url=${?ASSISTANT_URL}

# Coliving confirmation token
confirmation.token.url="/complete-signup?token="

# Public site base url
publicSite.baseUrl="https://www.lavomat.com.uy"

# Email
email.smtp.user="<EMAIL>"
email.smtp.password="1Nf0m4t1"
email.smtp.port=465
email.hostname="mail.lavomat.com.uy"
email.debug.enabled=true
email.debug.email=""

email.lavomat.admin="<EMAIL>"
email.lavomat.ceo="<EMAIL>"
email.lavomat.cto="<EMAIL>"
email.lavomat.asst="<EMAIL>"

# Google reCaptcha v3
google.reCaptcha.secret="6LdKDgskAAAAAIQ711OHqWHhRI5UGcjjhUnC-ECj"
google.reCaptcha.url="https://www.google.com/recaptcha/api/siteverify"

# Lavomat
lavomat.rut="217231170014"

# Plaza Italia SOAP
plazaItalia.enableNotification=true
plazaItalia.soap.notifySaleUrl="https://portal.plazaitaliashopping.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas?vista=RESP"
plazaItalia.soap.notifyRefundUrl="https://portal.plazaitaliashopping.com.uy/soap/portal/services/forms/v1.3/TAnulCFELocWS?vista=RESP"
plazaItalia.soap.user="217231170014"
plazaItalia.soap.password="Et5Zc.0Rtk"

# Totem
totem.booking.enabled=true

# Redis cache
play.modules.disabled += "play.api.cache.EhCacheModule"
play.modules.enabled += "services.cache.redis.RedisModule"

redis.host="lavomat-cache-wbxqn2.serverless.usw2.cache.amazonaws.com"
redis.port=6379
redis.enabled="true"

# Sicfe
sicfe = {
    lavomat = {
        url="http://ec2-44-225-209-58.us-west-2.compute.amazonaws.com/PROD/Services/sicfe.svc?singleWsdl"
        env="PROD"
        user="erp"
        pass="erp"
        tenant="lavoma"
        ruc="217231170014"
        companyName="LAVOMAT"
        commercialName="LAVOMAT S.R.L."
        phone="20351108"
        email="<EMAIL>"
    }
    lavamar = {
        url="https://services-prod-erps.cloud.sicfe.uy/sicfe.svc"
        env="PROD"
        user="erp_flycar"
        pass="ynWveuZP6AU3t7NR"
        tenant="flycar"
        ruc="220155820017"
        companyName="LAVAMAR"
        commercialName="FLY CARGAS SAS"
        phone="*********"
        email="<EMAIL>"
    }
}
