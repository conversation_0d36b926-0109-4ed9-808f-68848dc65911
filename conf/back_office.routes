# This file defines all application routes for back_office domain
# ~~~~

# Administrations
GET      /api/v1/administrations            domains.back_office.controllers.v1.AdministrationsController.list()
POST     /api/v1/administrations            domains.back_office.controllers.v1.AdministrationsController.create()
PUT      /api/v1/administrations/:id        domains.back_office.controllers.v1.AdministrationsController.update(id:Int)
DELETE   /api/v1/administrations/:id        domains.back_office.controllers.v1.AdministrationsController.delete(id:Int)

PUT     /api/v1/administrations/:id/user/assign/:userId/date/:date    domains.back_office.controllers.v1.administrations.UsersController.assignDebtCollector(id:Int, userId:Int, date:String)
PUT     /api/v1/administrations/:id/user/unassign                     domains.back_office.controllers.v1.administrations.UsersController.unassignDebtCollector(id:Int)

# Buildings
POST    /api/v1/buildings                            domains.back_office.controllers.v1.buildings.BuildingsController.createBuilding()
POST    /api/v1/buildings/:bid/upload-building       domains.back_office.controllers.v1.buildings.BuildingsController.uploadBuidling(bid:Int)
PUT     /api/v1/buildings/:bid                       domains.back_office.controllers.v1.buildings.BuildingsController.updateBuilding(bid:Int)
PUT     /api/v1/buildings/:id/disable-closure        domains.back_office.controllers.v1.buildings.BuildingsController.disableAdministrationClosure(id:Int)
PUT     /api/v1/buildings/:id/enable-closure         domains.back_office.controllers.v1.buildings.BuildingsController.enableAdministrationClosure(id:Int)
PUT     /api/v1/buildings/:id/disable-maintenance    domains.back_office.controllers.v1.buildings.BuildingsController.disableBuildingForMaintenance(id:Int)
PUT     /api/v1/buildings/:id/enable-maintenance     domains.back_office.controllers.v1.buildings.BuildingsController.enableBuildingForMaintenance(id:Int)
PATCH   /api/v1/buildings/:id/edit-closure-type      domains.back_office.controllers.v1.buildings.BuildingsController.editClosureType(id:Int)
GET     /api/v1/buildings/:id/units                  domains.back_office.controllers.v1.buildings.BuildingUnitsController.list(id:Int)
GET     /api/v1/buildings/:id/machines               domains.back_office.controllers.v1.buildings.BuildingMachinesController.list(id:Int)
GET     /api/v1/buildings/:id/first-use             domains.back_office.controllers.v1.buildings.BuildingsController.getFirstMachineUseByBuilding(id:Int)
GET     /api/v1/buildings/:id/oldest-keep-alive     domains.back_office.controllers.v1.buildings.BuildingsController.getOldestKeepAlive(id:Int)
PUT     /api/v1/buildings/:id/settings/payment-methods/pagos-web-split/enable    domains.back_office.controllers.v1.buildings.BuildingsController.enablePagosWebWithSplit(id:Int)
PUT     /api/v1/buildings/:id/settings/payment-methods/pagos-web-split/disable   domains.back_office.controllers.v1.buildings.BuildingsController.disablePagosWebWithSplit(id:Int)
PUT     /api/v1/buildings/:id/settings/payment-methods/red-pagos-split/enable    domains.back_office.controllers.v1.buildings.BuildingsController.enableRedPagosWithSplit(id:Int)
PUT     /api/v1/buildings/:id/settings/payment-methods/red-pagos-split/disable   domains.back_office.controllers.v1.buildings.BuildingsController.disableRedPagosWithSplit(id:Int)

# Cards
POST     /api/v1/cards                  domains.back_office.controllers.v1.cards.CardsController.create()
GET      /api/v1/cards/:id/events       domains.back_office.controllers.v1.cards.CardEventsController.list(id:Int)

# Machines
POST     /api/v1/machines               domains.back_office.controllers.v1.MachinesController.create()
GET      /api/v1/machines               domains.back_office.controllers.v1.MachinesController.list()
POST     /api/v1/maintenance/machine/:machineId/start               domains.back_office.controllers.v1.MaintenanceController.setMachineIntoMaintenance(machineId:Int)
POST     /api/v1/maintenance/machine/:machineId/interrupt           domains.back_office.controllers.v1.MaintenanceController.interruptMachineMaintenance(machineId:Int)

# Rates
GET      /api/v1/rates      domains.back_office.controllers.v1.RatesController.list()
POST     /api/v1/rates      domains.back_office.controllers.v1.RatesController.create()
PUT      /api/v1/rates/:id  domains.back_office.controllers.v1.RatesController.update(id:Int)

GET      /api/v1/rates/:id/rateEvents                       domains.back_office.controllers.v1.rates.RateEventsController.list(id:Int)
POST     /api/v1/rates/:rateId/rateEvents                   domains.back_office.controllers.v1.rates.RateEventsController.create(rateId:Int)
PUT      /api/v1/rates/:rateId/rateEvents/:id               domains.back_office.controllers.v1.rates.RateEventsController.update(rateId:Int, id:Int)
DELETE   /api/v1/rates/:rateId/rateEvents/:id               domains.back_office.controllers.v1.rates.RateEventsController.delete(rateId:Int, id:Int)
POST     /api/v1/rates/:rateId/rateEvents/notifyNewRate     domains.back_office.controllers.v1.rates.RateNotificationController.sendBuildingAdminNewRatePricesEmail(rateId:Int)

# Replacements
POST     /api/v1/replacements           domains.back_office.controllers.v1.ReplacementsController.create()

# Soap Dispensers
POST     /api/v1/dispensers                                     domains.back_office.controllers.v1.SoapDispensersController.create()
PUT      /api/v1/dispensers/:dispenserId                        domains.back_office.controllers.v1.SoapDispensersController.update(dispenserId:Int)
PUT      /api/v1/dispensers/:dispenserId/restartUses            domains.back_office.controllers.v1.SoapDispensersController.restartUses(dispenserId:Int)

# TinyUrl
# doc: /doc/api_v1_tinyUrls.apib
GET     /api/v1/tinyUrls                                        domains.back_office.controllers.v1.TinyUrlsController.list()
POST    /api/v1/tinyUrls                                        domains.back_office.controllers.v1.TinyUrlsController.create()
POST    /api/v1/tinyUrls/:count                                 domains.back_office.controllers.v1.TinyUrlsController.bulkCreation(count:Int)
PUT     /api/v1/tinyUrls/:id                                    domains.back_office.controllers.v1.TinyUrlsController.update(id:Int)

POST    /api/v1/tinyUrls/:token/machine/:machineId              domains.back_office.controllers.v1.tiny_urls.MachinesController.assign(token:String, machineId:Int)
DELETE  /api/v1/tinyUrls/:token/machine/:machineId              domains.back_office.controllers.v1.tiny_urls.MachinesController.unassign(token:String, machineId:Int)

# Tools
POST     /api/v1/tools                                          domains.back_office.controllers.v1.ToolsController.create()

# Transactions
GET     /api/v1/transactions                                    domains.back_office.controllers.v1.TransactionsController.list()

# Users
#   doc: /doc/api_v1_users.apib
GET      /api/v1/users                                          domains.back_office.controllers.v1.UsersController.list()
GET      /api/v1/users/technicians                              domains.back_office.controllers.v1.UsersController.listTechnicians()
GET      /api/v1/users/collectors                               domains.back_office.controllers.v1.UsersController.listCollectors()
GET      /api/v1/users/totem                                    domains.back_office.controllers.v1.UsersController.listTotemUsers()

POST     /api/v1/users                                          domains.back_office.controllers.v1.UsersController.create()
PUT      /api/v1/users/:id                                      domains.back_office.controllers.v1.UsersController.update(id:Int)
DELETE   /api/v1/users/:id                                      domains.back_office.controllers.v1.UsersController.delete(id:Int)

# Tools
POST     /api/v1/tools                                          domains.back_office.controllers.v1.ToolsController.create()

# Replacements
POST     /api/v1/replacements                                   domains.back_office.controllers.v1.ReplacementsController.create()

# Machines
POST     /api/v1/machines                                       domains.back_office.controllers.v1.MachinesController.create()
GET      /api/v1/machine/:id/history                            domains.back_office.controllers.v1.MachinesController.getMachineHistory(id:Int)

# Cards
POST     /api/v1/cards                                          domains.back_office.controllers.v1.cards.CardsController.create()

# Mappings
GET     /api/v1/totem/mappings                                  domains.back_office.controllers.v1.TotemsMappingsController.getMappings()
POST    /api/v1/totem/mappings/posTerminal                      domains.back_office.controllers.v1.TotemsMappingsController.createTotemPosTerminalMappings()
POST    /api/v1/totem/mappings/machine                          domains.back_office.controllers.v1.TotemsMappingsController.createTotemMachineMappings()
