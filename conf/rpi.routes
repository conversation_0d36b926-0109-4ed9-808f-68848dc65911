# This file defines all application routes for rpi domain
# path: /rpi/*
# ~~~~

#   spec: https://app.shortcut.com/lavomat/write/IkRvYyI6I3V1aWQgIjY0YWQ1NDZmLWQ2MDItNDA2OC05OWJiLWEyMjljMDAyMWU2YSI=
#   doc: /doc/rpi_v1.apib

## Booking
POST    /v1/booking/:id/received                                domains.rpi.controllers.v1.BookingController.received(id:String)

# Buildings
PUT     /v1/buildings/:bid/uses                                 controllers.BuildingsController.pushUses(bid:Int)
GET     /v1/buildings/:buildingId/rpi-count                     controllers.BuildingsController.getRpiCount(buildingId:Int)

# Machines
GET     /v1/activate/request/simple					            controllers.MachineController.activateMachineSimpleRequest(param1:String, param2:Int, param3:String)
POST    /v1/alive                                               domains.rpi.controllers.v1.MachinesController.alive()

# MachineUse
GET     /v1/machines/:machineId/machineUses/exist               domains.rpi.controllers.v1.machines.MachineUsesController.existsMachineUse(machineId:Int)

# Parts
GET     /v1/parts                                               controllers.PartsController.listParts(type : String ?= "ALL")

# Time
GET     /v1/conf                                                domains.rpi.controllers.v1.ConfigurationController.get()
