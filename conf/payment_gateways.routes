# This file defines all application routes for transactions
# doc: /doc/api_v1_transaction.apib
# ~~~~

### Shared operations ###

GET     /api/v1/transaction/reject     domains.payment_gateways.controllers.v1.transaction.CommonController.reject(param1:String, param2:String)

POST    /api/v1/transaction/create     domains.payment_gateways.controllers.v1.transaction.CreationController.creditTransaction()
POST    /api/v1/transaction/branding   domains.payment_gateways.controllers.v1.transaction.CreationController.brandingTransaction()

### Providers specific operations ###

# BackOffice
#   doc: TODO

POST    /api/v1/transaction/back-office        domains.payment_gateways.controllers.v1.transaction.BackOfficeController.create()

# Bamboo Payment
#   doc: /doc/api_v1_transaction_bambooPayment.apib

POST    /api/v1/transaction/bambooPayment/consulta         domains.payment_gateways.controllers.v1.transaction.bambooPayment.BambooPaymentController.create()
POST    /api/v1/transaction/bambooPayment/confirmarPago    domains.payment_gateways.controllers.v1.transaction.bambooPayment.BambooPaymentController.confirm()
POST    /api/v1/transaction/bambooPayment/anularPago       domains.payment_gateways.controllers.v1.transaction.bambooPayment.BambooPaymentController.reject()

# Bancard
#   doc: TODO

POST    /api/v1/transaction/buySingleConfirm       domains.payment_gateways.controllers.v1.transaction.BancardController.confirm()
POST    /api/v1/transaction/buySingleRollback      domains.payment_gateways.controllers.v1.transaction.BancardController.rollback()

# Mercado Pago
#   doc: TODO

GET     /api/v1/transaction/bind       domains.payment_gateways.controllers.v1.transaction.MercadoPagoController.bindAdministration(state:Int, code:String)
POST    /api/v1/transaction/bot        domains.payment_gateways.controllers.v1.transaction.MercadoPagoController.buyCreditsBot()
GET     /api/v1/transaction/confirm    domains.payment_gateways.controllers.v1.transaction.MercadoPagoController.confirm(param1:String)
POST    /api/v1/transaction/notify     domains.payment_gateways.controllers.v1.transaction.MercadoPagoController.notifyTx(topic:String, id:String)

GET     /api/v1/qr/order        domains.payment_gateways.controllers.v1.qr.MercadoPagoController.create(pos:String)
POST    /api/v1/qr/notify       domains.payment_gateways.controllers.v1.qr.MercadoPagoController.notifyTx(topic:String, id:String)

POST 	/api/v1/mercado-pago/store          domains.payment_gateways.controllers.v1.mercado_pago.StoreController.create()
POST    /api/v1/mercado-pago/store/pos      domains.payment_gateways.controllers.v1.mercado_pago.StoreController.createPoS()


# Pagos Web
#   doc: /doc/api_v1_transaction_pw.apib.apib

POST    /api/v1/transaction/pw/confirm     domains.payment_gateways.controllers.v1.transaction.pw.PagosWebContoller.confirm()
POST    /api/v1/transaction/pw/notify      domains.payment_gateways.controllers.v1.transaction.pw.PagosWebContoller.notifyTx()

# TransAct
#   doc: /doc/api_v1_transaction_transact.apib

POST    /api/v1/transaction/transact                domains.payment_gateways.controllers.v1.transaction.transact.TransActController.create()
POST    /api/v1/transaction/transact/:id/confirm    domains.payment_gateways.controllers.v1.transaction.transact.TransActController.confirm(id:String)
POST    /api/v1/transaction/transact/:id/reject     domains.payment_gateways.controllers.v1.transaction.transact.TransActController.reject(id:String)
GET     /api/v1/transaction/transact/status         domains.payment_gateways.controllers.v1.transaction.transact.TransActController.status()
