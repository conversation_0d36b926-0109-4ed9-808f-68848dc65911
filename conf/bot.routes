# This file defines all application routes for bot domain
# path: /bot/*
# doc: /doc/bot_v1.apib
# ~~~~

POST    /v1/buildings                                          domains.bot.controllers.v1.BuildingsController.getBuildingsDetailsByLocation()
GET     /v1/buildings/laundromat                               domains.bot.controllers.v1.BuildingsController.getLaundromatDetails()
POST    /v1/buildings/:buildingId                              domains.bot.controllers.v1.CardRequestsController.requestCard(buildingId:Int)
POST    /v1/buildings/:buildingId/cardAcceptance               domains.bot.controllers.v1.CardRequestsController.requestCardAcceptance(buildingId:Int)

GET     /v1/cards/:uid                                         domains.bot.controllers.v1.CardController.getDetails(uid:String)
POST    /v1/cards/:uid/getUserAssignmentStatus                 domains.bot.controllers.v1.CardController.getUserAssignmentStatus(uid:String)
POST    /v1/cards/:uid/history                                 domains.bot.controllers.v1.MachineUsesController.getHistory(uid:String)

POST    /v1/machines/:machineId/activation/:cardUid            domains.bot.controllers.v1.MachineActivationController.activation(machineId:Int, cardUid:String)
POST    /v1/machines/:machineId/activation/:cardUid/confirm    domains.bot.controllers.v1.MachineActivationController.confirmActivation(machineId:Int, cardUid:String)
GET     /v1/machines/:num/:uid                                 domains.bot.controllers.v1.MachinesController.getStatus(num:Int, uid:String)

POST    /v1/support/app                                        domains.bot.controllers.v1.SupportController.reportAppIssue()
POST    /v1/support/contact                                    domains.bot.controllers.v1.SupportController.contact()
POST    /v1/support/sales                                      domains.bot.controllers.v1.SupportController.requestSalesContact()
POST    /v1/support/technician                                 domains.bot.controllers.v1.SupportController.requestATechnician()

GET     /v1/users/:email                                       domains.bot.controllers.v1.UsersController.getDetails(email:String)
