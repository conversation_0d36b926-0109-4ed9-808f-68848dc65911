# This file defines all application routes for public site
# path: /public-site/*
# ~~~~

# Public Site
#   doc: /doc/public_site_v1.apib

POST    /v1/signin                                      domains.auth.controllers.v1.AuthenticationController.signInBuildingAdminWithRecaptcha()

GET     /v1/administrations/buildings/coliving          controllers.public_site.v1.BuildingController.listColivingBuildings()
POST    /v1/administrations/buildings/:bid/users        controllers.public_site.v1.ColivingUsersController.createColivingUser(bid:Int)
GET     /v1/administrations/buildings/:bid/users        controllers.public_site.v1.ColivingUsersController.listColivingBuildingUsers(bid:Int)
DELETE  /v1/administrations/buildings/:bid/users/:uid   controllers.public_site.v1.ColivingUsersController.deleteColivingUser(bid:Int, uid:Int)
GET     /v1/administrations/:aid/buildings              controllers.AdministrationsController.listAdministrationBuildings(aid:Int)

# Cards
#   doc: /doc/public_site_v1_cards.apib
GET     /v1/cards/:uid/methods                          domains.public_site.controllers.v1.CardsController.paymentMethods(uid:String)

POST    /v1/users/confirm                               controllers.public_site.v1.ColivingUsersController.confirmUser()
POST    /v1/users/verify-token                          controllers.public_site.v1.ColivingUsersController.verifyToken()
POST    /v1/users/:uid/resend-confirmation              controllers.public_site.v1.ColivingUsersController.resendConfirmationEmail(uid:Int)
PUT     /v1/users/delete                                domains.public_site.controllers.v1.UsersController.delete()

GET     /v1/reports/sendReportByUid                     controllers.WebController.sendReportByUid()

POST    /v1/confirmResetPassword                        domains.auth.controllers.v1.PasswordController.confirmReset()
