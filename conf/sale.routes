# This file defines all application routes for sale
# path: /sale/*
# ~~~~

# Public Site
#   doc: /doc/sale_v1.apib

POST     /v1/notify/:buildingId/bill/:billId                domains.sale_notifier.controllers.v1.SaleNotifierController.notifyBillSale(buildingId:Int, billId:Int)
POST     /v1/notify/:buildingId/use/:useId                  domains.sale_notifier.controllers.v1.SaleNotifierController.notifyMachineUseSale(buildingId:Int, useId:Int)
GET      /v1/notify/:buildingId/bulk                        domains.sale_notifier.controllers.v1.SaleNotifierController.bulkNotifySales(buildingId:Int)

POST     /v1/refund/:buildingId/bill/:billId                domains.sale_notifier.controllers.v1.SaleNotifierController.notifyBillRefund(buildingId:Int, billId:Int)
POST     /v1/refund/:buildingId/use/:useId                  domains.sale_notifier.controllers.v1.SaleNotifierController.notifyUseRefund(buildingId:Int, useId:Int)
