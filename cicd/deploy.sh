#!/bin/bash
SERVER_GROUP=$1
TAG_VERSION=$2
#prod,preprod,sandbox

BASE=/lavomat
CONFIG=$BASE/config
GIT_REPO=$BASE/git
BUILDS=$BASE/builds
DEPLOYS=$BASE/deploys
BUILD_NAME=lm_app-$TAG_VERSION

echo "Starting deploy of $SERVER_GROUP $TAG_VERSION"
rm -r $DEPLOYS/$SERVER_GROUP/*

/usr/bin/unzip $BUILDS/$BUILD_NAME.zip -d  $DEPLOYS/$SERVER_GROUP

# ENVIRONMENT CONFIG
cp $GIT_REPO/lm_app/config/application.conf.$SERVER_GROUP $DEPLOYS/$SERVER_GROUP/$BUILD_NAME/application.conf
mkdir $DEPLOYS/$SERVER_GROUP/$BUILD_NAME/conf

echo "Updating conf in jar file..."
jar uf $DEPLOYS/$SERVER_GROUP/$BUILD_NAME/lib/lm_app.$BUILD_NAME.jar -C $DEPLOYS/$SERVER_GROUP/$BUILD_NAME application.conf

echo "Deploy done: push config and restart the app for the changes to take effect"
