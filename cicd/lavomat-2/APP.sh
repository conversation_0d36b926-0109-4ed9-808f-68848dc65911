#!/bin/sh

CMD=$1


start()
{
	echo "Starting Lavomat app..."
	# lavomat-2
	echo 'nohup /lavomat/deploys/prod/$(ls /lavomat/deploys/prod)/bin/lm_app -J-javaagent:/opt/newrelic/newrelic.jar -J-Xms2048M -J-Xmx2048M -J-XX:+UseG1GC -J-XX:+ExplicitGCInvokesConcurrent -J-XX:MaxGCPauseMillis=500 -J-XX:+HeapDumpOnOutOfMemoryError -J-XX:HeapDumpPath="/var/log/lavomat/memory_dump.hprof" >> /var/log/lavomat/lmat.log 2>&1 &' | sudo bash
}

stop()
{
	echo "Stoping Lavomat app..."

	APP_PID=$(ps -ef | grep java | grep -v grep | grep lm_app | awk '{print $2}')
	sudo kill $APP_PID
	sleep 3

	cant=$(ps ax | grep java | grep lavomat/deploys | grep lm_app | grep -v grep | wc -l)

	if [ "$cant" -gt 0 ]; then
                $0 stop
        else
                echo "Lavomat app stopped"
        fi
}

case "$CMD" in
    start)
        start "start"
        ;;
    stop)
        stop "stop"
        ;;
    restart)
        stop "stop"
	start "start"
	;;
    status)
	status "status"
	;;
    *)
        echo "Usage $0 {start|stop|restart|status}"

        RETVAL=1
esac
