#!/bin/bash

TAG_VERSION=$1
BASE=/lavomat
SCRIPTS=$BASE/scripts
CONFIG=$BASE/config
PROJECT_DIR=$BASE/git/lm_app
BUILDS=$BASE/builds
BUILD_LOG=$BUILDS/build.log
BUILD_NAME=lm_app-$TAG_VERSION.zip

echo "Building $TAG_VERSION"

cd $PROJECT_DIR
# Move out from tag
git stash && git checkout main
# Remove local tags and fetch them
git tag -d $(git tag -l) && git fetch --tags --prune

if [ -z "$TAG_VERSION" ]; then
  echo "Error: No tag version provided. Usage: ./build.sh <tag-version>"
  exit 1
fi

if git tag | grep "$TAG_VERSION"; then
  echo "Tag $TAG_VERSION exists. Continuing..."
else
  echo "Tag $TAG_VERSION does not exist. Exiting..."
  exit 1
fi

git checkout $TAG_VERSION

sed 's/version\s\+:=\s\+\".*\"/version\ :=\ \"'$TAG_VERSION'\"/' $PROJECT_DIR/build.sbt > $PROJECT_DIR/build.sbt.new
mv $PROJECT_DIR/build.sbt.new $PROJECT_DIR/build.sbt

npm install

bower install

grunt dist

cd $PROJECT_DIR

activator update

activator reload

activator dist

mv $PROJECT_DIR/target/universal/$BUILD_NAME $BUILDS/.

echo "Build file: $BUILDS/$BUILD_NAME"

echo "$(date) Build $TAG_VERSION" >> $BUILD_LOG

echo "Build $TAG_VERSION done"
