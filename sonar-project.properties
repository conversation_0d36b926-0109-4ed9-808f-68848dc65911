# Project identification
sonar.projectKey=lm_app
sonar.projectName=Lavomat Application
sonar.projectVersion=1.0-SNAPSHOT

# Source configuration
sonar.sources=app
sonar.tests=test
sonar.java.source=1.8
sonar.java.target=1.8

# Binaries
sonar.java.binaries=target/scala-2.11/classes
sonar.java.test.binaries=target/scala-2.11/test-classes

# Libraries
sonar.java.libraries=target/scala-2.11/lib/*.jar,lib/**/*.jar

# Exclusions
sonar.exclusions=**/target/**,**/node_modules/**,**/public/bower_components/**,**/public/dist/**,app/org/**,app/com/**,**/*.scala.html

# Coverage
sonar.coverage.exclusions=**/test/**,**/models/**,**/dto/**

# Language-specific properties
sonar.sourceEncoding=UTF-8

# Rules
sonar.java.checkstyle.reportPaths=target/checkstyle-result.xml
