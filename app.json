{"name": "LAVOMAT BackOffice and API", "buildpacks": [{"url": "hero<PERSON>/nodejs"}, {"url": "heroku/scala"}, {"url": "heroku-community/cli"}], "env": {"ENV": {"required": true}, "JDBC_DATABASE_NAME": {"required": true}, "JDBC_DATABASE_PASSWORD": {"required": true}, "JDBC_DATABASE_SERVER": {"required": true}, "JDBC_DATABASE_URL": {"required": true}, "JDBC_DATABASE_USERNAME": {"required": true}, "PORT": {"required": true}, "HEROKU_APP_NAME": {"required": true}, "HEROKU_API_KEY": {"required": true}}, "environments": {"review": {"env": {"ENV": "sandbox", "JDBC_DATABASE_NAME": "lavomat", "JDBC_DATABASE_SERVER": "lavomat-sandbox.cq3icpiag9t0.us-west-2.rds.amazonaws.com", "JDBC_DATABASE_URL": "*****************************************************************************", "JDBC_DATABASE_USERNAME": "lavomat", "PORT": "80"}, "scripts": {"postdeploy": "bash ./review-apps-postdeploy.sh"}}}}