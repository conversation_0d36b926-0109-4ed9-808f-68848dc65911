0 * * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" "https://app.lavomat.com.uy/sale/v1/notify/1177/bulk"
30 3 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/coliving/invalidate-confirmation-tokens"
1 3 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/coliving/recharge-prepaid-uses"
0 12 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" "https://app.lavomat.com.uy/api/v1/administrations/close" >> /var/log/lavomat/cierres.log 2>&1
15 11 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/rates/building/admin/send-new-rate-email"
30 11 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/rates/lavomat/admin/send-upcoming-rate-expiration-email"
30 1 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/exchange-rate/ui/update"
45 1 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/exchange-rate/usd/update"
*/30 * * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/surge-pricing/c30"
0 * * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/surge-pricing/c1"
0 9 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/preventive-maintenance/hydrate"
0 12 * * * curl --header "X-LM-AUTH-TOKEN: a49Xd57b-79X5-4eXd-8fXe-e8aX3d0X237X" -X POST "https://app.lavomat.com.uy/tasks/v1/reporting/laundromats"
