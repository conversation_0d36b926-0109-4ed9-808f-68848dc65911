FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo de accesorios

### Listado de Accesorios [GET /api/v1/branding/items]

+ Clientes: BackOffice, Public Site

+ Propósito: Listar accessorios existentes

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + items: (array) - lista de facturas
            + item: (object)
                + id: 758 (number) - número identificador de un accesorio
                + name: Bo<PERSON><PERSON> de lavandería (string) - nombre del accesorio
                + description: <strong>Práctico, lindo y funcional</strong>, este bolso permite llevar... (string) - descripción del accesorio que puede contener HTML dentro
                + price: 300 (number) - precio del accesorio para los clientes
                + reference: washing-bag (string) - referencia para la navegación en el Public Site dentro de la URL (http://localhost:3000/accessories#washing-bag)
                + imageName: washing-bag (string) - nombre de la imagen que se encuentra en el Public Site

### Crear Accesorios [POST /api/v1/branding/items]

+ Clientes: BackOffice

+ Propósito: Crear un nuevo accessorio

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + description: <strong>Práctico, lindo y funcional</strong>, este bolso permite llevar... (string, required) - descripción del accesorio que puede contener HTML dentro
        + imageName: washing-bag (string, required) - nombre de la imagen que se encuentra en el Public Site
        + name: Bolso de lavandería (string, required) - nombre del accesorio
        + rateId: 1 (number, required) - número identificador de un Rate
        + reference: washing-bag (string, required) - referencia para la navegación en el Public Site dentro de la URL (http://localhost:3000/accessories#washing-bag)


+ Response 200 (application/json)


### Editar Accesorios [PUT /api/v1/branding/items/{id}]

+ Clientes: BackOffice

+ Propósito: Editar un accessorio existente

+ Parameters
    + id: 1 (number, required) - identificador del accesorio

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + description: <strong>Práctico, lindo y funcional</strong>, este bolso permite llevar... (string) - descripción del accesorio que puede contener HTML dentro
        + imageName: washing-bag (string) - nombre de la imagen que se encuentra en el Public Site
        + name: Bolso de lavandería (string) - nombre del accesorio
        + rateId: 1 (number) - número identificador de un Rate
        + reference: washing-bag (string) - referencia para la navegación en el Public Site dentro de la URL (http://localhost:3000/accessories#washing-bag)


+ Response 200 (application/json)


### Eliminar Accesorios [DELETE /api/v1/branding/items/{id}]

+ Clientes: BackOffice

+ Propósito: Eliminar un accessorio existente

+ Parameters
    + id: 1 (number, required) - identificador del accesorio


+ Response 200 (application/json)
