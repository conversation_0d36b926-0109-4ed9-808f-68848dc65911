FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para la integracion con TransAct

## Transactions [/api/v1/transaction/transact]

### Create Transaction [POST /api/v1/transaction/transact]

+ Clientes: Totem

+ Proposito: Recibir las transacciones que son creadas desde el Totem

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
        + serial: 111AAAA0A010 (string, required) - Serial de la una máquina, la cual se desea utilizar
        + rut: 101010101101 (number) - RUT identificador de la empresa a la cual se desea asociar la factura
        + customerName: Lavomat SRL (string) - Nombre de la empresa a la cual se desea asociar la factura. Requerdio en caso de indicar el RUT.


+ Response 200 (application/json)

    when the request was valid

    + Attributes(object)
        + Transaction_id: 101 (number) - Identificador de la transaccion
        + Transaction_amount: 100 (number) - Monto por el cual se va a realizar la operación.
        + Transaction_currency: "UYU" (string) - Moneda en la cual se realiza la transaccion
        + Transaction_token: "1XXX0XXX-111X-01X1-00X1-11X1XX0XX1XX" (string) - Token identificador para la transaccion dentro de TransAct


+ Response 400 (application/json)

    when the machine was busy

    + Attributes(object)
        + result_code: 153 (number) - identificador del error
        + result_message: MACHINE_BUSY (string) - código interno de resultado de la operación
