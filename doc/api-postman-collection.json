{"info": {"_postman_id": "211d1c29-b7b9-4972-8ee3-7266b34cf647", "name": "LM-API", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "AUTH", "item": [{"name": "/api/v1/signin", "id": "c6e4fc7e-0abb-444c-8606-69658459e835", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"emailAddress\": \"<EMAIL>\",\n    \"password\": \"Password01\",\n    \"rememberMe\": false\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/signin"}, "response": []}, {"name": "/api/v1/signout", "id": "2c5ba170-bc66-49e2-919f-1718e2fd4e23", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/api/v1/signout?all-devices=true", "host": ["{{API_URL}}"], "path": ["api", "v1", "signout"], "query": [{"key": "all-devices", "value": "true", "type": "text"}]}}, "response": []}, {"name": "/api/v1/signup", "id": "8e70aceb-a658-42cf-9228-7b174fbd7211", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"emailAddress\":\"<EMAIL>\", \n\t\"password\":\"lavomat1\",\n\t\"name\":\"<PERSON> Verón<PERSON>\",\n\t\"lastname\":\"<PERSON>rre<PERSON>\",\n\t\"country\":\"Uruguay\",\n\t\"mainAddress\":\"<PERSON> A de Herrera 3270\",\n\t\"phone\":\"*********\",\n\t\"fecha_nacimiento\": \"08-09-1990\"\n\n}"}, "url": "{{API_URL}}/api/v1/signup"}, "response": []}, {"name": "/api/v1/validateAccount", "id": "44f6abb0-accc-4b48-8313-99973cc4c8ef", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/validateAccount?param1=49&param2=6aae1967-cc5b-442e-8e08-91a1003e906f", "host": ["{{API_URL}}"], "path": ["api", "v1", "validateAccount"], "query": [{"key": "param1", "value": "49"}, {"key": "param2", "value": "6aae1967-cc5b-442e-8e08-91a1003e906f"}]}}, "response": []}], "id": "03e27113-1726-4f65-9450-30a215e16419"}, {"name": "/api/v1", "item": [{"name": "/activate", "item": [{"name": "/request/simple - RPI", "id": "0a1ae83c-300b-49ce-8e44-f2838a4561ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "apikey", "apikey": {"value": "{{LM_AUTH_TOKEN__RPI}}", "key": "X-LM-AUTH-TOKEN"}}, "method": "GET", "header": [{"key": "Accept-Language", "value": "es", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/activate/request/simple?param1=0x00000040&param2=1002&param3={{machine-serial}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "activate", "request", "simple"], "query": [{"key": "param1", "value": "0x00000040", "description": "card.uuid"}, {"key": "param2", "value": "1002", "description": "building.id"}, {"key": "param3", "value": "{{machine-serial}}", "description": "machine.serial"}]}}, "response": []}, {"name": "/request - Mobile", "id": "4281a682-114d-4862-9c81-27728b830013", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "apikey", "apikey": {"value": "{{LM_AUTH_TOKEN__USER}}", "key": "X-LM-AUTH-TOKEN"}}, "method": "GET", "header": [{"key": "Accept-Language", "value": "es", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "520d0f62-31b0-427f-9fe0-c4ef3072a221", "type": "default"}], "url": {"raw": "{{API_URL}}/api/v1/activate/request?param1=0x00000040&param2=1002&param3={{machine-serial}}&param4=false&param5=<EMAIL>&param6=1&param7=03df25c845d460b...f99cc3edcb63a85ea2ef2&param8=APP&param9=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "activate", "request"], "query": [{"key": "param1", "value": "0x00000040", "description": "uid"}, {"key": "param2", "value": "1002", "description": "b.id"}, {"key": "param3", "value": "{{machine-serial}}", "description": "serial"}, {"key": "param4", "value": "false", "description": "mqtt"}, {"key": "param5", "value": "<EMAIL>", "description": "email"}, {"key": "param6", "value": "1"}, {"key": "param7", "value": "03df25c845d460b...f99cc3edcb63a85ea2ef2"}, {"key": "param8", "value": "APP", "description": "channel"}, {"key": "param9", "value": "1"}]}}, "response": []}, {"name": "/request - Mobile, Totem, MercadoPago's QR, Wpp bot Copy", "id": "2b6be910-1f3d-4fcb-ba72-8e3fa8a45b34", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept-Language", "value": "es", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "520d0f62-31b0-427f-9fe0-c4ef3072a221", "type": "default"}], "url": {"raw": "{{API_URL}}/api/v1/activate/request?param1=0x00000040&param2=1002&param3={{machine-serial}}&param4=false&param5=afmi.470%2540gmail.com&param6=1&param7=03df25c845d460b...f99cc3edcb63a85ea2ef2&param8=APP&param9=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "activate", "request"], "query": [{"key": "param1", "value": "0x00000040", "description": "uid"}, {"key": "param2", "value": "1002", "description": "b.id"}, {"key": "param3", "value": "{{machine-serial}}", "description": "serial"}, {"key": "param4", "value": "false", "description": "mqtt"}, {"key": "param5", "value": "afmi.470%2540gmail.com", "description": "email"}, {"key": "param6", "value": "1"}, {"key": "param7", "value": "03df25c845d460b...f99cc3edcb63a85ea2ef2"}, {"key": "param8", "value": "APP", "description": "channel"}, {"key": "param9", "value": "1"}]}}, "response": []}, {"name": "/request - TOTEM", "event": [{"listen": "test", "script": {"id": "7a02496c-a044-4bd2-9d25-d77867c7f4bf", "exec": [""], "type": "text/javascript"}}], "id": "f75affcd-b5dd-4f99-84b3-f92114ef9781", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "92bbd519-ec26-4620-b927-75c08a8beec7"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/activate/request?param1=null&param2=1002&param3={{machine-serial}}&param4=true&param5=<EMAIL>&param6=0&param7=null&param8=TOTEM&param9={{transaction-id}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "activate", "request"], "query": [{"key": "param1", "value": "null", "description": "card uid"}, {"key": "param2", "value": "1002", "description": "buulding id "}, {"key": "param3", "value": "{{machine-serial}}", "description": "machine serial"}, {"key": "param4", "value": "true", "description": "generate mqqt messaje?"}, {"key": "param5", "value": "<EMAIL>", "description": "email"}, {"key": "param6", "value": "0", "description": "groupd id"}, {"key": "param7", "value": "null", "description": "expo device token"}, {"key": "param8", "value": "TOTEM", "description": "channel"}, {"key": "param9", "value": "{{transaction-id}}", "description": "transaction id"}]}}, "response": []}], "id": "500feb24-cac7-4032-9db2-d14254b0a1b5", "event": [{"listen": "prerequest", "script": {"id": "a454b414-b8d7-4517-a48b-341131416d38", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "64f17499-57e5-49d6-aa13-744b70526948", "type": "text/javascript", "exec": [""]}}]}, {"name": "/administrations", "item": [{"name": "/:admin-id/close - cierre de prepagos", "id": "9b7befb9-f447-4194-ba56-f73f84fb6765", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "00648f53-042b-4d28-bcdb-a81c42e82006", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/administrations/:admin-id/close", "host": ["{{API_URL}}"], "path": ["api", "v1", "administrations", ":admin-id", "close"], "variable": [{"key": "admin-id", "value": "60"}]}}, "response": []}, {"name": "/:admin-id/buildings", "id": "31671301-5e37-4581-a028-159281ffefdd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "0110e967-24d9-4956-a48e-a682afbd9b87"}], "url": {"raw": "https://app-sandbox.lavomat.com.uy/api/v1/administrations/:admin-id/buildings", "protocol": "https", "host": ["app-sandbox", "lavomat", "com", "uy"], "path": ["api", "v1", "administrations", ":admin-id", "buildings"], "variable": [{"key": "admin-id", "value": "36"}]}}, "response": []}, {"name": "/", "id": "c34e49ca-ab91-4df5-94ee-53bcb149474c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/api/v1/administrations"}, "response": []}], "id": "ce92f61e-d4e4-4036-a0a3-8f59fc437902"}, {"name": "/bills", "item": [{"name": "/ - list filtered", "id": "58d925bc-4296-4c48-b856-2b2234826d47", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/bills?administration=3&perPage=50&page=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "bills"], "query": [{"key": "administration", "value": "3"}, {"key": "perPage", "value": "50"}, {"key": "page", "value": "1"}]}}, "response": []}], "id": "a978013b-7057-4c65-b971-362c029cd4c6"}, {"name": "/branding", "item": [{"name": "/items", "id": "5623f924-a5a8-4326-9d42-25bd3e774383", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/branding/items?level=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "branding", "items"], "query": [{"key": "level", "value": "1", "type": "text"}]}}, "response": []}, {"name": "/items", "id": "e88b8df4-4fd4-4ff3-b5d7-f1605b8ffaee", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reference\": \"test\",\n    \"imageName\": \"test\",\n    \"rateId\": 31,\n    \"name\": \"test\",\n    \"description\": \"<div><!--block-->test&nbsp;</div>\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/branding/items"}, "response": []}, {"name": "/items", "id": "3c6f3a36-13d4-43d5-bf7b-813d4cb82a4a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"reference\": \"test-mod\",\n    \"imageName\": \"test-mod\",\n    \"rateId\": 31,\n    \"name\": \"test-mod\",\n    \"description\": \"<div><!--block-->test-mod</div>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/api/v1/branding/items/:id", "host": ["{{API_URL}}"], "path": ["api", "v1", "branding", "items", ":id"], "variable": [{"key": "id", "value": "5"}]}}, "response": []}, {"name": "/items", "id": "0a34ecff-bb72-48dd-8a65-592d74faeb0b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/branding/items/:id", "host": ["{{API_URL}}"], "path": ["api", "v1", "branding", "items", ":id"], "variable": [{"key": "id", "value": "5"}]}}, "response": []}, {"name": "/requests", "id": "9651b49c-7142-4616-bf57-71c7015b584f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2"}], "url": "{{API_URL}}/api/v1/branding/requests"}, "response": []}, {"name": "/requests/:request_id/delivered", "id": "62416079-c7b8-4c42-9785-3c416b8a4b78", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "1bb4935e-8d42-487c-95df-7fdbc7ce97f2"}], "url": {"raw": "{{API_URL}}/api/v1/branding/requests/:request_id/delivered", "host": ["{{API_URL}}"], "path": ["api", "v1", "branding", "requests", ":request_id", "delivered"], "variable": [{"id": "b3994c98-86da-47b1-9a25-90ba9c68a67c", "key": "request_id", "value": "3"}]}}, "response": []}], "id": "94f29875-be7e-41d1-b433-7ab9bbda781e"}, {"name": "/buildings", "item": [{"name": "/:bid/rpi-count", "id": "738863d0-1ff7-42ae-bb68-2a8e20689d12", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/buildings/:bid/rpi-count", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":bid", "rpi-count"], "variable": [{"id": "17068d44-8f43-4bb7-916a-02969a6051ab", "key": "bid", "value": "62"}]}}, "response": []}, {"name": "/:buildingId/uses - RPI - Push Uses", "id": "a48b0421-dd0b-4c26-9db4-de967dcd4135", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "7c65bc23-2226-4a86-ae2d-2dd1c71a4306", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"uses\": [\r\n        {\r\n            \"uid\": \"NULL\",\r\n            \"timestamp\": 1687357549,\r\n            \"energy_consumption\": 0,\r\n            \"result\": \"6\",\r\n            \"machine_id\": \"dsvsvs8s9v6s9v\",\r\n            \"water_consumption\": 0,\r\n            \"Transaction_id\": 2750\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{API_URL}}/api/v1/buildings/:buildingId/uses", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":buildingId", "uses"], "variable": [{"id": "ec0e6d33-d22b-427d-a8a1-013f40ab62a0", "key": "buildingId", "value": "1"}]}}, "response": []}, {"name": "/:buildingId/uses - RPI - Push Uses - Wrong result ERROR", "id": "18447498-68da-494f-9691-1912c58046ce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "7c65bc23-2226-4a86-ae2d-2dd1c71a4306", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"uses\": [\r\n        {\r\n            \"uid\": \"888b5d6b3bdf\",\r\n            \"timestamp\": 1600733104,\r\n            \"energy_consumption\": 0,\r\n            \"result\": \"1\",\r\n            \"machine_id\": \"309KWJU1E076\",\r\n            \"water_consumption\": 0\r\n        },\r\n        {\r\n            \"uid\": \"6853c7ca5493\",\r\n            \"timestamp\": 1600736704,\r\n            \"energy_consumption\": 0,\r\n            \"result\": \"1\",\r\n            \"machine_id\": \"309KWJU1E076\",\r\n            \"water_consumption\": 0\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{API_URL}}/api/v1/buildings/:buildingId/uses", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":buildingId", "uses"], "variable": [{"id": "7ba5eeb5-539a-4e7d-9e6e-b6e7148340bb", "key": "buildingId", "value": "1"}]}}, "response": []}, {"name": "/:buildingId/uses - RPI - Push Uses - ERROR", "id": "edf9cbb9-6cc5-47a2-87cf-6ef7753195dc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "7c65bc23-2226-4a86-ae2d-2dd1c71a4306", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"uses\": [\r\n        {\r\n            \"uid\": \"0x63e005b3\",\r\n            \"timestamp\": 1548198506,\r\n            \"energy_consumption\": 0,\r\n            \"result\": \"3\",\r\n            \"machine_id\": \"serialprueba1\",\r\n            \"water_consumption\": 0\r\n        },\r\n        {\r\n            \"uid\": \"0xf054c6de\",\r\n            \"timestamp\": 1548284906,\r\n            \"energy_consumption\": 0,\r\n            \"result\": \"0\",\r\n            \"machine_id\": \"serialprueba1\",\r\n            \"water_consumption\": 0\r\n        }\r\n    ]\r\n}"}, "url": {"raw": "{{API_URL}}/api/v1/buildings/:buildingId/uses", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":buildingId", "uses"], "variable": [{"id": "8a736900-1d04-4dbe-9ac8-381de0ab6d2c", "key": "buildingId", "value": "1"}]}}, "response": []}, {"name": "/:id/enable-closure", "id": "43fcf8ba-2a29-485e-a5d8-e5128541ad6e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "url": {"raw": "{{API_URL}}/api/v1/buildings/:id/enable-closure", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":id", "enable-closure"], "variable": [{"key": "id", "value": "1002"}]}}, "response": []}, {"name": "/:id/disable-closure", "id": "e8205f1c-73a7-4fdb-b6e6-c0fba5a795cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "url": {"raw": "{{API_URL}}/api/v1/buildings/:id/disable-closure ", "host": ["{{API_URL}}"], "path": ["api", "v1", "buildings", ":id", "disable-closure "], "variable": [{"id": "ef2a2ed3-0637-41f9-a102-083cbcc50581", "key": "id", "value": "1002"}]}}, "response": []}], "id": "fbd9228b-9e0e-4041-bc4b-02dcbbe4070b"}, {"name": "/card", "item": [{"name": "/balance - by uid", "id": "ccb9dadc-94f0-4172-8038-361c9c121c6a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "025c9a4f-20e3-49d0-b9a4-1bde78ac24c1", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/card/balance?param1={{uid}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "card", "balance"], "query": [{"key": "param1", "value": "{{uid}}"}]}}, "response": []}, {"name": "/balance - by virtual uid", "id": "43955960-01c7-4440-a234-f64c6a02822c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "025c9a4f-20e3-49d0-b9a4-1bde78ac24c1", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/card/balance?param1={{virtual-uid}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "card", "balance"], "query": [{"key": "param1", "value": "{{virtual-uid}}"}]}}, "response": []}, {"name": "/uses/:uid", "id": "4a0d1b11-6ae3-42e5-ae83-0c71df65fe3d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "92bbd519-ec26-4620-b927-75c08a8beec7"}], "body": {"mode": "raw", "raw": "\n\n"}, "url": {"raw": "{{API_URL}}/api/v1/card/uses/:uid", "host": ["{{API_URL}}"], "path": ["api", "v1", "card", "uses", ":uid"], "variable": [{"key": "uid", "value": "e917143b"}]}}, "response": []}, {"name": "/transactions/:uid", "id": "9946a059-047e-4ee6-b2a5-c83458dd541b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "92bbd519-ec26-4620-b927-75c08a8beec7"}], "url": {"raw": "{{API_URL}}/api/v1/card/transactions/:uid", "host": ["{{API_URL}}"], "path": ["api", "v1", "card", "transactions", ":uid"], "variable": [{"key": "uid", "value": "f972273b"}]}}, "response": []}], "id": "95d588aa-a259-45f0-8c56-74f6d452f0a7"}, {"name": "/group", "item": [{"name": "/card - Mobile -  AddCardToGroup", "id": "bdf70e8d-7258-4d3a-8444-ad9ce610645a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"emailAddress\":\"<EMAIL>\",\n\t\"cardNumber\": \"0x84cb57bc\",\n\t\"groupId\": \"null\",\n}"}, "url": "{{API_URL}}/api/v1/group/card"}, "response": []}, {"name": "/user - Mobile - AddUserToGroup", "id": "d53a7e68-254e-4465-bc51-28afe956ce91", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"emailAddress\":\"<EMAIL>\",\n\t\"inviteEmailAddress\":\"<EMAIL>\",\n\t\"groupId\": \"null\",\n\t\"cardNumber\":\"null\"\n}"}, "url": "{{API_URL}}/api/v1/group/user"}, "response": []}, {"name": "/ - get group by user", "id": "901afca9-00f2-48f0-be37-6ff0c17e3ecf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/group?param1=<EMAIL>", "host": ["{{API_URL}}"], "path": ["api", "v1", "group"], "query": [{"key": "param1", "value": "<EMAIL>"}]}}, "response": []}, {"name": "/main - Get Main User Groups", "id": "77542fd1-6b21-4a9a-b764-eab21e5ff63e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/group/main?param1=<EMAIL>", "host": ["{{API_URL}}"], "path": ["api", "v1", "group", "main"], "query": [{"key": "param1", "value": "<EMAIL>"}]}}, "response": []}], "id": "6a73f3b3-53af-40ae-b8f2-c1afe4a5ed85"}, {"name": "/laundromats", "item": [{"name": "/ - list by user", "id": "5a706398-0105-425a-af23-12e6ccbbf84b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "92bbd519-ec26-4620-b927-75c08a8beec7"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/laundromats?userid=40", "host": ["{{API_URL}}"], "path": ["api", "v1", "laundromats"], "query": [{"key": "userid", "value": "40"}]}}, "response": []}], "id": "9302eb40-1bf9-4240-9a40-d694ccdef591"}, {"name": "/machine", "item": [{"name": "/status/: building-id - Totem, Mobile", "id": "de77fb2d-7869-4845-9d41-ae51fc96ac18", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/machine/status/:building-id", "host": ["{{API_URL}}"], "path": ["api", "v1", "machine", "status", ":building-id"], "variable": [{"key": "building-id", "value": "1002"}]}}, "response": []}, {"name": "/:machine-id - Delete Booking", "id": "dac7bda8-f4f0-4c97-a09c-dfb06d682ca9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "url": {"raw": "http://localhost:9000/api/v1/machine/:machine-id", "protocol": "http", "host": ["localhost"], "port": "9000", "path": ["api", "v1", "machine", ":machine-id"], "variable": [{"key": "machine-id", "value": "1"}]}}, "response": []}, {"name": "/booking", "id": "2a2f7c74-14f0-4fe9-a7a3-000c6f1cf079", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"emailAddress\": \"<EMAIL>\",\n    \"group_id\": \"null\",\n    \"uuid\":\"\",\n    \"washers_count\": \"1\",\n    \"drying_count\": \"0\"\n}"}, "url": "{{API_URL}}/api/v1/machine/booking"}, "response": []}], "id": "fc6cea2f-8c9b-4df2-831d-e1a933c6525b"}, {"name": "/mercado-pago/store", "item": [{"name": "/ - MP - Create Store", "id": "27a6f9e3-f257-4484-834b-bdfb2ed9b3c4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"building_id\": \"1268\",\n    \"street_name\": \"<PERSON>v. <PERSON>\",\n    \"street_number\": \"3365\",\n    \"city_name\": \"Bolivar\",\n    \"latitude\": -34.868938,\n    \"longitude\": -56.169788,\n    \"zip_code\": \"11600\",\n    \"details\": \"Local de Nuevo Centro\"\n}\n"}, "url": "{{API_URL}}/api/v1/mercado-pago/store"}, "response": []}, {"name": "/pos - MP - Create POS for QR", "id": "5c30bbf8-e31a-4e0a-882d-ecfc70789cf9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"building_id\": \"1268\",\n    \"machine_serial\": \"310KWMK47027\",\n    \"details\":\"Maquina 1\"\n}\n"}, "url": "{{API_URL}}/api/v1/mercado-pago/store/pos"}, "response": []}], "id": "50896a19-2df3-4362-8d3d-e5d436a3b26c"}, {"name": "/parts", "item": [{"name": "list card for RPIs", "id": "673db15f-1e3f-4a02-b5a9-4ca6c21a710f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/parts?type=CARD&building=1177&includeMaster=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "parts"], "query": [{"key": "type", "value": "CARD"}, {"key": "building", "value": "1177"}, {"key": "includeMaster", "value": "1"}, {"key": "level", "value": "0", "disabled": true}]}}, "response": []}], "id": "6cc0cae0-4d08-4b4f-a0ea-d7f60766c34c"}, {"name": "/qr", "item": [{"name": "/order - MP's QR - Create POS Order", "id": "ac16797f-e650-4ee0-94e3-2228042eb282", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \n   \n}\n\n  \n   "}, "url": {"raw": "{{API_URL}}/api/v1/qr/order?pos=806KWNM5P471", "host": ["{{API_URL}}"], "path": ["api", "v1", "qr", "order"], "query": [{"key": "pos", "value": "806KWNM5P471"}]}}, "response": []}, {"name": "/notify - MP's QR -  Notify QR Payment", "id": "f6c8eaef-1217-4e75-8cc2-70c5a5cae706", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{API_URL}}/api/v1/qr/notify?topic=payment&id=1111", "host": ["{{API_URL}}"], "path": ["api", "v1", "qr", "notify"], "query": [{"key": "topic", "value": "payment"}, {"key": "id", "value": "1111"}]}}, "response": []}], "id": "c4369ee3-1969-4898-8e52-bd243643afd7"}, {"name": "/rates", "item": [{"name": "/ - list by level", "id": "3fa93c8c-4c76-430a-b3a0-1b1315dcff89", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/rates?level={{level}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "rates"], "query": [{"key": "level", "value": "{{level}}"}]}}, "response": []}], "id": "ef84f1de-fb88-4b3d-896a-cc66bc2014c4"}, {"name": "/reports", "item": [{"name": "/sendReportByUid - Public site", "id": "1c04de12-1b27-4c38-9819-eee039a41f7b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/reports/sendReportByUid?user={{email-to-deliver-mails}}&card=0x63e005b3", "host": ["{{API_URL}}"], "path": ["api", "v1", "reports", "sendReportByUid"], "query": [{"key": "user", "value": "{{email-to-deliver-mails}}"}, {"key": "card", "value": "{{report-uid}}", "disabled": true}, {"key": "card", "value": "0x63e005b3", "description": "PREPAID INACTIVE"}]}}, "response": []}, {"name": "/transactions - Reportes adm transacciones", "id": "ef5c02b0-a5ab-4bd6-8d81-b1dd7a6c958c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "02bb1956-f66e-438b-be06-a017f0473d06"}], "url": {"raw": "http://localhost:9000/api/v1/reports/transactions?from=1575169200&to=1577761200&building=54", "protocol": "http", "host": ["localhost"], "port": "9000", "path": ["api", "v1", "reports", "transactions"], "query": [{"key": "from", "value": "1575169200"}, {"key": "to", "value": "1577761200"}, {"key": "building", "value": "54"}]}}, "response": []}, {"name": "/uses - BO", "id": "72fa8630-e538-4b7d-8bef-dfa1e09ee2ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "01ec4c97-3ea9-41f9-be6d-04962efbc482"}], "body": {"mode": "raw", "raw": "\n\n"}, "url": {"raw": "{{API_URL}}/api/v1/reports/uses?building=1034&from=1577847600&to=1578538800&building=1034&perPage=50&page=1", "host": ["{{API_URL}}"], "path": ["api", "v1", "reports", "uses"], "query": [{"key": "building", "value": "1034"}, {"key": "from", "value": "1577847600"}, {"key": "to", "value": "1578538800"}, {"key": "building", "value": "1034"}, {"key": "perPage", "value": "50"}, {"key": "page", "value": "1"}]}}, "response": []}], "id": "b54b4c4e-c704-4fee-a4c4-7374e8d1052a"}, {"name": "/store", "item": [{"name": "/ - MP - Create store for QR", "id": "fcb9d30f-a16e-4da0-9339-1b2157f817f7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"building_id\": \"1177\",\n    \"street_name\": \"Av. Italia\",\n    \"street_number\":\"4250\",\n    \"latitude\":\"-34.88774\",\n    \"longitude\":\"-56.11789\",\n    \"zip_code\":\"11400\",\n    \"details\":\"Local ubicado dentro del Plaza Italia\"\n}\n"}, "url": "{{API_URL}}/api/v1/store"}, "response": []}], "id": "a3e00f5b-3df7-4d05-a0f0-ef556e06c36e"}, {"name": "/totem", "item": [{"name": "/order/send - TOTEM - send bill by email", "id": "e10a0af9-d6e5-4c2b-86e7-1451223b0be7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"id\": \"{{transact-id}}\",\n\t\"email\" : \"<EMAIL>\"\n}"}, "url": "{{API_URL}}/api/v1/totem/order/send"}, "response": []}, {"name": "/order - TOTEM - Create transaction", "id": "fb55a30f-4a5d-44aa-bfe3-fde87abcb41a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"serial\": \"{{machine-serial}}\"\n}\n\n  \t\n   "}, "url": "{{API_URL}}/api/v1/totem/order"}, "response": []}, {"name": "/order/:transaction-id/confirm", "id": "c9d90cb8-b7e7-4d10-8d54-650a73bd6ea9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/pdf", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/totem/order/:transaction-id/confirm", "host": ["{{API_URL}}"], "path": ["api", "v1", "totem", "order", ":transaction-id", "confirm"], "variable": [{"key": "transaction-id", "value": "{{transaction-id}}"}]}}, "response": []}], "id": "a9ba7354-b90e-463c-82f4-6ed1ba811a2e"}, {"name": "/transaction", "item": [{"name": "/bambooPayment", "item": [{"name": "/consulta", "id": "9e207079-3c12-4b12-a7a3-3a5ea256f931", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"username\": \"{{bamboo-payment-user}}\",\n  \"password\": \"{{bamboo-payment-password}}\",\n  \"tipoDocumento\": 5,\n  \"documento\": \"{{bamboo-payment-uid}}\",\n  \"redFisica\": \"REDPAGOS\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/bambooPayment/consulta"}, "response": []}, {"name": "/confirmarPago", "id": "c5c18796-206e-426d-8e24-9a2b1be6c8c1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"username\": \"Test@2022Lavomat\",\n  \"password\": \"27yXcDfAxEU=\",\n  \"tipoDocumento\": 5,\n  \"documento\": \"479462a3\",\n  \"redFisica\": \"REDPAGOS\",\n  \"moneda\": 858,\n  \"importeTotal\": 10000,\n  \"NroOrden\": 564927,\n  \"detallesPago\": [\n    {\n      \"IdItem\": 753,\n      \"Moneda\": 858,\n      \"ImportePagado\": 10000\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/bambooPayment/confirmarPago"}, "response": []}, {"name": "/confirmarPago weird json", "id": "24a38458-f333-438c-a38a-1b2341370ad4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"Test@2022Lavomat\",\n    \"password\": \"27yXcDfAxEU=\",\n    \"documento\": \"c4cc2add\",\n    \"tipoDocumento\": \"5\",\n    \"moneda\": \"858\",\n    \"importeTotal\": \"1000\",\n    \"detallesPago\": \"[{\\\"IdItem\\\":753,\\\"Moneda\\\":858,\\\"ImportePagado\\\":1000.0}]\",\n    \"NroOrden\": \"100300\",\n    \"redFisica\": \"REDPAGOS\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/bambooPayment/confirmarPago"}, "response": []}, {"name": "/anularPago", "id": "f4b392b1-4976-4ee6-8ee2-540d38a9540b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"username\": \"Test@2022Lavomat\",\n  \"password\": \"27yXcDfAxEU=\",\n  \"tipoDocumento\": 5,\n  \"documento\": \"479462a3\",\n  \"redFisica\": \"REDPAGOS\",\n  \"NroOrden\": 564927\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/bambooPayment/anularPago"}, "response": []}], "id": "e77a359c-9979-4280-9570-47f6a4858e50", "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"id": "c3439b91-085f-45ab-bd82-2586d2f28c4d", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "d89d60c7-9aaa-4d9f-b05a-ecee07486b7a", "type": "text/javascript", "exec": [""]}}]}, {"name": "/transact", "item": [{"name": "/ - create transaction. -TOTEM", "id": "05189902-c678-429b-9e30-58210c4f982b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "0de542f0-f80c-4736-ab81-497e8569ed7f", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"serial\": \"{{machine-serial}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/transact"}, "response": []}, {"name": "/:transaction-id/confirm - TOTEM", "id": "4b010a2c-3026-4532-b6dd-2478a1562bd9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/transaction/transact/:transaction-id/confirm", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "transact", ":transaction-id", "confirm"], "variable": [{"key": "transaction-id", "value": "{{transact-id}}"}]}}, "response": []}, {"name": "/:transaction_id/reject - TOTEM", "id": "1ef6d3e5-b2ba-4b7b-a8bb-9604c996ccea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"message\": \"OPTIONAL\",\n\t\"transaction_token\": \"{{transact-token}}\"\n}"}, "url": {"raw": "{{API_URL}}/api/v1/transaction/transact/:transaction_id/reject", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "transact", ":transaction_id", "reject"], "variable": [{"key": "transaction_id", "value": "{{transact-id}}"}]}}, "response": []}, {"name": "/status - TOTEM", "id": "741efc02-4e17-45cc-8a07-24c3a16d2efd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json", "disabled": true}], "url": {"raw": "{{API_URL}}/api/v1/transaction/transact/status?transaction_token={{transact-token}}", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "transact", "status"], "query": [{"key": "transaction_token", "value": "{{transact-token}}"}]}}, "response": []}], "id": "c89d7e66-f438-4695-8f31-576b08dd45ab"}, {"name": "/branding", "item": [{"name": "/ - branding transaction through MP", "id": "54740942-fa3e-4659-8cd7-01e408ad8f9d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"branding_item_id\": 1,\r\n    \"email\": \"<EMAIL>\",\r\n    \"quantity\": 5,\r\n    \"channel\": \"MercadoPagos\",\r\n    \"currency\": \"858\",\r\n    \"phone\": \"099505050\",\r\n    \"address\": \"My direccion\",\r\n    \"name\": \"<PERSON><PERSON><PERSON>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/branding"}, "response": []}, {"name": "/ - branding transaction through PWeb", "id": "7ded1641-22af-4aab-942e-213607e1f766", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"branding_item_id\": 1,\r\n    \"email\": \"<EMAIL>\",\r\n    \"quantity\": 5,\r\n    \"currency\": \"858\",\r\n    \"phone\": \"099505050\",\r\n    \"address\": \"My direccion 8756\",\r\n    \"name\": \"<PERSON><PERSON><PERSON>\",\r\n    \"channel\": \"PagosWeb\",\r\n    \"paymentMethod\": \"1\",\r\n    \"coutas\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/branding"}, "response": []}], "id": "fc6631c8-8845-4f7b-a869-9b548a4081c3"}, {"name": "/pw", "item": [], "id": "d5024219-8ed1-40ea-a3ff-3966502a2f5d"}, {"name": "/create -  MP - credit transation", "id": "6e47fab0-c23a-4db8-b92e-2841208dd395", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 200,\n    \"channel\": \"MercadoPagos\",\n    \"currency\": \"858\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON><PERSON><PERSON>\",\n    \"rut\": \"\",\n    \"uid\": \"0xa4f1f564\"\n}"}, "url": "{{API_URL}}/api/v1/transaction/create"}, "response": []}, {"name": "/reject -  MP", "id": "0d0a942e-7e61-4792-b8fb-5bdb51acefed", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 200,\n    \"channel\": \"MercadoPagos\",\n    \"currency\": \"858\",\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON><PERSON><PERSON>\",\n    \"rut\": \"\",\n    \"uid\": \"0xa4f1f564\"\n}"}, "url": {"raw": "{{API_URL}}/api/v1/transaction/reject?param1=c3381135-19a9-488f-849a-7f7c58110ccc&param2=MercadoPagos", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "reject"], "query": [{"key": "param1", "value": "c3381135-19a9-488f-849a-7f7c58110ccc"}, {"key": "param2", "value": "MercadoPagos"}, {"key": "collection_id", "value": "null", "disabled": true}, {"key": "collection_status", "value": "null", "disabled": true}, {"key": "payment_id", "value": "null", "disabled": true}, {"key": "status", "value": "null", "disabled": true}, {"key": "external_reference", "value": "Transaction_c3381135-19a9-488f-849a-7f7c58110ccc", "disabled": true}, {"key": "payment_type", "value": "null", "disabled": true}, {"key": "merchant_order_id", "value": "null", "disabled": true}, {"key": "preference_id", "value": "*********-899cce18-cf71-4982-a8d8-31362865a2a1", "disabled": true}, {"key": "site_id", "value": "MLU", "disabled": true}, {"key": "processing_mode", "value": "aggregator", "disabled": true}, {"key": "merchant_account_id", "value": "null", "disabled": true}]}}, "response": []}, {"name": "/notify - MP - credit transaction", "id": "ccdb116d-5273-4989-b1bd-ebf7b26ad2a3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"resource\": \"https://api.mercadolibre.com/merchant_orders/{{transaction-id}}\",\n    \"topic\": \"merchant_order\"\n}"}, "url": {"raw": "{{API_URL}}/api/v1/transaction/notify?id={{mp-id}}&topic=merchant_order", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "notify"], "query": [{"key": "id", "value": "{{mp-id}}", "description": "{id:**********, topic:merchant_order}"}, {"key": "topic", "value": "merchant_order"}]}}, "response": []}, {"name": "/buySingleConfirm - BANCARD - Confirm Transaction", "id": "766d3417-51da-4934-9a02-59532a3fdc28", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"operation\":\n\t{\n\t\t\"shop_process_id\": \"{{transaction-id}}\",\n\t\t\"response_code\": \"0\"\n\t}\n}"}, "url": "{{API_URL}}/api/v1/transaction/buySingleConfirm"}, "response": []}, {"name": "/reject  - BANCARD - Reject Transaction", "id": "7ad9c6bc-eed1-4c9a-91b5-edea2a80190a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/transaction/reject?param1=35&param2=Bancard", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "reject"], "query": [{"key": "param1", "value": "35"}, {"key": "param2", "value": "Bancard"}]}}, "response": []}, {"name": "/buySingleRollback - BANCARD - Rollback Transaction", "id": "79f274fd-9257-4ccf-b8b7-434e1dc24eeb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"operation\":\n\t{\n\t\t\"shop_process_id\": \"423\"\n\t}\n}"}, "url": "{{API_URL}}/api/v1/transaction/buySingleRollback"}, "response": []}, {"name": "/status/:num", "id": "261a3561-19e2-4a03-9fc8-77166f52404f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"name\": \"<PERSON><PERSON><PERSON>\",\n    \"amount\": 200,\n    \"uid\": \"0009579259\"\n}"}, "url": {"raw": "{{API_URL}}/api/v1/transaction/status/:num", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "status", ":num"], "variable": [{"key": "num", "value": "{{transaction-id}}"}]}}, "response": []}, {"name": "/internal - Mobile", "id": "e69bb60b-a032-4e34-9d55-9817c973c930", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"uid_sender\": \"0x747035bc\",\r\n    \"uid_receiver\": \"0xa4f1f564\",\r\n    \"amount\": \"200\",\r\n    \"currency\": \"UYU\",\r\n    \"details\": \"553\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/internal"}, "response": []}, {"name": "/create - PagosWeb - credit transaction", "id": "0ca4321d-6ee1-4c95-8cd8-cdb75d4cf6c0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"branding_item_id\": 1,\r\n    \"email\": \"<EMAIL>\",\r\n    \"quantity\": 5,\r\n    \"currency\": \"858\",\r\n    \"phone\": \"099505050\",\r\n    \"address\": \"My direccion 8756\",\r\n    \"name\": \"<PERSON><PERSON><PERSON>\",\r\n    \"channel\": \"PagosWeb\",\r\n    \"paymentMethod\": \"1\",\r\n    \"coutas\": 1,\r\n    \"amount\": 730,\r\n    \"uid\": \"6853c7ca5493\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/transaction/create"}, "response": []}, {"name": "/create - MP - Mobile user", "id": "00a25698-eef0-426f-9cd4-0759d403fbc5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"name\": \"Veronica Arreche\",\n    \"currency\": \"858\",\n    \"amount\": \"10\",\n    \"uid\": \"hddf99d9\",\n    \"channel\": \"MercadoPagos\",\n    \"rut\":\"\"\n}"}, "url": "{{API_URL}}/api/v1/transaction/create"}, "response": []}, {"name": "/internal - Share Credits between users", "id": "a23c22f0-1a0e-4843-949f-daa092c4f275", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"uid_sender\": \"0x2496ebd7\",\n    \"uid_receiver\": \"0xd4225abc\",\n    \"currency\": \"858\",\n    \"amount\": \"100\",\n    \"details\": \"transaction 1 test\"\n}"}, "url": "{{API_URL}}/api/v1/transaction/internal"}, "response": []}, {"name": "/bind", "id": "bd6fbc57-b64a-4df2-8eba-2b22224dc9cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{API_URL}}/api/v1/transaction/bind?adm=3&code=TG-5c71b840de6cb700061edf27-402020864&state=sjbfhs", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "bind"], "query": [{"key": "adm", "value": "3"}, {"key": "code", "value": "TG-5c71b840de6cb700061edf27-402020864"}, {"key": "state", "value": "sjbfhs", "type": "text"}]}}, "response": []}, {"name": "/external - RedPagos - [DEPRECATED]", "id": "6b1feec3-4a86-4061-ac3c-aad66f85f7f5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": \"300\",\n    \"service_id\": \"e917143b\",\n    \"currency\": \"858\",\n    \"paymentGatewayId\": \"ertyuiopkjhgfdsaqwer\",\n    \"authorizationResult\":\"Operación authorizada\"\n}\n\n\n           "}, "url": {"raw": "{{API_URL}}/api/v1/transaction/external", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "external"], "query": [{"key": "Content-type ", "value": "", "disabled": true}]}}, "response": []}, {"name": "/confirm", "id": "89b48617-7a21-4629-8c16-948615fa5837", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "92bbd519-ec26-4620-b927-75c08a8beec7"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/transaction/confirm?param1=1484", "host": ["{{API_URL}}"], "path": ["api", "v1", "transaction", "confirm"], "query": [{"key": "param1", "value": "1484"}]}}, "response": []}], "id": "6be57f1e-a13d-4d55-aac5-d498c8e4e299", "auth": {"type": "<PERSON><PERSON><PERSON>"}, "event": [{"listen": "prerequest", "script": {"id": "638257ac-c4bd-44eb-8022-bef3e55dc0e2", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "96a3dfec-0265-46d1-8cb5-3f8a2b7ed594", "type": "text/javascript", "exec": [""]}}]}, {"name": "/users", "item": [{"name": "/accredit", "id": "96bc4959-d2b6-4019-a3f1-445bbd5c78f9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"emailAddress\": \"<EMAIL>\",\n    \"uuid\": \"0x74ede459\"\n}"}, "url": "{{API_URL}}/api/v1/users/accredit"}, "response": []}, {"name": "/accredit/check - Mobile", "id": "dfd55d8a-8a88-4ed7-be9c-debc0d0a79a9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{API_URL}}/api/v1/users/accredit/check?param1=<EMAIL>", "host": ["{{API_URL}}"], "path": ["api", "v1", "users", "accredit", "check"], "query": [{"key": "param1", "value": "<EMAIL>"}]}}, "response": []}, {"name": "/support/info - Public Site - Support message", "id": "d90ea14a-fbae-4360-890f-9329b807b7e8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n\t     \"key_msg\" : \"contact_info\", \n\t\t \"line1\" : \"Verónica Arreche\", \n\t\t \"emailAddress\" : \"<EMAIL>\", \n\t\t \"line2\" : \"\", \n\t\t \"line3\" : \"\", \n\t\t \"bodytxt\" : \"Escribo para pobar el envio de contacto\" \n}"}, "url": "{{API_URL}}/api/v1/users/support/info"}, "response": []}, {"name": "/support - Mobile", "id": "42f02555-f127-45fa-9bdb-6268a6a32646", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "5b7ed9bc-fb74-4b9a-8ff1-db677d17015f", "type": "text", "disabled": true}], "url": {"raw": "{{API_URL}}/api/v1/users/support?param1=new_card&param2=0x44eee444&param3=<EMAIL>", "host": ["{{API_URL}}"], "path": ["api", "v1", "users", "support"], "query": [{"key": "param1", "value": "new_card"}, {"key": "param2", "value": "0x44eee444"}, {"key": "param3", "value": "<EMAIL>"}]}}, "response": []}, {"name": "/password\t- udpate password", "id": "3b4079ff-6f2f-4adc-9c45-f07ceba8bbd6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"newPassword\": \"nueva newPassword\",\n    \"emailAddress\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/users/password"}, "response": []}, {"name": "/ - list", "id": "07045c99-6afa-4418-af72-3d817f3a5f58", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/api/v1/users"}, "response": []}, {"name": "/collectors", "id": "95ecbb31-53ad-4c1c-a0c4-39757c8c7663", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/api/v1/users/collectors"}, "response": []}], "id": "89c12948-0fa3-429c-b246-9b22cdd8c932"}, {"name": "/utilities", "item": [{"name": "/run-task", "id": "6af358b5-7ff4-4b3f-b42a-1e34e846f2db", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "d11ec629-c675-415a-95f7-1eadc7d263c5", "description": "X-LM-AUTH-TOKEN: d11ec629-c675-415a-95f7-1eadc7d263c5", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/utilities/run-task"}, "response": []}, {"name": "/mqtt-message", "id": "41d9acc9-0203-4a2a-95fe-37b175ff21ca", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n    \"buildingId\": 1,\n    \"serialNumber\": \"\",\n    \"json\": \"{}\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/utilities/mqtt-message"}, "response": []}, {"name": "/test", "id": "f61507a2-0738-43bb-8237-fba3570164bf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/api/v1/utilities/test"}, "response": []}], "id": "cedfdeba-2bfe-45f1-b498-afff7a468aa2"}, {"name": "/tinyUrls", "item": [{"name": "/ - list", "id": "58835a83-627f-4592-b6bd-075fa223e678", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/api/v1/tinyUrls?level=2", "host": ["{{API_URL}}"], "path": ["api", "v1", "tinyUrls"], "query": [{"key": "level", "value": "2"}]}}, "response": []}, {"name": "/ - create", "id": "7e57dbee-ec00-4494-8a46-24d128c3ec49", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"destinationUrl\": \"https://assistant.lavomat.com.uy/alamos-carrasco-30eb1750\",\n    \"isActive\": false,\n    \"description\": \"Esto es una url de prueba\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/tinyUrls"}, "response": []}, {"name": "/:token/machine/:machineId - assign to a machine", "id": "259fee95-ef57-428c-914f-c2d54072e480", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{API_URL}}/api/v1/tinyUrls/:token/machine/:machineId", "host": ["{{API_URL}}"], "path": ["api", "v1", "tinyUrls", ":token", "machine", ":machineId"], "variable": [{"key": "token", "value": "wreUAFO5Az"}, {"key": "machineId", "value": "1"}]}}, "response": []}, {"name": "/:token/machine/:machineId - unassign to a machine", "id": "285af072-cdeb-496d-996f-a7da8a978175", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{API_URL}}/api/v1/tinyUrls/:token/machine/:machineId", "host": ["{{API_URL}}"], "path": ["api", "v1", "tinyUrls", ":token", "machine", ":machineId"], "variable": [{"key": "token", "value": "wreUAFO5Az"}, {"key": "machineId", "value": "1"}]}}, "response": []}, {"name": "/:count - create bulk", "id": "b05ca8a7-ebd9-46b0-aac2-f963f0cccc8a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{API_URL}}/api/v1/tinyUrls/:count", "host": ["{{API_URL}}"], "path": ["api", "v1", "tinyUrls", ":count"], "variable": [{"key": "count", "value": "2"}]}}, "response": []}, {"name": "/ - update", "id": "9f5527f9-5d1f-4ed6-8913-6126c2d402c5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"destinationUrl\": \"https://assistant.lavomat.com.uy/alamos-carrasco-30eb1750\",\n    \"isActive\": true,\n    \"description\": \"Esto es una url de prueba\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/api/v1/tinyUrls/:id", "host": ["{{API_URL}}"], "path": ["api", "v1", "tinyUrls", ":id"], "variable": [{"key": "id", "value": "5"}]}}, "response": []}], "id": "eb7f5dfa-bef1-4394-9c4e-001d0fe48700"}, {"name": "/alive", "id": "058425a9-6e4a-4b46-aebe-0857d1e2d968", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "X-LM-AUTH-TOKEN", "value": "0c325c0e-79ab-4659-8be5-2c1d88fc2133", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"pending_uses\": 0,\r\n    \"fw_version\": \"1.0.10\",\r\n    \"public_ip\": \"************\",\r\n    \"private_ip\": \"************* \",\r\n    \"serial\": \"309KWBY1X277\",\r\n    \"port\": \"3000\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/api/v1/alive"}, "response": []}, {"name": "/alive - prod error", "id": "43d844d9-c9a6-4f32-afe6-aefaac72c00f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "b38f17ca-93a8-435c-938d-91bee00468c4", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"pending_uses\": 10,\r\n    \"fw_version\": \"1.0.9\",\r\n    \"_public_ip\": \"***********\",\r\n    \"private_ip\": \"*************\",\r\n    \"serial\": \"512KWSB5M710\",\r\n    \"port\": \"3000\",\r\n    \"public_ip\": \"<!DOCTYPE html><html>  <head><meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1\\\"><meta charset=\\\"utf-8\\\"><title>Application Error</title><style media=\\\"screen\\\">  html,body,iframe {margin: 0;padding: 0;  }  html,body {height: 100%;overflow: hidden;  }  iframe {width: 100%;height: 100%;border: 0;  }</style>  </head>  <body><iframe src=\\\"//www.herokucdn.com/error-pages/application-error.html\\\"></iframe>  </body></html>\"\r\n}"}, "url": "{{API_URL}}/api/v1"}, "response": []}, {"name": "/ping", "event": [{"listen": "test", "script": {"id": "99df3ada-6ac3-4dc6-aafa-795c24540026", "exec": [" pm.test(\"Body matches string\", function () {", "    pm.expect(pm.response.text()).to.include(\"Running!\");", "});"], "type": "text/javascript"}}], "id": "42891cec-baab-46ba-a7b6-7e41f86d6e32", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": "{{API_URL}}/api/v1/ping"}, "response": []}, {"name": "/ping", "id": "990a5755-63fb-4aed-a7d9-6887bf862b5f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": "{{API_URL}}/api/v1/ping"}, "response": []}, {"name": "/rollback - ANULACION DE FACTURAS EMITIDAS", "id": "441274a0-53a3-4925-9c63-31c1fc9b8714", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "type": "text", "value": "02bb1956-f66e-438b-be06-a017f0473d06"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{API_URL}}/api/v1/rollback?buildingid=123&total_amount=29073&total_uses=417&serie=A&nro=1631&isEfactura=false&cantCards=0", "host": ["{{API_URL}}"], "path": ["api", "v1", "rollback"], "query": [{"key": "buildingid", "value": "123"}, {"key": "total_amount", "value": "29073"}, {"key": "total_uses", "value": "417"}, {"key": "serie", "value": "A"}, {"key": "nro", "value": "1631"}, {"key": "isEfactura", "value": "false"}, {"key": "cantCards", "value": "0"}]}}, "response": []}], "id": "1e9a0e43-905e-4725-81bf-f2c9a05f5e2b"}, {"name": "/bot/v1", "item": [{"name": "/cards", "item": [{"name": "/:card_uid/getUserAssignmentStatus", "id": "42b577af-6952-4103-8aef-88ad2f3688a5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\" }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/bot/v1/cards/:card_uid/getUserAssignmentStatus", "host": ["{{API_URL}}"], "path": ["bot", "v1", "cards", ":card_uid", "getUserAssignmentStatus"], "variable": [{"id": "2436a1ed-fae5-46ca-b097-b8d6c561a6fc", "key": "card_uid", "value": "0xA6CA46CB"}]}}, "response": []}, {"name": "/:card_uid - Card details", "id": "7deaeb04-b82c-4e40-a505-de5cbf088cc4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/bot/v1/cards/:card_uid", "host": ["{{API_URL}}"], "path": ["bot", "v1", "cards", ":card_uid"], "variable": [{"key": "card_uid", "value": "47B669A3"}]}}, "response": []}, {"name": "/:card_uid/history", "id": "e0a9a223-7503-46d1-b36e-06582c3cbd5f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/bot/v1/cards/:card_uid/history", "host": ["{{API_URL}}"], "path": ["bot", "v1", "cards", ":card_uid", "history"], "variable": [{"key": "card_uid", "value": "0xa409b1f9"}]}}, "response": []}], "id": "a6583343-87a6-4d2d-b578-9abc4a9a550e"}, {"name": "/machines", "item": [{"name": "/:num/:card_uid - Machine status", "id": "e108d044-b336-41ef-90bc-e4df776cddcc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/bot/v1/machines/:num/:card_uid", "host": ["{{API_URL}}"], "path": ["bot", "v1", "machines", ":num", ":card_uid"], "variable": [{"key": "num", "value": "1"}, {"key": "card_uid", "value": "0x747035bc"}]}}, "response": []}], "id": "9b3491a8-2053-4f16-8231-9876b5cf63b5"}, {"name": "/support", "item": [{"name": "/technician", "id": "6bd99e70-f4b8-4980-8b24-744468b3a940", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept-Language", "value": "es", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"path\": \"fondjvndv > d v fv?GVf f > fvfv\",\n    \"cardUid\": \"0xa4f1ead7\",\n    \"phone\": \"099889988\",\n    \"email\": \"<EMAIL>\",\n    \"message\": \"esto es un test\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/bot/v1/support/technician"}, "response": []}, {"name": "/sales", "id": "47f72bfb-6ce2-45bc-87b8-4d57e3dcb605", "protocolProfileBehavior": {"disabledSystemHeaders": {}, "disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "es"}, {"key": "", "type": "text", "value": "", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"2 2222 2222\",\r\n  \"request\": \"Necesito una cotizacion\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/bot/v1/support/sales"}, "response": []}, {"name": "/contact", "id": "67d0adb9-555b-4aee-add9-9cac73530964", "protocolProfileBehavior": {"disabledSystemHeaders": {}, "disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "es"}, {"key": "", "type": "text", "value": "", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"2 2222 2222\",\r\n  \"request\": \"Necesito una cotizacion\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/bot/v1/support/contact"}, "response": []}], "id": "86a8894f-84ec-47b3-b84c-67c30e362f58"}, {"name": "/buildings", "item": [{"name": "/ - busqeuda por ubicacion", "id": "e06fbe93-f58f-43fe-a298-4b4c845bb90c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"latitude\": -34.9010394,\n  \"longitude\": -56.1335616\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/bot/v1/buildings"}, "response": []}, {"name": "/laundromat", "id": "784e805d-5911-491d-9706-8cab26c7933f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/bot/v1/buildings/laundromat"}, "response": []}, {"name": "/:buildingId/cardAcceptance", "id": "f8c5d1d1-2a32-4140-863e-4a5d432e2d8e", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}, "disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "es"}, {"key": "", "type": "text", "value": "es", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\",\r\n  \"firstName\": \"<PERSON>\",\r\n  \"lastName\": \"<PERSON>\",\r\n  \"phone\": \"099 999 999\",\r\n  \"unit\": \"301\",\r\n  \r\n  \"uid\": \"0xaa0a0aaa\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/bot/v1/buildings/:buildingId/cardAcceptance", "host": ["{{API_URL}}"], "path": ["bot", "v1", "buildings", ":buildingId", "cardAcceptance"], "variable": [{"id": "5e442f3f-6c26-4818-b6b4-05cdd9b5b69a", "key": "buildingId", "value": "44", "description": "Loop"}]}}, "response": []}, {"name": "/:buildingId", "id": "53139c99-ff33-4789-9872-fe2ec8b1b09f", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}, "disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept-Language", "type": "text", "value": "es"}, {"key": "", "type": "text", "value": "es", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"<EMAIL>\",\r\n  \"firstName\": \"<PERSON>\",\r\n  \"lastName\": \"<PERSON>\",\r\n  \"phone\": \"099 999 999\",\r\n  \"unit\": \"301\",\r\n  \"tower\": \"1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/bot/v1/buildings/:buildingId", "host": ["{{API_URL}}"], "path": ["bot", "v1", "buildings", ":buildingId"], "variable": [{"id": "5e442f3f-6c26-4818-b6b4-05cdd9b5b69a", "key": "buildingId", "value": "44", "description": "Loop"}]}}, "response": []}], "id": "85d935f2-a661-451b-91ac-3ea1850d2e8f"}, {"name": "users/:email", "id": "8aace3dc-dc6f-4271-893e-c8e13cb853d0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/bot/v1/users/:email", "host": ["{{API_URL}}"], "path": ["bot", "v1", "users", ":email"], "variable": [{"id": "ba24177f-7659-4aee-b688-7aacfc616ac5", "key": "email", "value": "<EMAIL>"}]}}, "response": []}], "id": "af53228c-9027-49ee-bba0-487b60dea1d1", "auth": {"type": "apikey", "apikey": {"value": "{{BOT_API_KEY}}", "in": "query", "key": "api_key"}}, "event": [{"listen": "prerequest", "script": {"id": "6584f7b2-b71f-4faf-aa50-f1226fa9ec6a", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "64f29c16-3ee5-4637-87d2-902dd2b47642", "type": "text/javascript", "exec": [""]}}]}, {"name": "/public-site/v1", "item": [{"name": "/administrations/buildings/coliving", "id": "0f95a780-0fb2-4276-b76d-4c9c960803ff", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": "{{API_URL}}/public-site/v1/administrations/buildings/coliving"}, "response": []}, {"name": "/cards/:uid/methods - avilable payment method for card", "id": "646e5ab6-e210-404a-a271-8ea8be869661", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/public-site/v1/cards/:uid/methods", "host": ["{{API_URL}}"], "path": ["public-site", "v1", "cards", ":uid", "methods"], "variable": [{"key": "uid", "value": "12121212"}]}}, "response": []}], "id": "c0547c8d-9d1f-45c2-bec8-30c31b5c46ca"}, {"name": "/s", "item": [{"name": "/:token", "id": "502caaec-b4a3-4572-8c36-f580dcf5b638", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/s/:token", "host": ["{{API_URL}}"], "path": ["s", ":token"], "variable": [{"key": "token", "value": "test"}]}}, "response": []}], "id": "4b04734d-109d-4839-b4af-03f166dc370d"}, {"name": "External", "item": [{"name": "Create TransAct Transaction by SOAP", "id": "2794f5b8-7618-4504-a6df-30b80bccfdf9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/xml", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://wwwi.transact.com.uy/Concentrador/TarjetasTransaccion_400.svc?wsdl", "protocol": "https", "host": ["wwwi", "transact", "com", "uy"], "path": ["Concentrador", "TarjetasTransaccion_400.svc"], "query": [{"key": "wsdl", "value": ""}]}}, "response": []}, {"name": "Transaction >> MP o<PERSON><PERSON>", "id": "1ae5357b-05bb-4158-9cdd-22bb01dcb3de", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "url": {"raw": "https://api.mercadopago.com/oauth/token?client_id=7094776074066958&client_secret=********************************&grant_type=authorization_code&code=TG-5c71b95bde6cb700061efa18-402020864&redirect_uri=https://app-sandbox.lavomat.com.uy/api/v1/transaction/bind?adm=3", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["o<PERSON>h", "token"], "query": [{"key": "client_id", "value": "7094776074066958"}, {"key": "client_secret", "value": "********************************"}, {"key": "grant_type", "value": "authorization_code"}, {"key": "code", "value": "TG-5c71b95bde6cb700061efa18-402020864"}, {"key": "redirect_uri", "value": "https://app-sandbox.lavomat.com.uy/api/v1/transaction/bind?adm=3"}]}}, "response": []}, {"name": "Me<PERSON>do <PERSON> >> refresh token", "id": "52189925-b38c-4f58-aba1-49340a952139", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "https://api.mercadopago.com/oauth/token?client_secret=APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********&grant_type=refresh_token&refresh_token=TG-5d615a0f48817f00063dab17-462865496", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["o<PERSON>h", "token"], "query": [{"key": "client_secret", "value": "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********"}, {"key": "grant_type", "value": "refresh_token"}, {"key": "refresh_token", "value": "TG-5d615a0f48817f00063dab17-462865496"}]}}, "response": []}, {"name": "Mercado <PERSON> >> Consulta Merchant Orders ", "id": "b4184ff5-8e63-4820-8623-b992deafee8b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "https://api.mercadopago.com/merchant_orders/1560575229?access_token=APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["merchant_orders", "1560575229"], "query": [{"key": "access_token", "value": "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********"}]}}, "response": []}, {"name": "Cualit Test", "id": "9af7c5e8-5efc-482f-8ba8-3426c1f211dd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "token", "value": "redpagos-lavomat-testing", "type": "text"}, {"key": "currency", "value": "858", "type": "text"}, {"key": "amount", "value": "12000", "type": "text"}, {"key": "customerTransactionId", "value": "12345-98765e-954-001", "type": "text"}, {"key": "invoiceNumber", "value": "11223", "type": "text"}, {"key": "taxableAmount", "value": "9836", "type": "text"}, {"key": "service_id", "value": "0x943c30bc", "type": "text"}]}, "url": "https://paymentgateway.cualit.com/api/v1/transaction"}, "response": []}, {"name": "Mercado <PERSON> >> consulta payments", "id": "de86b62f-a0f5-4dc5-802e-0adcf8532113", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "https://api.mercadopago.com/v1/payments/7057576995?access_token=APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["v1", "payments", "7057576995"], "query": [{"key": "access_token", "value": "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********"}]}}, "response": []}, {"name": "MERCADO PAGO >> Search stores", "id": "b8b0af11-c296-48aa-abcb-24fb62e33648", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "https://api.mercadopago.com/users/:user_id/stores/search?external_id=Store_1&access_token=APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["users", ":user_id", "stores", "search"], "query": [{"key": "external_id", "value": "Store_1"}, {"key": "access_token", "value": "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********"}], "variable": [{"key": "user_id", "value": "*********"}]}}, "response": []}, {"name": "MERCADO PAGO >> Create stores", "id": "a15883fa-47e3-4a46-a961-4a80c7786400", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"name\": \"LAVOMAT Plaza Italia\",\n    \"external_id\": \"Store_5\",\n    \"location\": {\n        \"zip_code\": \"11400\",\n        \"street_number\": \"4250\",\n        \"street_name\": \"Av. Italia\",\n        \"city_name\": \"Malvin\",\n        \"state_name\": \"Montevideo\",\n        \"latitude\":-34.88774,\n        \"longitude\": -56.11789,\n        \"reference\": \"Local ubicado dentro del Plaza Italia\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.mercadopago.com/users/:user_id/stores?access_token=APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********", "protocol": "https", "host": ["api", "mercadopago", "com"], "path": ["users", ":user_id", "stores"], "query": [{"key": "access_token", "value": "APP_USR-7573715325607961-031307-5bf84f62604ff8a21dbe309fbe1fda30-*********"}], "variable": [{"id": "62779acc-c2d6-4cbc-8daa-e62dca536b36", "key": "user_id", "value": "*********"}]}}, "response": []}], "id": "18a62e3c-1d45-4392-b88b-a320e535b906"}, {"name": "/asst/v1", "item": [{"name": "/buildings/:slug", "id": "31ba8ec8-eb10-4a73-981a-cc7611bf5a0a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{API_URL}}/asst/v1/buildings/:slug", "host": ["{{API_URL}}"], "path": ["asst", "v1", "buildings", ":slug"], "variable": [{"key": "slug", "value": "laundromat-plaza-621ce91a"}]}}, "response": []}], "id": "a8d834e4-5dfe-41b2-94fc-5a9f658f5192"}, {"name": "/rpi/v1", "item": [{"name": "/activate/request/simple", "id": "38a16948-dc40-4f69-9aa4-863d06cb4070", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/rpi/v1/activate/request/simple?param1=0x00000040&param2=1002&param3={{machine-serial}}", "host": ["{{API_URL}}"], "path": ["rpi", "v1", "activate", "request", "simple"], "query": [{"key": "param1", "value": "0x00000040", "description": "card.uuid"}, {"key": "param2", "value": "1002", "description": "building.id"}, {"key": "param3", "value": "{{machine-serial}}", "description": "machine.serial"}]}}, "response": []}, {"name": "/booking/:id/received", "id": "1486c574-e889-4bf2-abbc-6c05e2941a90", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"bookable\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{API_URL}}/rpi/v1/booking/:id/received", "host": ["{{API_URL}}"], "path": ["rpi", "v1", "booking", ":id", "received"], "variable": [{"key": "id", "value": "{{booking-id}}"}]}}, "response": []}], "id": "ffbd9f50-e5e3-4d69-8a73-5d5844ac3f4f", "auth": {"type": "apikey", "apikey": {"value": "{{LM_AUTH_TOKEN__RPI}}", "key": "X-LM-AUTH-TOKEN"}}, "event": [{"listen": "prerequest", "script": {"id": "8024e629-1152-4411-8226-41dc3dbccc05", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "dc44e28b-1936-47b6-893c-993ac0b7bfca", "type": "text/javascript", "exec": [""]}}]}, {"name": "/totem/v1", "item": [{"name": "/booking - book machine", "id": "852986ae-322e-4a05-8a66-e3ee55b5099a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"serial\": \"{{machine-serial}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/totem/v1/booking"}, "response": []}, {"name": "/booking - booking status", "id": "ae31fdaf-322e-4b15-905e-f3c036e09fa6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{API_URL}}/totem/v1/booking/:id", "host": ["{{API_URL}}"], "path": ["totem", "v1", "booking", ":id"], "variable": [{"key": "id", "value": "{{booking-id}}"}]}}, "response": []}, {"name": "/booking - cancel", "id": "d7ec30bd-098a-4d14-8cd1-146e87aa76de", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{API_URL}}/totem/v1/booking/:id", "host": ["{{API_URL}}"], "path": ["totem", "v1", "booking", ":id"], "variable": [{"id": "fc4d934c-bb26-4f20-9374-4ebe7cfbe326", "key": "id", "value": "{{booking-id}}"}]}}, "response": []}], "id": "35e48686-219b-4e8e-bcd2-9e3f714472f6", "auth": {"type": "apikey", "apikey": {"value": "{{LM_AUTH_TOKEN__TOTEM}}", "key": "X-LM-AUTH-TOKEN"}}, "event": [{"listen": "prerequest", "script": {"id": "e3f0ee28-6067-4af7-9f57-4bd44d34f1a8", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "980051a7-b611-403b-b250-f2a494aaff28", "type": "text/javascript", "exec": [""]}}]}, {"name": "/webhoook/v1", "item": [{"name": "/sentry", "id": "f26b14b4-97eb-4f15-b459-056c5bff5632", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"data\": {\n        \"triggered_rule\": \"test\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": "{{API_URL}}/webhook/v1/sentry"}, "response": []}], "id": "85e6d941-4f25-4a37-bece-2cb0d630f5c4"}], "auth": {"type": "apikey", "apikey": {"value": "{{LM_AUTH_TOKEN}}", "key": "X-LM-AUTH-TOKEN"}}, "event": [{"listen": "prerequest", "script": {"id": "34bfc91a-9a13-4086-abfd-5ebd860c9467", "type": "text/javascript", "exec": ["pm.sendRequest({", "    url: `${pm.variables.get(\"API_URL\") || ''}/api/v1/signin`,", "    method: 'POST',", "    header: {", "    'Content-Type': 'application/json',", "  },", "  body: {", "    mode: \"row\",", "    raw: JSON.stringify({ ", "        emailAddress: pm.variables.get(\"LM_AUTH_USER\"), ", "        password: pm.variables.get(\"LM_AUTH_PASSWORD\"), ", "    })", "  }", "}, function (error, response) {", "    if (!error) {", "        // { account: { user: { role, name, id, email, lastname } }, token }", "        const { token } = response.json();", "", "        pm.environment.set(\"LM_AUTH_TOKEN\", token || '');", "    } else {", "        console.error(error)", "    }", "});"]}}, {"listen": "test", "script": {"id": "38cd161c-1814-44e1-b76c-121fb81145f2", "type": "text/javascript", "exec": [""]}}]}