FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo de herramientas


### Create tool [POST /api/v1/tools]

+ Clientes: BackOffice

+ Propósito: Crear una herramientas

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + editing: true (string) - valor que indica si el objeto es editable o no. Valores esperados: true, false
        + name: AA3119FF33 (string, required) - nombre de una máquina
        + serial_number: 119K2AA2E124 (string, required) - número de serie de un repuesto
        + description: herramienta buena (string) - descripción de herramienta


+ Response 201 (application/json)

+ Response 400 (application/json)

    + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_detail: Los siguientes campos son requeridos: name,serial_number (string) - código interno de resultado de la operación
