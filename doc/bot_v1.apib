FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - Integracion Chatbot - API

## Tarjetas [/bot/v1/cards]
### Detalle de la tarjeta [GET /bot/v1/cards/{card_uid}{?api_key}]

Camino:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID

Responde a:
- SI es PREPAGO, indicamos el saldo actual y a qué edificio pertenece y el apto
    - Si la carga de saldo es posterior al último keepalive indicamos que vemos que compró $xx saldo a la hora xx:xx, indicamos saldo acrtual y que ese saldo se sincroniza en xx minutos y volver a probar.
        - error code: 130
    - Si el saldo es inferior a un uso: Se indica el saldo actual y se ofrecen las siguientes opciones
        - error code: 115
    - Si la carga de saldo es anterior al último keepalive, indicamos saldo actual, pedimos desenchufar y volver a enchufar el lavarropas, luego esperar 5 minutos y volver a probar.
        - salida por defecto
- Su tarjeta está bloqueada. Desea solicitar la activación de la misma? (02)
    - error code: 100


+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request
    + Headers

            Accept-Language: es


+ Response 200 (application/json)

    + Attributes(Cards)

+ Response 400 (application/json)

    + Attributes(Card blocked)

+ Response 404 (application/json)

    + Attributes(Card not found)


### Historial de la tarjeta [POST /bot/v1/cards/{card_uid}/history{?api_key}]

Camino 1 :
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. SI es PREPAGO, indicamos el saldo actual y a qué edificio pertenece y el apto > Si el saldo es inferior a un uso: Se indica el saldo actual y se ofrecen las siguientes opciones
4. Opción 1: Ver historial de saldo

Camnino 2:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. La tarjeta es POSTAPGO y esta ok
4. Opción 2: Quiero saber el historial de usos.

Responde a:
- Solicitamos un mail y luego mostramos el historial de los ultimos 10 movimientos, e indicamos ver mail enviado por más información.

+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes (object)
        + email: <EMAIL> (string, required) - email ingresado por el usuario

+ Response 200 (application/json)

    + Attributes(Uses history)

+ Response 404 (application/json)

    + Attributes(Card not found)

### Soporte a usuario que no puede loggear su tarjeta [POST /bot/v1/cards/{card_uid}/getUserAssignmentStatus{?api_key}]

Camino 1 :
1. Opción 2: No puedo vincular mi tarjeta de LAVOMAT a mi usuario en la APP


Responde a:
- Solicitamos un mail y el uid de la tarjeta, y mostramos el estado de la asignación de la tarjeta con el mail ingresado.

+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes (object)
        + email: <EMAIL> (string, required) - email ingresado por el usuario

+ Response 200 (application/json)

    + Attributes(object)
        + message: La tarjeta no existe (string, required) - mensaje que retorna la asociación del usuario con la tarjeta.

+ Response 404 (application/json)

    + Attributes(Card not found)


## Contacto con soporte [/bot/v1/support]

### Solicitar contacto [POST /bot/v1/support/contact{?api_key}]


Camino:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. SI es PREPAGO, indicamos el saldo actual y a qué edificio pertenece y el apto > Si el saldo es inferior a un uso: Se indica el saldo actual y se ofrecen las siguientes opciones
4. Opción 1: Ver historial de saldo
5. Solicitamos un mail y luego mostramos el historial de los ultimos 10 movimientos, e indicamos ver mail enviado por más información.

Responde a:
- Opción 2: Solicitar el contacto de una persona.

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente


+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes (object)
        + email: <EMAIL> (string, required) - email ingresado por el usuario


+ Response 204


### Solicitar tecnico [POST /bot/v1/support/technician{?api_key}]

- Problemas con una máquina > Solicitar tecnico

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes (object)
        + path: El secarropas no calienta > Filtro controlado (string, required) - sucesion de pasos seguidos por el usuario que lo llevaron a solictar un tecnico
        + cardUid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta, requerido si la buildingId y unit no fueron ingresados
        + buildingId: 1 (number) - identificador del edificio, requerido si la cardUid no fue ingresado
        + unit: 301 (string) - unidad al que pertenece una tarjeta, requerido si la cardUid no fue ingresado
        + message: no prende la maquina (string, optional) - descripción del error ingresado por el usuario
        + email: <EMAIL> (string, optional) - email ingresado por el usuario
        + phone: 099889988 (string, optional) - número telefonico de contacto
        + machineNum: 20 (number, optional) - número de máquina ingresado por el usuario

+ Response 204


### Solicitar contacto comercial [POST /bot/v1/support/sales{?api_key}]

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)

    + Attributes (object)
        + email: <EMAIL> (string, optional) - email de contacto ingresado por el usuario
        + phone: 2 2222 2222 (string, optional) - telefono d e contacto ingresado por el usuario
        + request: Necesito una cotizacion (string, required) - motivo por el cual quire realizar un contacto con el area comercial

+ Response 204


### Notificar error con la app [POST /bot/v1/support/app{?api_key}]

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)

    + Attributes (object)
        + email: <EMAIL> (string, required) - email de contacto ingresado por el usuario
        + uid: 0xaa0a0aaa (string, required) - UID: identificador de una tarjeta
        + reason: Tenemos un problema en la App (string, required) - motivo por el cual ocurrio un error en la app.
        + phone: 2 2222 2222 (string, optional) - telefono d e contacto ingresado por el usuario

+ Response 204


## máquinas [/bot/v1/machines]

### Estado de la máquina [GET /bot/v1/machines/{machine_num}/{card_uid}{?api_key}]

Camnino:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. La tarjeta es POSTAPGO y esta ok
4. Opción 1: No puedo activar la máquina
5. Pedir número de máquina

Responde a:
- Su tarjeta está ok. Pedir desenchufar y volver a enchufar el lavarropas, esperar 5 minutos y volver a probar. Luego dos opciones “FUNCIONA” o “NO FUNCIONA” (03) (Esto lo muestra si es POSTPAGO)


+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + machine_num: 1 (number) - número de la máquina. Numeracion establecida de acuerdo a la disposicion fisica de las mismas
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request
    + Headers

            Accept-Language: es

+ Response 200 (application/json)

    + Attributes(Machine)

+ Response 400 (application/json)

    + Attributes(Machine disabled)

+ Response 404 (application/json)

    + Attributes(Machine not found)


+ Response 404 (application/json)

    + Attributes(Card not found)


### Activar máquina [POST /bot/v1/machines/{machine_id}/activation/{card_uid}{?api_key}]

Camnino:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. La tarjeta es POSTAPGO y esta ok
4. Opción 1: No puedo activar la máquina
5. Pedir número de máquina
6. Su tarjeta está ok. Pedir desenchufar y volver a enchufar el lavarropas, esperar 5 minutos y volver a probar. Luego dos opciones “FUNCIONA” o “NO FUNCIONA” (03) (Esto lo muestra si es POSTPAGO)

Responde a:
- Si no funciona: dar la opción de activar la máquina mediante este chat. Preguntar si activo o no. Si no activo, avisar que va un técnico.


+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + machine_id: 1 (number) - identificador de una máquina
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request
    + Headers

            Accept-Language: es

+ Response 204

+ Response 400 (application/json)

    + Attributes(Machine activation failed)

### Confirmar Activacion [POST /bot/v1/machines/{machine_id}/activation/{card_uid}/confirm{?api_key}]

Camnino:
1. Consulta tarjeta de LAVOMAT
2. Ingresar UID
3. La tarjeta es POSTAPGO y esta ok
4. Opción 1: No puedo activar la máquina
5. Pedir número de máquina
6. Su tarjeta está ok. Pedir desenchufar y volver a enchufar el lavarropas, esperar 5 minutos y volver a probar. Luego dos opciones “FUNCIONA” o “NO FUNCIONA” (03) (Esto lo muestra si es POSTPAGO)
7. Si no funciona: dar la opción de activar la máquina mediante este chat. Preguntar si activo o no. Si no activo, avisar que va un técnico.

Responde a:
- Opción 1: La máquina se activó mediante el chat.
- Opción 2: La máquina NO se activó mediante el chat

+ Parameters
    + card_uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
    + machine_id: 1 (number) - identificador de una máquina
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente


+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes(Machine confirm activation)

+ Response 204

+ Response 404 (application/json)

    + Attributes(Machine not found)


## Edificios [/bot/v1/buildings]

### Detalle del edificio [POST /bot/v1/buildings{?api_key}]


Camnino:
1. Menu Solicitar tarjeta nueva
2. Pregunta donde lavar
3. Pedir dirección del edificio (ubicacion del dispositivo)

Responde a:
- Si es POSTPAGO. Pedir número de apto. Luego pedir datos, ver especificaciones.
- Si es PREPAGO, indicar costo de tarjeta y preguntar si desea pedirla

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes(Address)

+ Response 200 (application/json)

    + Attributes(Buildings)

+ Response 404 (application/json)

    + Attributes(Building not found)


### Solicitar tarjeta [POST /bot/v1/buildings/{building_id}{?api_key}]


Camnino:
1. Menu Solicitar tarjeta nueva
2. Pregunta donde lavar
3. Pedir dirección del edificio (ubicacion del dispositivo)
4. Si es POSTPAGO. Pedir número de apto. Luego pedir datos, ver especificaciones.
5. Indicar costo de $xxx. Y que la misma se entregará el día hábil siguiente en horario de portería. Confirmar pedido

Responde a:
- Opción 1: SI. Indicar que estaremos validando los datos con la administración y luego confirmaremos la hora.

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente
    + building_id: 1 (number) - identificador del edificio


+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes(Card request)

+ Response 204


### Solicitar ingreso de tarjeta [POST /bot/v1/buildings/{building_id}/cardAcceptance{?api_key}]


Camnino:
1. Problemas con una máquina
2. Ingresar UID de tarjeta o usuario de APP
3. Su tarjeta no está ingresada en el sistema. Desea ingresar su tarjeta? (04)
4. Se le pide que ingrese el número o nombre torre si hay mas de una, luego se le pide la unidad
5. Se le pide al usuario un mail


Responde a:
- Se envía un mail a la administración consultando si esa tarjeta se puede asociar a esa unidad y dos opciones, “ACEPTAR” o “NO ACEPTAR”
- Se envía mail al usuario cuando se confirma la tarjeta a la unidad y se bloquea la tarjeta anterior.


+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente
    + building_id: 1 (number) - identificador del edificio


+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes(Card acceptance)

+ Response 204


### Detalle de lavanderia LAVOMAT [GET /bot/v1/buildings/laundromat{?api_key}]

Camnino:
1. Menu Solicitar tarjeta nueva
2. Pregunta donde lavar
3. Opción 2: En una lavandería de LAVOMAT

Responde a:
- Si es PREPAGO, indicar costo de tarjeta y preguntar si desea pedirla


+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request
    + Headers

            Accept-Language: es

+ Response 200 (application/json)

    + Attributes(Building)

+ Response 404 (application/json)

    + Attributes(Building not found)


## Usuarios [/bot/v1/users]

### Detalle del usuario [GET /bot/v1/users/{email}{?api_key}]

Camnino:
1. Ayuda con la APP
2. Ingresar mail de usuario

Responde a:
- Su usuario no existe, mostrar pasos para crear ususario.
- El usuario no esta validado. Debe revisar su mail. Nota: ver en SPAM.
- Si el usuario es PREPAGO Indicamos el saldo actual y a qué edificio pertenece y el apto
    - Si la carga de saldo es posterior al último keepalive indicamos que vemos que compró $xx saldo a la hora xx:xx, indicamos saldo acrtual y que ese saldo se sincroniza en xx minutos y volver a probar.
        - error code: 130
    - Si la carga de saldo es anterior al último keepalive, indicamos saldo actual, pedimos desenchufar y volver a enchufar el lavarropas, luego esperar 5 minutos y volver a probar.
        - salida por defecto
    - Si el saldo es inferior a un uso, ofrecer cargar saldo con la url de la web de lavomat donde pide elegir en tre MP y PW.
        - error code: 115
- Si es POSTAPGO y esta ok



+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente
    + email: <EMAIL> (string, required) - email ingresado por el usuario

+ Request (application/json)
    + Headers

            Accept-Language: es

+ Response 200 (application/json)

    + Attributes(User cards)

+ Response 400 (application/json)

    + Attributes(User unconfirmed)

+ Response 404 (application/json)

    + Attributes(User not found)


# Data Structures

## Cards (object)
+ cards (array[Card]) - listado de tarjetas

## Card (object)
+ id: 1 (number) - identificador interno de una tarjeta
+ uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
+ type (Card Type) - tipo de tarjeta: POSTPAID o PREPAID
+ building (Building) - edificio al que pertenece una tarjeta
+ unit (Unit) - unidad al que pertenece una tarjeta
+ status (enum) - estado de la tarjeta, permite saber si esta bloqueada
    + ACTIVE (string) - listo para usar
    + INACTIVE (string) - bloqueada
+ balance: 100 (number, optional) - credito con el cual cuenta una trajeta cuando es PREPAID.
+ One Of
    + error (object)
        + code: 130 (number) - internal code error
        + message: Ha relizado una carga de saldo correspondiente a $xx a la hora xx:xx. Su saldo actual es $xx. Estamos esperimentando una demora en la sincronizacion del equipo. Vuelva a intentarlo en xx minutis. (string) - descripción del error
    + error (object)
        + code: 115 (number) - internal code error
        + message: Su saldo actual es $xxx. Es inferior al necesario para la operación (string) - descripción del error
    + error (object)
        + code: 100 (number) - internal code error
        + message: La tarjeta se encuentra bloqueada (string) - descripción del error

## Card Type (enum)
+ POSTPAID
+ PREPAID


## Building (object)
+ id: 1 (number) - identificador interno de un edificio
+ name: Edificio Uno (string) - nombre de un edificio
+ type (Card Type) - tipo de contrato del edificio
+ cardCost: 300 (number) - costo de la tarjeta
+ towerRequired: true (boolean) - indica si es requerido el dato de la torre ademas de la unidad para futuras operaciónes
+ appEnabled: true (boolean) - indica si la app mobile esta habilitada para su uso en el edificio

## Unit (object)
+ id: 1 (number) - identificador interno de una unidad/apto
+ name: 301 (string) - nombre de la unidad/apto, puede incluir identificador de la torre y/o unidad


## Uses history (object)
+ uses (array[Individual use])

## Individual use (object)
+ timestamp: `2021-02-16 13:51` (string) - fecha y hora
+ type (enum) - tipo de operación realizada
    + Secado (string)
    + Lavado (string)
+ amount: 1000 (number) - monto de la transaccion realizada


## Machine (object)
+ id: 1 (number) - identicador interno de la máquinas
+ status: ok (enum) - estado de la máquina
    + ok
    + error

## Machine confirm activation (object)
+ result: ok (enum) - resultado de activacion de la máquina indicado por el usuario
    + ok
    + error


## Address (object)
+ latitude: `-55.55` (number) - Latitud obtenida de la ubicacion del dispositivo del usuario
+ longitude: `-55.55` (number) - longitud obtenida de la ubicacion del dispositivo del usuario

## Buildings (object)
+ buildings (array[Building])


## Card request (object)
+ email: 301 (string) - email ingresado por el usuario
+ firstName: 301 (string) - nombre ingresado por el usuario
+ lastName: 301 (string) - apellido ingresado por el usuario
+ phone: 301 (string) - telefono ingresado por el usuario
+ unit: 301 (string) - número de apartamento ingresado por el usuario
+ tower: 1 (number) - número de torre en caso de ser necesario para identificar la unidad


# User cards (object)
+ cards (array[User card]) - Listado de tarjetas asociadas a un usuario


## Card acceptance (object)
+ email: <EMAIL> (string, required) - email ingresado por el usuario
+ firstName: Juan (string, required) - nombre ingresado por el usuario
+ lastName: Perez (string) - apellido ingresado por el usuario
+ phone: 099 999 999 (string) - telefono ingresado por el usuario
+ unit: 301 (string, required) - número de apartamento ingresado por el usuario
+ tower: 1 (number) - número de torre en caso de ser necesario para identificar la unidad
+ uid: 0xaa0a0aaa (string, required) - UID: identificador de una tarjeta


## User card (object)
+ id: 1 (number) - identificador interno de una tarjeta
+ uid: 0xaa0a0aaa (string) - UID: identificador de una tarjeta
+ type (Card Type) - tipo de tarjeta: POSTPAID o PREPAID
+ building (Building) - edificio al que pertenece el usuario
+ unit (Unit) - unidad al que pertenece el usuario
+ virtual (boolean) - indica si la tarjeta es virtual, implica que solo esta disponible para ser usada en la lavanderia publica
+ status (enum) - estado de la tarjeta, permite saber si esta bloqueada
    + ACTIVE (string) - listo para usar
    + INACTIVE (string) - bloqueada
+ balance: 100 (number, optional) - credito con el cual cuenta una trajeta cuando es PREPAID.
+ One Of
    + error (object)
        + code: 130 (number) - internal code error
        + message: Ha relizado una carga de saldo correspondiente a $xx a la hora xx:xx. Su saldo actual es $xx. Estamos esperimentando una demora en la sincronizacion del equipo. Vuelva a intentarlo en xx minutis. (string) - descripción del error
    + error (object)
        + code: 115 (number) - internal code error
        + message: Su saldo actual es $xxx. Es inferior al necesario para la operación (string) - descripción del error
    + error (object)
        + code: 100 (number) - internal code error
        + message: La tarjeta se encuentra bloqueada (string) - descripción del error


-- Errors

## Card not found (object)
+ result_code: 10 (number) - internal code error
+ result_message: La tarjeta no existe (string) - descripción del error

## Card insufficient balance (object)
+ code: 115 (number) - internal code error
+ message: Ha relizado una carga de saldo correspondiente a $xx a la hora xx:xx. Su saldo actual es $xx. Estamos esperimentando una demora en la sincronizacion del equipo. Vuelva a intentarlo en xx minutis. (string) - descripción del error

## Card async balance (object)
+ code: 130 (number) - internal code error
+ message: Su saldo actual es $xxx. Es inferior al necesario para la operación (string) - descripción del error

## Card blocked (object)
+ result_code: 100 (number) - internal code error
+ result_message: La tarjeta se encuentra bloqueada (string) - descripción del error

## Machine disabled (object)
+ result_code: 117 (number) - internal code error
+ result_message: No se ha podido establecer una conexion con el equipo (string) - descripción del error

## Machine not found (object)
+ result_code: 9 (number) - internal code error
+ result_message: El número de equipo indicado no se encuentra en el edificio (string) - descripción del error

## Machine activation failed
+ result_code: 104 (number) - internal code error
+ result_message: El equipo no ha podido ser activado (string) - descripción del error

## Building not found (object)
+ result_code: 2 (number) - internal code error
+ result_message: No se ha podido determinar su edificio (string) - descripción del error

## User not found (object)
+ result_code: 3 (number) - internal code error
+ result_message: La tarjeta no existe (string) - descripción del error

## User insufficient balance (object)
+ code: 115 (number) - internal code error
+ message: Ha relizado una carga de saldo correspondiente a $xx a la hora xx:xx. Su saldo actual es $xx. Estamos esperimentando una demora en la sincronizacion del equipo. Vuelva a intentarlo en xx minutis. (string) - descripción del error

## User async balance (object)
+ code: 130 (number) - internal code error
+ message: Su saldo actual es $xxx. Es inferior al necesario para la operación (string) - descripción del error

## User unconfirmed (object)
+ result_code: 131 (number) - internal code error
+ result_message: El usuario no esta validado (string) - descripción del error
