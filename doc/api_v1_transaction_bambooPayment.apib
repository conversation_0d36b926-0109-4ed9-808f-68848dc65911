FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API hooks for BambooPayment Physical Agents

## Transactions [/api/v1/transaction/bambooPayment]

### Create Transaction [POST /api/v1/transaction/bambooPayment/consulta]

+ Clientes: BambooPayment

+ Proposito: Recibir las recargas de saldo que realizan los usuarios en los mostradores de RedPagos y validar si los datos suministrados son validos

+ Request

    + Attributes(object)
        + username: lavomat1234 (string, required) - usuario que identifica a LAVOMAT en BambooPayment
        + password: `PjWx12a+5D0=` (string, required) - contraseña generada en comun acuerdo entre LAVOMAT y BambooPayment
        + tipoDocumento: 5 (number, required) - Tipo de documento del usuario
        + documento: 0xaa0a0aaa (string, required) - UID: identificador de una tarjeta
        + redFisica: REDPAGOS (string, required) - Identificador de la red física que realiza la consulta


+ Response 200 (application/json)

    when the request was valid

     + Attributes(object)
        + Status: 0 (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: OK, la operación fue correctamente ejecutada. (string, required) - Descripción aclaratoria del resultado de la operación
        + PagosPendientes (array, required) - Lista de pagos pendientes del cliente
            + payment (object)
                + IdItem: 1 (number, required) - Identificador del Item a Pagar
                + Descripcion: saldo (string, required) - Descripción del Item
                + Importe: 0 (number, fixed) - Importe del Item a pagar
                + Moneda: 858 (number, fixed) - Código de moneda de la deuda
                + ConsumidorFinal: false (boolean, fixed) - Flag que indica si el cliente es consumidor final
                + ImporteGravadoIVA: 0 (number, fixed) - Importe que está gravado con IVA
                + NroFactura: `` (string, fixed) - Número de Factura
        + ClienteNombre: Juan (string) - Nombre del cliente.
        + ClienteApellido: Perez (string) - Apellido del cliente
        + ClienteEmail: <EMAIL> (string) - Email del cliente

+ Response 200 (application/json)

    when the request was invalid

     + Attributes(object)
        + Status: `-1` (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: Ocurrió un error interno en la aplicación. (string, required) - Descripción aclaratoria del resultado de la operación
        + PagosPendientes (array, required) - Lista de pagos pendientes del cliente


### Confirm Transaction [POST /api/v1/transaction/bambooPayment/confirmarPago]

+ Clientes: BambooPayment

+ Proposito: Recibir las confirmacion de las recargas de saldo que realizan los usuarios en los mostradores de RedPagos y validar si los datos suministrados son validos


+ Request

    + Attributes(object)
        + username: lavomat1234 (string, required) - usuario que identifica a LAVOMAT en BambooPayment
        + password: `PjWx12a+5D0=` (string, required) - contraseña generada en comun acuerdo entre LAVOMAT y BambooPayment
        + tipoDocumento: 5 (number, required) - Tipo de documento del usuario
        + documento: 0xaa0a0aaa (string, required) - UID: identificador de una tarjeta
        + redFisica: REDPAGOS (string, required) - Identificador de la red física que realiza la consulta
        + moneda: 858 (number, required) - Código de moneda del pago.
        + importeTotal: 10000 (number, required) - Importe total del pago. En decimales, 10000 equivales a 100.
        + NroOrden: 4356 (number, required) - Número de Orden generado en Bamboo
        + detallesPago (array, required) - Lista con el detalle de las transacciones pagadas por el cliente.
            + payment (object)
                + IdItem: 1 (number, required) - Identificador del item que se está pagando.
                + Moneda: 858 (number, fixed) - Código de moneda de la transacción
                + ImportePagado: 10000 (number, required) - Importe de la transacción. Debe ser igual al informado en la consulta. En decimales, 10000 equivales a 100.


+ Response 200 (application/json)

    when the request was valid

     + Attributes(object)
        + Status: 0 (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: OK, la operación fue correctamente ejecutada. (string, required) - Descripción aclaratoria del resultado de la operación

+ Response 200 (application/json)

    when the request was invalid

     + Attributes(object)
        + Status: 107 (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: La transacción ya se encuentra paga. (string, required) - Descripción aclaratoria del resultado de la operación


### Reject Transaction [POST /api/v1/transaction/bambooPayment/anularPago]

+ Clientes: BambooPayment

+ Proposito: Recibir las anulaciones de las recargas de saldo que realizaron los usuarios en los mostradores de RedPagos y deshacer la acreditaciones realizadas


+ Request

    + Attributes(object)
        + username: lavomat1234 (string, required) - usuario que identifica a LAVOMAT en BambooPayment
        + password: `PjWx12a+5D0=` (string, required) - contraseña generada en comun acuerdo entre LAVOMAT y BambooPayment
        + tipoDocumento: 5 (number, required) - Tipo de documento del usuario
        + documento: 0xaa0a0aaa (string, required) - UID: identificador de una tarjeta
        + redFisica: REDPAGOS (string, required) - Identificador de la red física que realiza la consulta
        + moneda: 858 (number, required) - Código de moneda del pago.
        + importeTotal: 10000 (number, required) - Importe total del pago. En decimales, 10000 equivales a 100.
        + NroOrden: 4356 (number, required) - Número de Orden generado en Bamboo
        + detallesPago (array, required) - Lista con el detalle de las transacciones pagadas por el cliente.
            + payment (object)
                + IdItem: 1 (number, required) - Identificador del item que se está pagando.
                + Moneda: 858 (number, fixed) - Código de moneda de la transacción
                + ImportePagado: 10000 (number, required) - Importe de la transacción. Debe ser igual al informado en la consulta. En decimales, 10000 equivales a 100.


+ Response 200 (application/json)

    when the request was valid

     + Attributes(object)
        + Status: 0 (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: OK, la operación fue correctamente ejecutada. (string, required) - Descripción aclaratoria del resultado de la operación

+ Response 200 (application/json)

    when the request was invalid

     + Attributes(object)
        + Status: 201 (number, required) - Código que determina el resultado de la operación
        + StatusDescripcion: No hay transacciones para anular (string, required) - Descripción aclaratoria del resultado de la operación
