FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo de URLs

## Tiny URLs [/api/v1/tinyUrls]

### Retrieve Tiny URLs [GET /api/v1/tinyUrls{?level}]

+ Parameters
    + level (number, optional) - indica el nivel de datos a ser devuelto

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)
    + Attributes (object)
        + tinyUrls (array)
            + (object)
                + id: 1 (number) - Internal identifier of the URL
                + token: "jkdfgj87df" (string) - Token associated with the URL
                + description: "Esto es una url de prueba" (string) - Description of the URL
                + url: "https://app-sandbox-ec2.lavomat.com.uy/s/test" (string) - Generated URL
                + destinationUrl: "https://example.com/building-slug" (string) - Destination URL
                + isActive: false (boolean) - Indicates if the URL is active or not
                + associatedTo: "Machine: 111AAAA0A010" (string) - Associated entity and identificator
                + createdAt: "22/06/2023 00:28" (string) - Date and time of creation
                + updatedAt: "26/06/2023 22:44" (string) - Date and time of last update



### Create Tiny URL [POST]

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes (object)
        + destinationUrl: "https://example.com/building-slug" (string) - Destination URL
        + isActive: false (boolean) - Indicates if the URL is active or not
        + description: "Esto es una url de prueba" (string) - Description of the URL

+ Response 201 (Created)


### Update Tiny URL [PUT /api/v1/tinyUrls/{id}]

+ Parameters
    + id (number) - ID of the tiny URL

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes (object)
        + destinationUrl: "https://example.com/building-slug" (string) - Destination URL
        + isActive: false (boolean) - Indicates if the URL is active or not
        + description: "Esto es una url de prueba" (string) - Description of the URL

+ Response 200 (OK)


### Create Bulk Tiny URLs [POST /api/v1/tinyUrls/{count}]

+ Parameters
    + count (number) - Number of items to create

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 201 (Created)


### Assign Machine to Tiny URL [POST /api/v1/tinyUrls/{token}/machine/{machineId}]

+ Parameters
    + token (string) - Token of the tiny URL
    + machineId (string) - ID of the machine

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (OK)


### Unassign Machine from Tiny URL [DELETE /api/v1/tinyUrls/{token}/machine/{machineId}]

+ Parameters
    + token (string) - Token of the tiny URL
    + machineId (string) - ID of the machine

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (OK)
