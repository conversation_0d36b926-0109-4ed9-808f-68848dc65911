FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo de máquinas

## Card [/api/v1/machines]

### Zeroing machine's pending uses [PUT /api/v1/machine/{machineId}/zero-pending-uses]

+ Clientes: BackOffice

+ Propósito: Setear en 0 los usos pendientes de una máquina

+ Parameters
    + machineId: 1 (number, required) - identificador de una máquina (part.id)

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 401 (application/json)

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 9 (number) - identificador del error
            + result_message: MACHINE_NOT_FOUND (string) - código interno de resultado de la operación


### Create machine [POST /api/v1/machines]

+ Clientes: BackOffice

+ Propósito: Crear una máquina

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + average_use_time: 45 (number) - tiempo promedio de uso de una máquina
        + capacity: 10 (number) - capacidad en kg de ropa para que una máquina haga un lavado óptimo
        + description: una descripción (string) - descripción de la máquina
        + editing: true (string) - valor que indica si el objeto es editable o no. Valores esperados: true, false
        + expected_uses: 15000 (number) - usos estimados de tiempo de vida de una máquina
        + machine_model_id: 42 (number) - número identificador de una máquina
        + machine_type: WASHER (string) - tipo de máquina. Valores esperados: 'WASHER', 'DRYER'
        + name: RV1329AN7S (string, required) - nombre de una máquina
        + rateId: 33 (number) - número identificador de un rate
        + reference: L.D (string) - referencia para saber el lugar de una máquina, en este caso, L.D = Lavadora Derecha
        + serial_number: 339K1FF3E421 (string, required) - número de serie de una maquina
        + sort_index: 1 (number) - número para identificar una máquina en el lavadero
        + unit_price: 700 (number) - precio de fábrica de una máquina
        + uy_price: 1200 (number) - precio de venta en Uruguay


+ Response 201 (application/json)

+ Response 400 (application/json)

    + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_detail: Los siguientes campos son requeridos: name,serial_number (string) - código interno de resultado de la operación
