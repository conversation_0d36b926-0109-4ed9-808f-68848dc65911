FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para activacion de máquinas

## Building [/api/v1/activate]

### Independent request [GET /api/v1/activate/request{?param1}{&param2}{&param3}{&param4}{&param5}{&param6}{&param7}{&param8}{&param9}]

- Clientes: Mobile app, Totem, MercadoPago's QR, Whatsapp bot

- Propósito: Activar la máquina desde un cliente indendiente

+ Parameters
    + param1: 0xaa0a0aaa (string, required) - identificador de una tarjeta (card uuid)
    + param2: 1 (number, required) - identificador del edificio (building id)
    + param3: 111KWQW1E111 (string, required) identicador único de las máquinas proveniente de fabrica (machine serialNumber)
    + param4: true (boolean) - si se require el envio de un mensaje MQTT
    + param5: <EMAIL> (string) - email del usuario asociado a la cuenta de a la mobile app (user emailAddress), requirido si `channel` es igual a `APP`
    + param6: 1 (number) - identificador del grupo al que pertenece el usuario de la cuenta de a la mobile app
    + param7: 03df25c845d460b...f99cc3edcb63a85ea2ef2 (string) - token para enviar notificaciones a la mobile app
    + param8: APP (string) - origen desde el cual se genero la peticion. Valores aceptados: `WP` (Whatsapp bot), `APP` (Mobile app), `TOTEM` (Totem), `QR` (MercadoPago's QR)
    + param9: 1 (number) - identificador de la transaccion internal de LAVOMAT

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + Transaction_id: 1 (number) - identificador de la auditoria interna


### RPI request [GET /api/v1/activate/request/simple{?param1}{&param2}{&param3}]

- Clientes: RPI

- Propósito: Activar la máquina desde un cliente indendiente

+ Parameters
    + param1: 0xaa0a0aaa (string, required) - identificador de una tarjeta (card uuid)
    + param2: 1 (number, required) - identificador del edificio (building id)
    + param3: 111KWQW1E111 (string, required) identicador único de las máquinas proveniente de fabrica (machine serialNumber)

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + Transaction_id: 1 (number) - identificador de la auditoria interna
        + BLOCKED_TIME: 45 (number) - tiempo promedio de demora del ciclo de una máquina
        + result: 1 (string) - código interno de resultado de la operación. Valores desde `0` a `99`
