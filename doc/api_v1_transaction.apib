FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API BackOffice para cargar saldo manual

## Transactions [/api/v1/transaction]

### Manual Transaction [POST /api/v1/transaction/back-office]

+ Clientes: BackOffice

+ Propósito: Modificar el saldo de una tarjeta de forma manual en el BackOffice

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
            + cardBalance: 1 (number, required) - nuevo saldo de la tarjeta
            + cardId: 1 (number, required) - identificador de una tarjeta
            + details: REFUND (string) - causa predefinida de modificación del saldo. Valores esperados: 'REFUND', 'COURTESY', 'CASH', 'PREVIOUS_CARD', 'OTHER'
            + otherDetail: otra causa (string) - causa de modificación del saldo. Requerido cuando 'details' es igual a 'OTHERS'
            + isTransactionRequired: true (boolean) - determina si es necesario crear una transacción
            + isBillRequired: true (boolean) - determina si es necesario facturar
            + consumer: FINAL (string) - tipo de consumidor. Valores esperados: 'FINAL', 'BUSINESS'. Requerido cuando 'isBillRequired' es igual 'true'. Cuando es igual a 'BUSINESS', se requieren los datos de la empresa
            + recipientName: PEPITO SA (string) - razón social o nombre de la empresa. Requerido cuando 'consumer' es igual a 'BUSINESS'
            + recipientDocType: 2 (string) - tipo de documento. Valores esperados: '2', '3'. Requerido cuando 'consumer' es igual a 'BUSINESS'
            + recipientDoc: 49857849 (string) - número identificador del documento
            + isCreditNoteRequired: true (boolean) - determina si es necesario crear una nota de crédito
            + billId: 1 (number) - identificador de una factura. Requerido cuando 'isCreditNoteRequired' es igual a 'true'

+ Response 200 (application/json)

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 135 (number) - identificador del error
            + result_message: TRANSACTION_INVALID_AMOUNT (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 138 (number) - identificador del error
            + result_message: CREDIT_NOTE_INVALID_AMOUNT (string) - código interno de resultado de la operación

+ Response 409 (application/json)

     + Attributes(object)
            + result_code: 70 (number) - identificador del error
            + result_message: La tarjeta ingresada no es de tipo prepago (string) - código interno de resultado de la operación
