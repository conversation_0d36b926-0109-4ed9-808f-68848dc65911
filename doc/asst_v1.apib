FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - Integracion Assistant <> API

## Edificios [/asst/v1/buildings]
### Detalle del edificio [GET /asst/v1/buildings/{slug}]

- Clientes: Assistant

- Propósito: Obtener los detalles del edificio

+ Parameters
    + slug: prueba-prepago-8f401a50 (string) - identicador único

+ Response 200 (application/json)

    + Attributes(Building details)

+ Response 404 (application/json)

    + Attributes(Buidling not found)


## Tarjeta [/asst/v1/cards]
### Detalle del tarjeta [GET /asst/v1/cards/{uid}]

- Clientes: Assistant

- Propósito: Obtener los detalles de una tarjeta

+ Parameters
    + slug: d4eb58bc (string) - identicador único
    + level (number, optional) - The level of detail for the response. Can be any number between 0 and 6.


+ Response 200 (application/json)

    + Attributes(object)
        + uid: d4eb58bc (string, required) - The unique identifier of the card.
        + balance: 50 (number, required) - The current balance of the card.
        + activity (array, required) - The transaction history of the card.
            + (object)
                + amount: -61.6 (number, required) - The amount of the transaction.
                + type: Secado (string, required) - The type of transaction. It can also be `Lavado` and `Acreditación`
                + timestamp: 2017-09-03 11:34:31.0 (string, required) - The timestamp of the transaction.
        + state: ACTIVE (string, required) - The state of the card. See Part.PartState
        + contractType: PREPAID (string, required) - The type of contract for the card. See Card.ContractType
        + buildings (array, required) - The buildings where the card can be used.
            + (object)
                + rate (object, required) - The rate details for the building.
                    + name: Tarifa Redpagos Monte (string, required) - The name of the rate.
                    + priceCustomer: 77 (number, required) - The price for the customer.
                + name: Sunline (string, required) - The name of the building.

# Data Structures

## Building details
+ id: 1 (number) - identificador interno de un edificio
+ name: Edificio Uno (string) - nombre de un edificio

## Buidling not found (object)
+ result_code: 2 (number) - código interno de error
+ result_message: ex_building_not_found (string) - descripción corta
+ result_detail: No se encuentra el edificio solicitado (string) - descripción larga
