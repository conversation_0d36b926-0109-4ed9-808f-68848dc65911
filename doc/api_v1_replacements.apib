FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo de repuestos


### Create replacement [POST /api/v1/replacements]

+ Clientes: BackOffice

+ Propósito: Crear una repuestos

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + anual_consumption: 33 (number) - consumo anual de un repuesto
        + description: repuesto caro (string) - descripción de un repuesto
        + editing: true (string) - valor que indica si el objeto es editable o no. Valores esperados: true, false
        + english_description: expensive replacement (string) - descripción en ingles
        + minimum_stock: 22 (number) - número mínimo del repuesto en stock
        + name: UE1339AA7S (string, required) - nombre de una máquina
        + quantity: 11 (number) - cantidad de repuestos
        + request_point: 27 (string) - cálculo de cuando se debería pedir un stock
        + serial_number: 119K2AA2E124 (string, required) - número de serie de un repuesto
        + unit_price: 700 (number) - precio de fábrica de una máquina
        + uy_price: 1200 (number) - precio de venta en Uruguay


+ Response 201 (application/json)

+ Response 400 (application/json)

    + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_detail: Los siguientes campos son requeridos: name,serial_number (string) - código interno de resultado de la operación
