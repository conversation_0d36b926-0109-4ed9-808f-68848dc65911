FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo RPIs

## Machine [/api/v1/machines]

### Find Machine Use [GET /api/v1/machines/:machineId/machineUses/exist]

+ Clientes: RPI

+ Propósito: Val<PERSON>r por fecha si la máquina tiene un uso valido

+ Parameters
    + machineId: 42 (int) - identicador único de máquina

+ Response 200 (application/json)
    + Attributes(object)
            + exists: true (boolean) - devuelve true si existe un uso válido con ese timestamp

+ Response 404 (application/json)

    + Attributes(object)
        + result_message: MACHINE_NOT_FOUND

+ Response 400 (application/json)

    + Attributes(object)
        + result_message: MISSING_PARAMETERS
