FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - IoT API

- Clientes: RPI

### Cantidad de RPIs dentro de un edificio [GET /rpi/v1/buildings/{building_id}/rpi-count]

- Propósito: Desde la RPI se necesita obtener la cantidad de RPI que existen dentro de un mismo edificio.

+ Parameters
    + building_id: 1 (number) - id del edificio

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + count: 2 (number) - número de RPIs

+ Response 404 (application/json)

    + Attributes(Buidling not found)


### RPI request [GET /rpi/v1/activate/request/simple{?param1}{&param2}{&param3}]

- Propósito: Activar la máquina desde un cliente indendiente

+ Parameters
    + param1: 0xaa0a0aaa (string, required) - identificador de una tarjeta (card uuid)
    + param2: 1 (number, required) - identificador del edificio (building id)
    + param3: 111KWQW1E111 (string, required) identicador único de las máquinas proveniente de fabrica (machine serialNumber)

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + Transaction_id: 1 (number) - identificador de la auditoria interna
        + result: 1 (string) - código interno de resultado de la operación. Valores desde `0` a `99`

### Reserva recibida [POST /rpi/v1/booking/{booking_id}/received]

- Propósito: Desde la RPI se confirma que ha recibido la reserva y si la operación es posible de realizarse.

+ Parameters
    + booking_id: x1x11111-1x11-11x1-xx (string) - id de la reserva

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

             + bookable: true (boolean) - indica si la máquina fue reservada.
             + reason: La máquina ya se encontraba reservada (string) - razón por la cual la máquina no pude ser reservada.


+ Response 204 (application/json)
