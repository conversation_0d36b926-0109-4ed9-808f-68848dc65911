FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para manejo RPIs

## Machine [/api/v1/machine]

### Assign Machine RPI child [PUT /api/v1/machine/{parent_machine_id}/assign-rpi-child/{child_machine_id}]

+ Clientes: BackOffice

+ Propósito: Crear relación padre/hijo (RPI) entre máquinas

+ Parameters
    + parent_machine_id: 42 (int) - identicador único de máquina (part)
    + child_machine_id: 134 (int) - identicador único de máquina (part)

+ Response 200 (application/json)

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 9 (number) - identificador del error
        + result_message: MACHINE_NOT_FOUND

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 138 (number) - identificador del error
        + result_message: MACHINE_IS_PARENT


### Unassign Machine RPI child [PUT /api/v1/machine/{parent_machine_id}/unassign-rpi-child]

+ Clientes: BackOffice

+ Propósito: Eliminar relación padre/hijo (RPI) entre máquinas

+ Parameters
    + parent_machine_id: 42 (int) - identicador único de máquina (part)    

+ Response 200 (application/json)

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 9 (number) - identificador del error
        + result_message: MACHINE_NOT_FOUND

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 138 (number) - identificador del error
        + result_message: MACHINE_IS_NOT_PARENT
