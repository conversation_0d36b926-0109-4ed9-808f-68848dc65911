FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API hooks for PagosWeb

## Transactions [/api/v1/transaction/pw]

### Confirm Transaction [POST /api/v1/transaction/pw/confirm]

+ Clientes: PagosWeb

+ Proposito:

+ Request

    + Attributes(object)
        + codigoAutorizacion: 000042 (string, required) - código de autorización de la venta
        + mensaje: `Venta <PERSON>ob<PERSON>` (string, required) - mensaje de estado de la venta por parte de BambooPayment
        + numeroOrden: `abcd1111-1111-1a1a-11aa-3a3a3a42000` (string, required) - número de orden tipo GUID

+ Response 200 (application/json)

+ Response 400 (application/json)

     + Attributes(object)
         + result_code: 4 (number) - identificador del error
         + result_message: ex_missing_parameters (string) - código interno de resultado de la operación
         + result_detail: No paso chequeo de veracidad de PagosWeb (string) - mensaje más detallado

+ Response 500 (application/json)

     + Attributes(object)
         + result_code: 31 (number) - identificador del error
         + result_message: ex_internal_server_error (string) - código interno de resultado de la operación


### Notify Transaction [POST /api/v1/transaction/pw/notify]

+ Clientes: PagosWeb

+ Proposito: Notificar el estado de la transacción

+ Request

    + Attributes(object)
        + codigoAutorizacion: 0 (string, required) - código de autorización de la venta
        + mensaje: `Transaccion Aprobada` (string, required) - mensaje de estado de la venta por parte de BambooPayment. Valores posibles: "Venta Aprobada", "Transaccion Aprobada"
        + numeroOrden: `abcd1111-1111-1a1a-11aa-3a3a3a42000` (string, required) - número de orden tipo GUID

+ Response 200 (application/json)


+ Response 400 (application/json)

     + Attributes(object)
         + result_code: 4 (number) - identificador del error
         + result_message: ex_missing_parameters (string) - código interno de resultado de la operación
         + result_detail: No paso chequeo de veracidad de PagosWeb (string) - mensaje más detallado


+ Response 500 (application/json)

     + Attributes(object)
         + result_code: 31 (number) - identificador del error
         + result_message: ex_internal_server_error (string) - código interno de resultado de la operación
