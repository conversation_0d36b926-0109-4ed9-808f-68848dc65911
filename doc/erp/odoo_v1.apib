FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# ODOO - API para manejo de máquinas

### Create machine [POST /erp/odoo/v1/machines]

+ Clientes: Odoo

+ Propósito: Crear una máquina en el sistema a partir de los datos provistos por Odoo.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111
            X-LM-SOURCE: ODOO

    + Attributes(object)

            + odooId: 42 (number, required) - Identificador único de id externo
            + name: RV1329AN7S (string, required) - Nombre identificador de la máquina.
            + model: LT4211 (string) - Modelo de la máquina.
            + description: Máquina industrial de carga frontal (string) - Descripción general de la máquina.
            + serialNumber: 339K1FF3E421 (string, required) - Número de serie único de la máquina.
            + unitPrice: 700 (number) - Precio de fábrica o de adquisición de la máquina.
            + uyPrice: 1200 (number) - Precio de venta en Uruguay.
            + averageUseTime: 45 (number) - Tiempo promedio de uso por ciclo (en minutos).
            + machineType: WASHER (string, required) - Tipo de máquina. Valores esperados: `WASHER`, `DRYER`.
            + reference: L.D (string) - Referencia o ubicación asignada de la máquina, por ejemplo: "L.D" = Lavadora Derecha.
            + capacity: 10 (number) - Capacidad de la máquina en kilogramos.
            + expectedUses: 15000 (number) - Usos estimados durante la vida útil de la máquina.
            + state: SOLD (string, required) - Estado de la máquina. Valores esperados: `NEW`, `ON_SERVICE`, `ON_MAINTENANCE`, `ACTIVE`, `INACTIVE`, `SUSPENDED`, `LOST`, `DISCONNECTED`, `DAMAGED`, `PRE_BLOCKED`, `DEPRECIATED`, `MAINTENANCE_IN_SITU`, `MAINTENANCE_IN_WORKSHOP`, `SOLD`, `REMOVED_FROM_SALE`, `REFURBISHED_FOR_INSTALLATION`.

+ Response 201 (application/json)

+ Response 400 (application/json)
    + Attributes(object)
        + result_code: 0 (number) - identificador del error
        + result_message: MACHINE_ALREADY_EXISTS (string) - código interno de resultado de la operación
        + result_detail: "" (string) - detalle del mensaje

    + Body
        {
        	"result_code": 0,
        	"result_message": "MACHINE_ALREADY_EXISTS",
        	"result_detail": ""
        }

+ Response 401 (application/json)

    + Attributes(object)
        + result_code: 401 (number) - identificador del error
        + result_message: auth_token_not_found (string) - código interno de resultado de la operación

+ Response 401 (application/json)

    + Attributes(object)
        + result_code: 401 (number) - identificador del error
        + result_message: auth_token_invalid (string) - código interno de resultado de la operación

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 4 (number) - identificador del error
        + result_message: ex_missing_parameters (string) - código interno de resultado de la operación
        + result_detail": The following parameters are required: machineType

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 146 (number) - identificador del error
        + result_message: UNSUPPORTED_VALUE (string) - código interno de resultado de la operación

+ Response 404 (application/json)

    + Attributes (object)
        + result_code: 0 (number) - identificador del error
        + result_message: EXTERNAL_ODOO_ID_NOT_FOUND_EXCEPTION (string) - código interno de resultado de la operación

+ Response 404 (application/json)

    + Attributes (object)
        + result_code: 0 (number) - identificador del error
        + result_message: MACHINE_MODEL_NOT_FOUND (string) - código interno de resultado de la operación
