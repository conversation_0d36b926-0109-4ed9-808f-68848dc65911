FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - Integracion SimpleSolutions - API

## Auth [/simple-solutions/v1]
### Identificación de usuario [POST /simple-solutions/v1/signin{?api_key}]

- Clientes: SimpleSolutions API

- Propósito: Identificación del usuario que va a acceder al sistema de LAVOMAT

+ Parameters
    + api_key: 00xx000x-0000-000x-x000-x00000x0x000 (string) - identificador del cliente

+ Request (application/json)
    + Headers

            Accept-Language: es

    + Attributes (object)
        + building: edificio01 (string, required) - identicador de edificios en el sistema de SimpleSolutions
        + unit: `101-A` (string, required) - identicador de la unidad funcional dentro de un edificio. Compuesto por el número de unidad y la torre, separados por el caracter '-'.

+ Response 200 (application/json)

    Respuesta satifactoria que indica a donde debe ser redirigido el usuario para acceder a la información de LAVOMAT.

     + Attributes(object)
        + redirect_url: https://assistant.lavomat.com.uy/ss/0x0x00xx0x0000x00000x00x (string) - URL a donde redireccionar los usuarios para que puedan acceder al contenido de LAVOMAT

+ Response 400 (application/json)

    Cuando alguno de los datos requeridos no se han recibido.

    + Attributes(object)
        + result_code: 4 (number) - identificador del error
        + result_message: Los siguientes campos son requeridos: building, unit (string) - mensaje de error

+ Response 401 (application/json)

    Cuando los datos suministrados no permiten identificar le usuario que intenta acceder a los datos.

    + Attributes(object)
        + result_message: Credenciales incorrectas (string) - mensaje de error
