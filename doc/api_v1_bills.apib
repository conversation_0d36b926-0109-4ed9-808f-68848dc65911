FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para gestión de facturas

## Bill [/api/v1/bills]

### Independent request [POST /api/v1/bills/{billId}/cancel]

+ Clientes: BackOffice

+ Propósito: Anular una factura, parcial o totalmente

+ Parameters
    + billId: 1 (number, required) - identificador de una factura

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + type: partial (string, required) - tipo de anulación a ejectuar. Valores validos: total, partial
        + reason: devolución por defecto de máquina (string) - razón por la cual se realiza la aulación
        + discreditedItems (array) - Lista de items a ser cancelados. Esta lista debe contener items cuando la anulación no se realiza en base a usos.
            + item (object)
                + itemName: Tarjeta Adicional (string, required) - Descripción del item
                + unit: Tarj (string, required) - unidad de mendida del item involucrado
                + unitPrice: 245.5 (number, required) - valor unitario del item, sin incluir impuestos
                + amount: 10 (number, required) - cantidad total de items que se encuentran en la factura
                + discreditedAmount: 5 (number, required) - cantidad total de items qeu se desean remover de la factura


+ Response 200 (application/json)

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 121 (number) - identificador del error
            + result_message: BILL_NOT_FOUND (string) - código interno de resultado de la operación

### Create bill [POST /api/v1/bills]

+ Clientes: BackOffice

+ Propósito: Crear una factura

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + items (array) - lista de objetos y atributos correspondientes a una factura
            + billToType: UNIT (string) - Valores esperados: 'NEW', 'UNIT', 'BUILDING'
            + building: 42 (number) - número identificador de un edificio
            + currency: UYU (string, required) - tipo de moneda. Valores esperados: 'UYU', 'USD'
            + items (array) - lista de items de una factura
                + item (object)
                    + amount: 1 (number, required) - cantidad total de items que se encuentran en la factura
                    + name:  Carga de Saldo (string, required) - descripción del item
                    + measureUnit: Sld (string, required) - unidad de medida del item. Valores esperados: 'Sld', 'Tarj', 'Usos', 'Lava', 'Seca', 'Ltrs', 'Repo', 'Mano'
                    + unitPrice: 427.9 (number, required) - valor unitario del item, sin incluir impuestos
            + paymentMethod: 2 (number, required) - número de método de pago
            + timestamp: `2023-02-27` (string) - fecha de creación de la factura
            + periodStart: `2023-01-01` (string) - fecha de inicio de período
            + periodEnd: `2023-01-31` (string) - fecha de fin de período
            + recipient (object) - datos requeridos cuando 'billToType' es igual a 'MISC'
                + address: Recto a la derecha 421 (string, required) - dirección donde recide el cliente
                + city: Rivera (string, required) - ciudad donde vive el cliente
                + country: Uruguay (string, required) - país donde recide el cliente
                + countryCode: UY (string, required) - código de país
                + department: Rivera (string, required) - departamento donde recide el cliente
                + doc: 12345678 (number, required) - cedula de identidad del cliente
                + docType: 3 (number, required) - número de tipo de documento. Valores esperados: 1, 2, 3, 4, 5, 6, 7
                + name: Nutria (string, required) - nombre del cliente
            + unit: 24 (number) - número identificador de la base de datos del registro de una unidad


+ Response 200 (application/json)

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_message: Parámetros inválidos (string) - código interno de resultado de la operación

+ Response 406 (application/json)

     + Attributes(object)
            + result_code: 156 (number) - identificador del error
            + result_message: Parámetros inválidos (string) - código interno de resultado de la operación

### Create bill [GET /api/v1/bills/item/measures]

+ Clientes: BackOffice

+ Propósito: Obtener una lista de las unidades de medida de los items de una factura

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    Attributes (array[string])
            + message: [Sld, Tarj, Usos, Lava, Seca, Ltrs, Repo, Mano]


