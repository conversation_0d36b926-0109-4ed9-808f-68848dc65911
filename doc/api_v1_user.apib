FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para consultas sobre usuarios

## User [/api/v1/users]

### Búsqueda de usuarios por nombre, email o rol [GET /api/v1/users{?keyword}{&role}]

- Clientes: BackOffice

- Propósito: Obtener una lista de usuarios en el BackOffice

+ Parameters
    + keyword: facu (string) - nombre, apellido, mail o parte de uno de ellos de un usuario.
    + role: MASTER (string) - rol de un usuario. Valores esperados: 'USER', 'TOTEM', 'MASTER', 'BUILDING_ADM', 'TECHNICIAN', 'DEBT_COLLECTOR'

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + users: (array) - lista de usuarios
            + user: (object)
                + id: 758 (number) - número identificador de un usuario
                + name: <PERSON><PERSON><PERSON><PERSON> (string) - nombre del usuario
                + lastname: <PERSON><PERSON><PERSON> (string) - a<PERSON><PERSON><PERSON> del usuario
                + email: <EMAIL> (string) - email único de un usuario
                + role: MASTER (string) - rol de un usuario
