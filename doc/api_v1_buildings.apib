FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para consultas sobre edificios

## Building [/api/v1/buildings]

### Cantidad de RPIs [GET /api/v1/buildings/{building_id}/rpi-count]

- Clientes: RPI

- Propósito: Desde la RPI se necesita obtener la cantidad de RPI que existen dentro de un mismo edificio.

+ Parameters
    + building_id: 1 (number) - id del edificio

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + count: 2 (number) - número de RPIs

+ Response 404 (application/json)

    + Attributes(Buidling not found)


### Modificación de atributos de un edificio [PUT /api/v1/buildings/{bid}]

+ Clientes: BackOffice

+ Propósito: Modificar atributos de un edificio

+ Parameters
    + bid: 1 (int) - identificador único de edificio

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
            + name: Edificio Uno (string, required) - nombre de edificio
            + address: av Brasil (string) - dirección del edificio
            + country: Uruguay (string, required) - país en que se encuentra ubicado el edificio
            + city: Rivera (string) - ciudad en que se encuentra el edificio
            + state: Rivera (string) - departamento en que se ubica el edificio
            + rut: 101010101101 (string) - RUT identificador de la empresa
            + contact: 099423311 (string) - número de contacto
            + invoicingType: BUILDING (string) - Valores esperados: 'BUILDING', 'PER_UNIT', 'HYBRID'
            + contractType: PREPAID (string) - tipo de contrato de tarjeta asociada a un edificio. Valores esperados: 'POSTPAID', 'PREPAID', 'MIXED'
            + buildingType: LAUNDROMAT (string) - tipo de edificio. Valores esperados: 'BUILDING', 'HOTEL', 'LAUNDROMAT'
            + invoicingMethod: CASH (string) - método de facturación. Valores esperados: 'CREDIT', 'CARD'
            + administration (object) - número identificador de una administración
                + administration (object)
                    + address: Luis Alberto de Herrera 1022 ofic 156 (string) - dirección de la oficina de la administración
                    + closureDay: 1 (number) - fecha de cierre
                    + collectionDate: `2021-04-25 18:15:34.0` (string) - fecha de cobro
                    + collector: Mariela (string) - nombre del cobrador
                    + contact: <EMAIL> (string) - email de contacto
                    + id: 2 (number) - número identificador de una administración
                    + name: Estudio Nuevo (string) - nombre del administrador
            + rate: 1 (number) - número identificador que indica el precio del uso de una máquina
            + latitude: `-56.56` (number) - latitud geográfica de edifico
            + longitude: `-56.56` (number) - longitud geográfica de edifico
            + mp100: 100 (number) - cantidad de usos restantes para el mentenimiento mp100
            + mp500: 400 (number) - cantidad de usos restantes para el mentenimiento mp500
            + mp1200: 1000 (number) - cantidad de usos restantes para el mentenimiento mp1200
            + prepaidRechargeableUses: 8 (number) - usos acreditados cuando se hace el cierre del edificio mensualmente a tarjetas de edificios específicos, que se pagan luego de usarlos
            + paymentMethod: WEB_AND_REDPAGOS (string) - método de pago de una regarga a una tarjeta. Valores esperados: 'NOT_SPECIFIED', 'WEB_MP_ONLY', 'WEB_AND_REDPAGOS'

+ Response 200 (application/json)

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 147 (number) - identificador del error
            + result_message: UNSUPPORTED_VALUE (string) - código interno de resultado de la operación


### Search machines with differents rates by building id [GET /api/v1/buildings/{buildingId}/machinesWithDifferentsRates]

+ Clientes: BackOffice

+ Proposito: Para listar las máquinas con distintos precios

+ Parameters
    + buildingId: 1 (number, required) - identificador de un edificio

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + machines: (array) - lista de máquinas
            + machine: (object)
                + id: 1 (number) - identificador de una máquina
                + name: LAVA LG G (string) - nombre de la máquina
                + priceMachine: 64 (number) - precio de uso
                + description: LAVARROPAS LG GRIS - PISO 11 (string) - descripción de una maáquina
                + serial_number: 712KWFN6T723 (string) - identificador único de las máquinas proveniente de fábrica
                + state: NEW (string) - estado que se encuentra una máquina
                + sort_index: 1 (number) - número que identifica el lugar de una máquina en un edificio
                + type: models.Building (string) - carpeta donde se encuentra la clase del objeto

            
# Data Structures

## Buidling not found (object)
+ result_code: 2 (number) - internal code error
+ result_message: ex_building_not_found (string) - short description
+ result_detail: No se encuentra el edificio solicitado (string) - long description


###  To disable the building closure by building id [PUT /api/v1/buildings/:id/disable-closure]

+ Clientes: BackOffice

+ Proposito: Para deshabilitar el cierre del edificio

+ Parameters
    + buildingId: 1 (number, required) - identificador de un edificio

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)



###  To enable the building closure by building id [PUT /api/v1/buildings/:id/enable-closure]

+ Clientes: BackOffice

+ Proposito: Para habilitar el cierre del edificio

+ Parameters
    + buildingId: 1 (number, required) - identificador de un edificio

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

