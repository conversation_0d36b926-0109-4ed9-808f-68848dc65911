<?xml version="1.0" encoding="UTF-8"?>
<ivy-module version="2.0" xmlns:e="http://ant.apache.org/ivy/extra">
	<info organisation="play4jpa" module="play4jpa_2.11" revision="1.0-SNAPSHOT" status="integration" publication="20150929130442">
		<description>
		play4jpa
		</description>
	</info>
	<configurations>
		<conf name="compile" visibility="public" description=""/>
		<conf name="runtime" visibility="public" description="" extends="compile"/>
		<conf name="test" visibility="public" description="" extends="runtime"/>
		<conf name="provided" visibility="public" description=""/>
		<conf name="optional" visibility="public" description=""/>
		
		
		
		
		<conf name="sources" visibility="public" description=""/>
		
		<conf name="pom" visibility="public" description=""/>
		
	</configurations>
	<publications>
		<artifact name="play4jpa_2.11" type="pom" ext="pom" conf="pom"/>
		<artifact name="play4jpa_2.11" type="jar" ext="jar" conf="compile"/>
		<artifact name="play4jpa_2.11" type="src" ext="jar" conf="sources" e:classifier="sources"/>
	</publications>
	<dependencies>
		
		<dependency org="org.scala-lang" name="scala-library" rev="2.11.6" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="twirl-api_2.11" rev="1.1.1" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="play-server_2.11" rev="2.4.3" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="play-test_2.11" rev="2.4.3" conf="test->default(compile)"/>
		
		<dependency org="com.typesafe.play" name="play-java_2.11" rev="2.4.3" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="play-netty-server_2.11" rev="2.4.3" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="play-java-jdbc_2.11" rev="2.4.3" conf="compile->default(compile)"/>
		<dependency org="com.typesafe.play" name="play-java-jpa_2.11" rev="2.4.3" conf="compile->default(compile)"/>
		<dependency org="org.hibernate" name="hibernate-entitymanager" rev="4.2.7.Final" conf="compile->default(compile)"/>
	</dependencies>
</ivy-module>
