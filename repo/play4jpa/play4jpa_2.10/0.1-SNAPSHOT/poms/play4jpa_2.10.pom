<?xml version='1.0' encoding='UTF-8'?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>play4jpa</groupId>
    <artifactId>play4jpa_2.10</artifactId>
    <packaging>jar</packaging>
    <description>play4jpa</description>
    <version>0.1-SNAPSHOT</version>
    <name>play4jpa</name>
    <organization>
        <name>play4jpa</name>
    </organization>
    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>2.10.2</version>
        </dependency>
        <dependency>
            <groupId>play4jpa-fixy</groupId>
            <artifactId>play4jpa-fixy_2.10</artifactId>
            <version>0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play_2.10</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-test_2.10</artifactId>
            <version>2.2.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-java_2.10</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-java-jdbc_2.10</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.play</groupId>
            <artifactId>play-java-jpa_2.10</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>4.2.7.Final</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>typesafereleases</id>
            <name>typesafe-releases</name>
            <url>https://repo.typesafe.com/typesafe/releases/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>TypesafeReleasesRepository</id>
            <name>Typesafe Releases Repository</name>
            <url>http://repo.typesafe.com/typesafe/releases/</url>
            <layout>default</layout>
        </repository>
    </repositories>
</project>