# lm-app

[![Compilation check](https://github.com/LAVOMAT/lm-app/actions/workflows/compilation-check.yml/badge.svg)](https://github.com/LAVOMAT/lm-app/actions/workflows/compilation-check.yml) [![Deploy documentation](https://github.com/LAVOMAT/lm-app/actions/workflows/doc-deploy.yml/badge.svg)](https://github.com/LAVOMAT/lm-app/actions/workflows/doc-deploy.yml)

This app was built using Play Framework 2.4

## TOC

- [API Documentation](#documentation)
- [Release API](#release-api)
- [Troubleshooting](#troubleshooting)
    * [Ver logs](#ver-logs)
    * [Borrar builds](#borrar-builds)
- [Add new MP machine](#add-new-mp-machine)
- [PASOS PARA INSTALACION](#PASOS-PARA-INSTALACION)
- [DB Modifiactions](#DB-Modifiactions)

## Documentation

https://lavomat-doc.herokuapp.com/

### how to get credentials

1. login to heroku site using `<EMAIL>`
1. run `heroku login` and complete cli auth
1. run `heroku authorizations:create` and copy the `token` value
1. go to [project secrets](/LAVOMAT/lm-app/settings/secrets/actions)
1. set `DOC_HEROKU_EMAIL` with `<EMAIL>`
1. set `DOC_HEROKU_API_KEY` with the `token` value
1. set `DOC_HEROKU_APP_NAME` with the name of the heroku app

## Release API

El proceso de release una nueva versión consiste en: BUILD + DEPLOY + RESTART

### 1. Conectarse a ECS

```sh
    sudo ssh -i lavomat.pem ec2-user@<ec2-ip>
```

 EC2       | IP            | Enviroment 
-----------|---------------|------------
 lavomat-1 | ************* | PROD       
 lavomat-2 | ************  | PROD       

### 2. Cambiar al usuario `lavomat`

```sh
    sudo su - lavomat
```

### 3. Build

1. Ir a la carpeta que contiene el script de build

```sh
    cd /lavomat/scripts
```

2. Ejecutar el archivo `build.sh` seguido de la branch del repositorio en la que se quiere basar, en caso de omisión
   es `main`. **

__build tag__

```sh
./build.sh <tag-version>
```

__i.e. build v1.1.1 tag__

```sh
./build.sh v1.1.1 
```

** En caso de no ser posible usar el usuario y contraseña para obtener los cambios, se puede obtener un Github token el
valor de la contraseña.

1. Ir a https://github.com/settings/tokens
2. Click en el boton `Generate new token`.
3. Seleccionar la siguente configuración:
    * Note: `one-time-token`
    * Expiration: custom - one day
    * Scopes:
        * :ballot_box_with_check: repo

#### 3.1. Instalar dependencias bower (opcional)

Si se agregaron nuevas dependencias de bower

1. Ir a

```sh
cd /lavomat/git/lm_app/public/
```

2. Ejecutar

```sh
bower install
```

3. Volver al correr el build (Paso 3)

### 4. Deploy

1. Ir a la carpeta que contiene el script de build (misma carpeta que la anterior)

```sh
    cd /lavomat/scripts
```

2. Ejecutar el archivo `deploy.sh` seguido por el ambiente que se desean cargar las configuraciones.

__deploy to prod env__

```sh
    ./deploy.sh prod v1.1.0
```

__deploy to sandbox env__

```sh
    ./deploy.sh sandbox v1.1.1
```

### 5. Desloguearse del usuario `lavomat` y volver al usuario `ec2-user`

```sh
    exit
```

### 6. Reiniciar el servicio

1. Ir a la carpeta que contiene el script para detener el servicio

```sh
    cd /home/<USER>/scripts/lavomat-<instancia>
```

2. Ejecutar el archivo `APP.sh` seguido la flag `stop`

```sh
    ./APP.sh stop
```

3. Ejecutar el archivo `APP.sh` seguido la flag `start`

```sh
    ./APP.sh start
```

### 7. Finalizado!

Comprobar que se han levantado las instancias correctamente.

```sh
    tail -f /var/log/lavomat/lmat.log
```

[see more](#ver-logs)

### 8. Ejecutar migraciones

Ejectuar las migraciones incluidas en el PR

## Troubleshooting

### Ver logs

1. Ir a la carpeta que contiene el archivo `lmat.log`, donde se encuentran los diferentes registros.

```sh
    cd /var/log/lavomat
```

#### Visualizacion del log

- Ver las últimas 50 líneas del archivo: `$ tail -n 50 lmat.log`
- Ver archivo en tiempo real: `$ tail -f lmat.log`. Para salir `ctr + c`
- Buscar: `$ grep "Data:" lmat.log | tail 10`
- Buscar por dos palabras: `$ grep "2019-02-02\|ERROR" lmat.log `
- Buscar por dos palabras: `$ grep "2019-02-02\|ERROR" lmat.log ` o `grep "2019-02-02" lmat.log | grep "ERROR"`
- Buscar entre fechas: `egrep "2019-03-18 16:00:00|2019-03-18 17:15:00" lmat.log`

NOTE: Se recomienda el uso de [Papertrail](https://papertrailapp.com/) para los ambientes de producción.

### Borrar builds

En caso de visualizar un error por el cual no se puedan generar nuevos builds debido a que no se cuenta con el espacio
suficiente.

1. Cambiar al usuario `lavomat`

```sh
    sudo su - lavomat
```

2. Ir a la carpeta que contiene los build anteriores

```sh
    cd /lavomat/builds
```

3. Listar builds y detalles

```sh
    stat -c "%y %s %n" *
```

4. Borrar los builds deseados (verificar el pattern utilizado a modo de ejemplo)

```sh
    rm -f -r lm_app-0.*
```

5. Hacer lo mismo en la ruta `/lavomat/git/lm_app/target/scala-2.11/`

## Add new MP machine

Correspondencia de términos:
Lavomat | Mercado Pago
--- | ---
Lavadero / Local / Building | Store
Machine | Pos

Para agregar una nueva maquina a nivel de Mercado Pago es necesario:

1. Realizar la siguiente request:

```
POST /api/v1/pos

Body:

{
    "building_id": "<Building.id>", // Local de Micenas Mall: 1069
    "machine_serial": "<Machine.serialNumber>",
    "details": "Maquina <numero identificativo dentro del local>"
}

```

Para generar los QRs correspondiente a cada máquina dirigirse
a [QR Generator](https://bitbucket.org/lavomatserver/qr-generator).

## PASOS PARA INSTALACION

### Script

- ejecutar el archivo `setup.sh`
- para levantar el proyecto ejecutar `./run.sh`

### Manual

Pre-requisitos

- OpenJDK (cualquier versión es retrocompatible)
    - Configurar $JAVA_HOME
- Node 8.11.2 (Para manejar varias versiones instalar antes NVM)
- MySQL server (para versiones nuevas modificar el la version del conector java a 5.1.44) [./build.sbt]
- [optional] jenv - version manager

INSTALACIÓN

1. Clonar repo: git clone https://bitbucket.org/lavomatserver/lm_app.git
1. Copiar `conf/application.conf.example` a  `conf/application.conf`
1. Extraer 'play4jpa.zip' en su propria carpeta en el root
1. Ir a ./play4jpa/dist y ejecutar en una terminal (demora)
    - ../../activator publishLocal
1. Ir al root del proyecto y ejecutar en una terminal
    - ./activator run
1. Abrir un navegador en http://localhost:9000 para probar si esta corriendo
1. npm install bower -g if needed
1. npm install grunt -g if needed
1. Ir al root del proyecto y ejecutar en una terminal
    - npm install
1. Ir a la carpeta ./public/ y ejecutar en una terminal
    - bower install
1. Ir al root del proyecto y ejecutar en una terminal
    - grunt dist
1. `brew install redis`

Para correrlo en Eclipse

1) ./activator eclipse
2) ./activator clean
3) ./activator compile

Para poder debuggear desde Eclipse ir a Run > Debug Configurations > Remote Java App - create
one using the project root in port 9999
Luego: ./activator -jvm-debug 9999 run

### Run tests

`./activator test`

#### Also check

* [additional commands](https://www.playframework.com/documentation/2.4.x/JavaTest#Overview)
* [How to create new test](./test/README.md)

## DB Modifiactions

Cuando sea necesario registrar modificaciones a la base de datos, así como las actualizaciones de datos o cualquier
script que sea necesario ejecutar antes o después de un deploy de una nueva versión, deberán ser registrados siguiendo
el siguiente formato.

> Tomar en cuenta que algunas de las modificaciones pueden ser realizadas por el ORM de forma automática, esos cambios
> no será necesario registrarlos.

- File Location: [db/migrations](db/migrations)
- File name: `<today date | 'YYYYMMdd'>_<table>_<action>_<column>_<order|optional>.sql`
- Template:

```
-- BEFORE DEPLOY
-- <What does the change do? | replace it with your comment>

-- AFTER DEPLOY: <What did the deploy modify? | replace it with your comment>

-- <What does the change do? | replace it with your comment>

```

### Example

`20201126_parts_update_capacity.sql`

```
-- BEFORE DEPLOY

-- AFTER DEPLOY: Add capacity column to parts

--  Set default capacity column value for parts
UPDATE lavomat.part SET capacity='0' WHERE id > 0;

-- Set default capacity column value only for machines
UPDATE lavomat.part SET capacity='15' WHERE id > 0 AND from_class = 'machine';

```
